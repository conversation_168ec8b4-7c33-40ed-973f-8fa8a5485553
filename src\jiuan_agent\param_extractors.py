"""
久安大模型参数提取模块 - 包含从用户输入提取API参数的相关函数
"""

import re
import logging
from typing import Dict, Any

# 创建logger
logger = logging.getLogger("jiuan_agent")

def extract_event_params(user_input: str) -> Dict[str, Any]:
    """
    从用户输入中提取
      1. 事件名称   -> event_name
      2. 距离(公里) -> distance
      3. 资源类型   -> resource_type  (可选, 如队伍/仓库/专家...)
    """
    event_name = ""
    distance = 0
    resource_type = "" 
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes =  ["请你告诉我", "请告诉我", "请查询", "查询", "请问", "告诉我", 
               "帮我查一下", "帮我看看", "帮忙查询", "帮忙看看", "麻烦查询", 
               "麻烦告诉我", "我想知道", "能告诉我", "能查询", "能帮我查询",
               "想了解", "查一下", "看一下", "帮我问一下", "帮我确认一下",
               "请帮我查看", "请帮我确认", "请问一下", "麻烦问一下", 
               "麻烦帮我查", "能否告诉我", "能否查询", "能否帮我看", 
               "想请教", "请协助查询", "协助查看", "帮忙问一下", "我想知道", "查找","开启", "关闭",
                "启动", "停止", "打开", "展示", "显示", "查看","获取", "调取", "调出", "切换到", "切换", "进入", "退出"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 首先处理"请告诉我洪涝灾害事件"这样的格式
    if "请告诉我" in user_input and "事件" in user_input:
        # 尝试直接提取"XX事件"
        event_matches = re.findall(r'([\w\s]+?事件)', user_input)
        if event_matches:
            # 排除带有"请告诉我"的匹配
            for match in event_matches:
                if "请告诉我" not in match:
                    event_name = match
    
    # 各种匹配模式
    patterns = [
        # 格式1：XX事件[X公里]范围内
        r'([\w\s]+?事件)(?:[^\d]*?)(\d+)(?:公里|km|千米)(?:(?:范围|半径)内|以内|附近)',
        # 格式2：XX事件附近X公里
        r'([\w\s]+?事件)(?:附近|周边|周围)(?:[^\d]*?)(\d+)(?:公里|km|千米)',
        # 格式3：XX事件的X公里范围
        r'([\w\s]+?事件)的(?:[^\d]*?)(\d+)(?:公里|km|千米)(?:范围|半径|内|附近|周边)',
        # 模式4: 事件+周边/附近+资源类型 (如: 杭州市暴雨事件周边视频)
        r'([\w\s]+?事件)(?:周边|附近|周围|范围内)(?:的)?(?:队伍|仓库|专家|避难场所|医疗卫生|防护目标|企业信息|物资|装备|医院|危险源|视频|监控|监控点位|摄像头|摄像机|camera|监控点|点位|探头)',
        # 模式5: 事件+的+资源类型 (如: 杭州市暴雨事件的视频)
        r'([\w\s]+?事件)的(?:队伍|仓库|专家|避难场所|医疗卫生|防护目标|企业信息|物资|装备|医院|危险源|视频|监控|监控点位|摄像头|摄像机|camera|监控点|点位|探头)',
        # 模式6: 通用事件匹配 (如: 杭州市暴雨事件) - 放在最后作为兜底
        r'([\w\s]+?事件)(?=\s|$|，|,|的|周边|附近|周围)'
    ]
    
    # 依次尝试各种模式
    for pattern in patterns:
        matches = re.search(pattern, cleaned_input)
        if matches and len(matches.groups()) >= 1:
            if not event_name:  # 如果之前没提取到，则提取
                event_name = matches.group(1).strip()
            if len(matches.groups()) >= 2 and matches.group(2):  # 如果包含距离信息
                try:
                    distance = int(matches.group(2))
                except ValueError:
                    pass
            break  # 找到一种匹配就退出循环
    
    # 如果没有通过模式匹配提取到事件，但包含"事件"关键词，尝试简单提取
    if not event_name and "事件" in cleaned_input:
        # 提取"事件"前后的几个词作为事件名称
        words = cleaned_input.split()
        for i, word in enumerate(words):
            if "事件" in word:
                # 向前最多取3个词，加上当前词
                start = max(0, i - 3)
                event_name = "".join(words[start:i+1])
                break
    
    # 如果仍然没有提取到，尝试匹配"XX灾害"类型的名称
    if not event_name:
        disaster_matches = re.findall(r'([\w\s]+?灾害)', cleaned_input)
        if disaster_matches:
            event_name = disaster_matches[0]

    m_res = re.search(r'(?:的)?\s*(队伍|仓库|专家|避难场所|医疗卫生|防护目标|企业信息|物资|装备|医院)(?:资源|信息|列表|情况)?\s*$', cleaned_input)
    if m_res:
        resource_type = m_res.group(1)

    # 清理提取到的事件名称中的标点符号
    if event_name:
        # 移除常见中英文标点符号
        event_name = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', event_name).strip()
        # 移除"发生了"、"发生"、"爆发了"等前缀
        event_name = re.sub(r'^(发生了|发生|爆发了|爆发|出现了|出现|有了|有|最近的)', '', event_name).strip()
    
    # 如果没有提取到距离，设置默认值
    if distance <= 0:
        distance = 5  # 默认5公里
    
    # 记录提取结果
    logger.info(f"提取到事件参数 - 事件名称: {event_name}, 距离: {distance}")
    result = {
        "event_name": event_name,
        "distance": distance
    }
    if resource_type:               # 🔸 只在确实提取到时才带回
        result["resource_type"] = resource_type

    return result

def extract_camera_params(user_input: str) -> dict:
    """
    从用户输入中提取摄像头ID参数
    """
    camera_id = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请你", "请", "帮我", "我想", "我要", "我希望", "能否", "能不能", 
                "是否可以", "麻烦", "麻烦你", "帮忙", "想要", "需要", "我需要", "希望能"]
    for prefix in prefixes:
        if cleaned_input.lower().startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):].strip()
            break
        
    # 各种匹配模式，使用命名捕获组提高可读性
    patterns = [
        # 特殊格式：数字-Camera 数字
        r'(?i)(?P<id>\d+\s*-\s*[Cc]amera\s+\d+)',
        
        # 常见操作+摄像头格式
        r'(?:预览|查看|打开|开启)(?:一下|下)?(?:摄像头|监控|摄像机|监控点|点位|探头|[Cc]amera)?(?:画面)?[:：\s]*(?P<id>[a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',
        
        # 摄像头+ID格式
        r'(?:摄像头|监控|摄像机|监控点|点位|探头)(?:[:：\s]*)(?P<id>[a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',
        
        # ID+摄像头格式
        r'(?P<id>[a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)(?:的)?(?:摄像头|监控|摄像机|点位|探头)(?:的)?(?:画面|内容|视频|录像|预览|监控)?',
        
        # Camera+ID格式
        r'[Cc]amera\s*(?P<id>[a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',
    ]
    
    # 遍历所有模式尝试匹配
    for pattern in patterns:
        match = re.search(pattern, cleaned_input)
        if match and 'id' in match.groupdict():
            camera_id = match.group('id').strip()
            break
    
    # 清理提取到的摄像头ID中的标点符号，但保留连字符"-"
    if camera_id:
        camera_id = re.sub(r'[,.。，、；;：:！!?？""\'\"()（）【】\[\]{}\s]', '', camera_id).strip()
    
    return {
        "camera_id": camera_id
    }

# 完善资源参数提取函数
def extract_resource_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取资源名称参数"""
    resource_name = ""
    resource_type = "default"  # 默认类型
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 各种匹配模式
    resource_patterns = [
        r'一张图查看(?:并定位)?\s*(.+?)(?:$|\s|，|。)',  # 匹配"一张图查看XX"
        r'一张图定位\s*(.+?)(?:$|\s|，|。)',  # 匹配"一张图定位XX"
        r'定位\s*(.+?)(?:$|\s|，|。)',  # 匹配"定位XX"
        r'查看\s*(.+?)的位置',  # 匹配"查看XX的位置"
        r'查询\s*(.+?)的位置',  # 匹配"查询XX的位置"
        r'(?:查询|查看)\s*(.+?)(?:位置|在哪里|在哪儿|在什么地方)',  # 匹配"查询XX位置"
    ]
    
    # 遍历所有模式尝试匹配
    for pattern in resource_patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            resource_name = match.group(1).strip()
            break
    
    # 清理提取到的资源名称中的标点符号
    if resource_name:
        resource_name = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', resource_name).strip()
    
    logger.info(f"提取到资源参数 - 资源名称: {resource_name}, 资源类型: {resource_type}")
    
    return {
        "resource_name": resource_name,
        "resource_type": resource_type
    }

def extract_plan_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取预案类型"""
    plan_type = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 如果用户明确要查询所有预案
    if "所有预案" in cleaned_input or "全部预案" in cleaned_input:
        return {"plan_type": ""}
    
    # 各种匹配模式
    patterns = [
        r'搜索(?:一下|下)?(?P<type>[\w\d]+?)(?:类型|类)?(?:的)?(?:应急)?预案',
        r'查找(?:一下|下)?(?P<type>[\w\d]+?)(?:类型|类)?(?:的)?(?:应急)?预案',
        r'查询(?:一下|下)?(?P<type>[\w\d]+?)(?:类型|类)?(?:的)?(?:应急)?预案',
        r'(?P<type>[\w\d]+?)(?:类型|类)?(?:的)?(?:应急)?预案(?:查询|搜索|列表)',
        r'(?P<type>[\w\d]+?)(?:类型|类|方面|相关|领域)(?:的)?(?:应急)?预案'
    ]
    
    # 依次尝试各种模式
    for pattern in patterns:
        m = re.search(pattern, cleaned_input)
        if m:
            plan_type = m.group('type').strip()
            # 去掉可能的“全部/所有”修饰词
            plan_type = re.sub(r'^(全部|所有|所有的|全部的)', '', plan_type)
            break
    
    # 如果没有找到具体类型但包含预案查询的意图
    if not plan_type and any(keyword in cleaned_input for keyword in ["查询预案", "搜索预案", "查找预案", "预案查询"]):
        plan_type = ""
    
    logger.info(f"提取到预案参数 - 预案类型: {plan_type}")
    
    return {
        "plan_type": plan_type
    }

def extract_event_query_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取事件查询参数"""
    page = 1
    page_size = 50
    event_status = ""
    event_name = ""
    event_level = ""
    
    # 提取页码信息
    page_match = re.search(r'第(\d+)页', user_input)
    if page_match:
        page = int(page_match.group(1))
    
    # 提取页面大小
    size_match = re.search(r'(\d+)条', user_input)
    if size_match:
        page_size = int(size_match.group(1))
    
    # 提取事件名称
    name_patterns = [
        r'名称包含(.+?)的事件',
        r'(.+?)事件',
        r'事件名称.*?([^，,\s]+)'
    ]
    
    for pattern in name_patterns:
        match = re.search(pattern, user_input)
        if match:
            event_name = match.group(1).strip()
            break
    
    logger.info(f"提取到事件查询参数 - 页码: {page}, 页面大小: {page_size}, 事件名称: {event_name}")
    
    return {
        "page": page,
        "page_no": page,
        "page_size": page_size,
        "event_status": event_status,
        "event_name": event_name,
        "event_level": event_level
    }

def extract_area_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取区域名称"""
    area_name = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要", "查询", "搜索"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 尝试匹配"XX市"或"XX区"格式
    area_patterns = [
        r'([^,，\s]+?(?:市|区))(?:的)?(?:所有|全部)?(?:应急)?预案',
        r'查询(?:[^,，\s]+?)?([^,，\s]+?(?:市|区))(?:的)?(?:所有|全部)?(?:应急)?预案',
        r'搜索(?:[^,，\s]+?)?([^,，\s]+?(?:市|区))(?:的)?(?:所有|全部)?(?:应急)?预案',
        r'(?:所有|全部)?([^,，\s]+?(?:市|区))(?:的)?(?:应急)?预案',
        r'([^,，\s]+?(?:市|区))本级(?:的)?(?:所有|全部)?(?:应急)?预案'
    ]
    
    # 依次尝试各种模式
    for pattern in area_patterns:
        matches = re.search(pattern, cleaned_input)
        if matches:
            area_name = matches.group(1).strip()
            break
    
    logger.info(f"提取到区域参数 - 区域名称: {area_name}")
    
    return {
        "area_name": area_name
    }

def extract_resource_camera_params(user_input: str) -> Dict[str, Any]:
    """
    从用户输入中提取资源名称和类型，用于查询资源周边视频信息
    """
    resource_name = ""
    resource_type = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要", 
               "查询", "搜索", "查看", "打开", "显示", "展示", "调出", "查询一下", "看一下", 
               "我要查询", "我要搜索", "麻烦查询", "麻烦搜索", "帮我查询", "帮我搜索"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 资源类型映射（中文到英文）
    resource_type_map = {
        "防护目标": "protection_target",
        "救援队伍": "rescue_team",
        "应急仓库": "repository",
        "物资仓库": "repository",
        "企业": "enterpriseInfo",
        "人员": "person",
        "避难场所": "refuge",           # 新增
        "医疗卫生": "medical_health" 
    }
    
    # 1. 首先尝试同时提取资源名称和类型
    # 匹配格式如"XX应急仓库周边的视频"、"XX防护目标的摄像头"等
    for zh_type, en_type in resource_type_map.items():
        pattern = fr'([^,，\s]+?{zh_type})(?:周边|附近|旁边|的|周围)?(?:视频|摄像头|监控|摄像机|监控点|点位)'
        match = re.search(pattern, cleaned_input)
        if match:
            resource_name = match.group(1).replace(zh_type, "").strip()  # 移除类型名称
            resource_type = en_type
            break
    # for zh_type, en_type in resource_type_map.items():
    #     # 模式1: 名称在类型前面 (如: BB避难场所)
    #     pattern1 = fr'([^,，\s]+?){zh_type}(?:周边|附近|旁边|的|周围)?(?:视频|摄像头|监控|摄像机|监控点|点位)'
    #     match1 = re.search(pattern1, cleaned_input)
        
    #     # 模式2: 类型在名称前面 (如: 避难场所BB)  
    #     pattern2 = fr'{zh_type}([^,，\s]+?)(?:周边|附近|旁边|的|周围)?(?:视频|摄像头|监控|摄像机|监控点|点位)'
    #     match2 = re.search(pattern2, cleaned_input)
        
    #     if match1:
    #         resource_name = match1.group(1).strip()
    #         resource_type = en_type
    #         break
    #     elif match2:
    #         resource_name = match2.group(1).strip()
    #         resource_type = en_type
    #         break
    # 2. 如果没有匹配到完整资源名称和类型，尝试从"周边"、"附近"等词前面提取资源名称
    if not resource_name:
        surroundings = ["周边", "附近", "旁边", "周围"]
        for surround in surroundings:
            if surround in cleaned_input:
                parts = cleaned_input.split(surround)
                if len(parts) > 1:
                    # 获取"周边"等词前面的内容作为资源名称
                    resource_name = parts[0].strip()
                    # 移除资源名称中可能包含的其他词
                    for word in ["的", "查看", "显示", "打开"]:
                        if resource_name.endswith(word):
                            resource_name = resource_name[:-len(word)].strip()
                    break
    
    # 3. 如果仍然没有提取到资源名称，尝试从整个输入中提取引号中的内容
    if not resource_name:
        quote_matches = re.findall(r'["\'""'']([^"\'""'']+)["\'""'']', cleaned_input)
        if quote_matches:
            resource_name = quote_matches[0].strip()
    
    # 4. 如果仍然没有提取到资源名称，尝试提取包含特定词的短语
    if not resource_name:
        name_indicators = ["仓库", "中心", "目标", "队伍", "企业", "单位", "部门", "基地", "站", "点"]
        for indicator in name_indicators:
            pattern = fr'([^,，\s]+?{indicator})'
            match = re.search(pattern, cleaned_input)
            if match:
                resource_name = match.group(1).strip()
                break
    
    # 5. 如果已提取到资源名称但没有类型，尝试从名称或输入中推断类型
    if resource_name and not resource_type:
        # 根据名称特征推断类型
        if any(keyword in resource_name for keyword in ["仓库", "物资", "储备"]):
            resource_type = "repository"
        elif any(keyword in resource_name for keyword in ["防护", "目标", "保护"]):
            resource_type = "protection_target"
        elif any(keyword in resource_name for keyword in ["队伍", "救援", "应急队"]):
            resource_type = "rescue_team"
        elif any(keyword in resource_name for keyword in ["企业", "公司", "厂"]):
            resource_type = "enterpriseInfo"
        elif any(keyword in resource_name for keyword in ["人员", "人"]):
            resource_type = "person"
        elif any(keyword in resource_name for keyword in ["避难", "避难所", "场所"]):  # 新增
            resource_type = "refuge"
        elif any(keyword in resource_name for keyword in ["医疗", "卫生", "医院", "诊所"]):  # 新增
            resource_type = "medical_health"
        else:
            # 从输入中查找类型关键词
            for zh_type, en_type in resource_type_map.items():
                if zh_type in cleaned_input:
                    resource_type = en_type
                    break
    
    # 记录提取结果
    logger.info(f"提取到资源周边视频参数 - 资源名称: {resource_name}, 资源类型: {resource_type}")
    
    return {
        "resource_name": resource_name,
        "resource_type": resource_type
    }

def extract_call_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取呼叫参数"""
    resource_name = ""
    resource_type = ""
    calling_type = "0"  # 默认为视频通话
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 确定呼叫类型
    if "视频" in cleaned_input:
        calling_type = "0"
    elif "电话" in cleaned_input:
        calling_type = "1"
    
    # 资源类型映射
    resource_type_keywords = {
        "队伍": "rescue_team",
        "救援队": "rescue_team",
        "救援队伍": "rescue_team",
        "人员": "person",
        "队员": "person",
        "指挥员": "person",
        "负责人": "person"
    }
    
    # 提取资源名称和类型
    for keyword, r_type in resource_type_keywords.items():
        if keyword in cleaned_input:
            resource_type = r_type
            # 提取名称
            pattern = fr'([^,，\s]+?)(?:{keyword})'
            match = re.search(pattern, cleaned_input)
            if match:
                resource_name = match.group(1).strip()
                break
    
    # 如果没有匹配到，尝试提取引号中的内容
    if not resource_name:
        quote_matches = re.findall(r'["\'""'']([^"\'""'']+)["\'""'']', cleaned_input)
        if quote_matches:
            resource_name = quote_matches[0].strip()
            resource_type = "person"  # 默认为人员
    
    logger.info(f"提取到呼叫参数 - 资源名称: {resource_name}, 资源类型: {resource_type}, 呼叫类型: {calling_type}")
    
    return {
        "resource_name": resource_name,
        "resource_type": resource_type,
        "calling_type": calling_type
    }

def extract_model_name(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取模型名称"""
    model_name = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要", "按照", "根据", "使用"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 各种匹配模式
    patterns = [
        r'([^,，\s]+?模型)(?:附近|周边|周围|搜索|查询)',
        r'模型(?:[:：\s]*)?([^,，\s]+)',
        r'按照(?:\s*)?([^,，\s]+?模型)',
        r'根据(?:\s*)?([^,，\s]+?模型)',
        r'使用(?:\s*)?([^,，\s]+?模型)',
    ]
    
    # 依次尝试各种模式
    for pattern in patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            model_name = match.group(1).strip()
            break
    
    logger.info(f"提取到模型参数 - 模型名称: {model_name}")
    
    return {
        "model_name": model_name
    }

def extract_real_time_travel(user_input: str) -> Dict[str, Any]:
    """
    判断是否是启动一键调度跟进实时轨迹的请求，并提取事件名称
    """
    is_real_time_travel = False
    event_name = ""
    
    # 关键词组合
    keyword_sets = [
        ["启动", "一键", "调度"],
        ["启动", "调度", "轨迹"],
        ["跟进", "救援队伍", "轨迹"],
        ["实时", "轨迹", "救援"],
        ["跟踪", "救援队伍", "位置"],
        ["实时", "跟踪", "救援"],
        ["查看", "救援队伍", "轨迹"]
    ]
    
    for keywords in keyword_sets:
        if all(keyword in user_input for keyword in keywords):
            is_real_time_travel = True
            break
    
    # 如果是实时轨迹请求，尝试提取事件名称
    if is_real_time_travel:
        # 尝试匹配"启动一键调度跟进XX事件所有救援队伍实时轨迹"这种格式
        # 修改正则表达式，更精确地提取事件名称
        event_pattern = r'跟进([\u4e00-\u9fa5a-zA-Z0-9]+?事件)(?:所有)?救援队伍'
        match = re.search(event_pattern, user_input)
        if match:
            event_name = match.group(1).strip()
            logger.info(f"从轨迹请求中提取到事件名称: {event_name}")
        
        # 如果上面没匹配到，尝试更通用的事件名称提取
        if not event_name:
            # 匹配包含"事件"的词组，但排除包含"调度"、"跟进"等词的匹配
            event_matches = re.findall(r'([\u4e00-\u9fa5a-zA-Z0-9]+?事件)', user_input)
            if event_matches:
                # 选择不包含"调度"、"跟进"等关键词的事件名称
                for match in event_matches:
                    if not any(kw in match for kw in ["调度", "跟进", "启动", "一键"]):
                        event_name = match.strip()
                        break
                # 如果都包含关键词，取第一个
                if not event_name and event_matches:
                    event_name = event_matches[0].strip()
                logger.info(f"从通用模式中提取到事件名称: {event_name}")
    
    # 记录判断结果
    logger.info(f"判断是否启动一键调度跟进实时轨迹: {is_real_time_travel}, 事件名称: {event_name}")
    
    return {
        "is_real_time_travel": is_real_time_travel,
        "event_name": event_name
    }

def extract_meeting_name(user_input: str, is_start: bool = True) -> Dict[str, Any]:
    """
    从用户输入中提取会议名称
    
    参数:
        user_input: 用户输入
        is_start: 是否是开启会议，False表示结束会议
    """
    meeting_name = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请", "帮我", "帮忙", "麻烦", "想要", "需要", "我想", "我需要", "希望能", "我要", 
                "查询", "搜索", "查看", "打开", "显示", "展示", "调出", "查询一下", "看一下", 
                "我要查询", "我要搜索", "麻烦查询", "麻烦搜索", "帮我查询", "帮我搜索"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 检查常见的会议场景，提高处理准确性
    common_meetings = {
        "森林火险会议": ["森林火险", "森林", "火险"],
        "森林火灾会议": ["森林火灾", "火灾"],
        "应急救灾会议": ["应急救灾", "救灾"],
        "防灾减灾会议": ["防灾减灾", "减灾"],
        "应急指挥会议": ["应急指挥", "指挥"],
        "地震灾害会议": ["地震灾害", "地震"],
        "洪水灾害会议": ["洪水灾害", "洪水", "水灾"]
    }
    
    # 直接匹配整个输入 - 增加更多直接匹配项
    if is_start:
        direct_matches = [
            "开启森林火险会议", "启动森林火险会议", "开始森林火险会议",
            "开启火灾会议", "启动火灾会议", "开始火灾会议",
            "开启森林会议", "启动森林会议", "开始森林会议"
        ]
        for direct_match in direct_matches:
            if direct_match in cleaned_input:
                parts = direct_match.split("会议")
                if parts and parts[0]:
                    action_part = parts[0]
                    # 提取动作后的主题
                    for start_action in ["开启", "启动", "开始", "创建", "建立"]:
                        if start_action in action_part:
                            meeting_theme = action_part.replace(start_action, "").strip()
                            if meeting_theme:
                                return {"meeting_name": meeting_theme + "会议"}
    else:  # 结束会议
        direct_matches = [
            "结束森林火险会议", "终止森林火险会议", "关闭森林火险会议",
            "结束火灾会议", "终止火灾会议", "关闭火灾会议", 
            "结束森林会议", "终止森林会议", "关闭森林会议"
        ]
        for direct_match in direct_matches:
            if direct_match in cleaned_input:
                parts = direct_match.split("会议")
                if parts and parts[0]:
                    action_part = parts[0]
                    # 提取动作后的主题
                    for end_action in ["结束", "终止", "关闭", "停止"]:
                        if end_action in action_part:
                            meeting_theme = action_part.replace(end_action, "").strip()
                            if meeting_theme:
                                return {"meeting_name": meeting_theme + "会议"}
    
    # 匹配模式 - 使用更广泛的动词列表
    start_actions = ["开启", "启动", "开始", "创建", "建立", "召开", "举行", "组织", "发起"]
    end_actions = ["结束", "终止", "关闭", "停止", "解散", "取消", "散会"]
    
    action_words = start_actions if is_start else end_actions
    action_pattern = "|".join(action_words)
    
    # 增加更多匹配模式，使用更宽松的匹配条件
    meeting_patterns = [
        # 标准模式 - 修改为贪婪匹配以确保能提取完整会议名称
        rf'(?:{action_pattern})(?:一下|下)?(?:\s*)?([^,，]+?(?:会议|会|研讨会|发布会|例会))',  # "开启XX会议"
        rf'([^,，]+?(?:会议|会|研讨会|发布会|例会))(?:的)?(?:{action_pattern})',  # "XX会议的开启"
        
        # 更宽松的匹配模式，允许动词和会议中间有其他内容
        rf'{action_pattern}(?:[^,，。\n]{0,20})?([\u4e00-\u9fa5a-zA-Z0-9]+?会议)',  # 匹配任何可能的动词+主题+会议组合
        
        # 匹配特定的会议类型
        rf'({action_pattern})[^\n]*?((?:森林火险|森林火灾|地震|洪水|应急|救灾|防灾|减灾)会议)',
        
        # 匹配中间带空格的会议名称
        rf'{action_pattern}\s*([\u4e00-\u9fa5a-zA-Z0-9\s]+?会议)',
    ]
    
    for pattern in meeting_patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            # 检查捕获组数量
            if len(match.groups()) > 1 and match.group(2):  # 如果匹配到第二个组
                meeting_name = match.group(2).strip()
            else:  # 否则使用第一个组
                meeting_name = match.group(1).strip()
            break
    
    # 如果仍未提取出会议名称，尝试从输入中找出最长的包含"会议"的子串
    if not meeting_name:
        words = cleaned_input.split()
        for word in words:
            if "会议" in word:
                meeting_name = word
                break
    
    # 如果没有匹配到会议名称，但输入中包含"森林火险"这些关键词和"会议"，直接返回特定会议
    if not meeting_name:
        # 只有在完全匹配关键词时才使用预设会议名称，避免误匹配
        for topic, keywords in common_meetings.items():
            for keyword in keywords:
                if keyword in cleaned_input and "会议" in cleaned_input and len(keyword) > 1:
                    # 确保是完整匹配，避免"火灾"匹配到"森林火灾"
                    if keyword == "火灾" and "森林火灾" not in cleaned_input:
                        continue
                    logger.info(f"检测到常见会议关键词: {keyword}")
                    return {"meeting_name": topic}
    
    # 如果仍未找到，但用户输入中包含"会议"，尝试从上下文推断
    if not meeting_name and "会议" in cleaned_input:
        # 直接从输入中提取会议名称
        parts = cleaned_input.split("会议")
        if parts and parts[0]:
            # 从会议前面的文本提取名称，考虑开启/结束等动词
            text_before = parts[0]
            # 移除动词
            for verb in start_actions + end_actions:
                if verb in text_before:
                    text_before = text_before.replace(verb, "")
            
            text_before = text_before.strip()
            if text_before:  # 确保有内容
                meeting_name = text_before + "会议"
    
    # 如果提取的名称太短或只有"会议"两个字，尝试更智能地提取主题
    if not meeting_name or len(meeting_name) <= 3 or meeting_name == "会议":
        # 尝试从整个输入中推断会议主题
        for topic, keywords in common_meetings.items():
            for keyword in keywords:
                if keyword in cleaned_input:
                    meeting_name = topic
                    break
            if meeting_name:
                break
    
    # 记录提取结果
    logger.info(f"提取到{'开启' if is_start else '结束'}会议名称: {meeting_name}")
    
    return {
        "meeting_name": meeting_name
    }