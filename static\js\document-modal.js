// 文档模态窗口功能，查看已上传文档功能
document.addEventListener('DOMContentLoaded', function() {
  // 创建模态窗口元素
  const modalOverlay = document.createElement('div');
  modalOverlay.className = 'modal-overlay';
  modalOverlay.id = 'document-modal';
  
  modalOverlay.innerHTML = `
    <div class="modal-container">
      <div class="modal-header">
        <div class="modal-title">已上传文档列表</div>
        <button class="modal-close" id="close-document-modal">&times;</button>
      </div>
      <div class="modal-body">
        <iframe class="modal-iframe" id="document-list-frame" src="/document_list"></iframe>
      </div>
    </div>
  `;
  
  // 添加到body
  document.body.appendChild(modalOverlay);
  
  // 获取按钮和模态窗口元素
  const viewDocsBtn = document.getElementById('view-uploaded-docs');
  const documentModal = document.getElementById('document-modal');
  const closeModalBtn = document.getElementById('close-document-modal');
  
  // 点击查看文档按钮打开模态窗口
  if (viewDocsBtn) {
    viewDocsBtn.addEventListener('click', function(e) {
      e.preventDefault();
      documentModal.style.display = 'flex';
      // 重新加载iframe以确保内容最新
      document.getElementById('document-list-frame').src = '/document_list';
    });
  }
  
  // 点击关闭按钮关闭模态窗口
  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', function() {
      documentModal.style.display = 'none';
    });
  }
  
  // 点击模态窗口外部关闭模态窗口
  documentModal.addEventListener('click', function(e) {
    if (e.target === documentModal) {
      documentModal.style.display = 'none';
    }
  });
});
