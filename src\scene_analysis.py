#!/usr/bin/env python3
import asyncio
import configparser
import logging
import os
import base64
from typing import AsyncGenerator
from dotenv import load_dotenv
from langchain.callbacks import AsyncIteratorCallbackHandler
from langchain.prompts import PromptTemplate
from langchain_community.llms import Ollama
from langchain_openai import ChatOpenAI
from PIL import Image
import io
import requests
import json

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("scene_analysis")

# 获取环境变量中的配置
DEFAULT_MULTIMODAL_MODEL = os.getenv("DEFAULT_MULTIMODAL_MODEL", "llava")
OLLAMA_DIRECT_API = os.getenv("OLLAMA_DIRECT_API", "false").lower() == "true"

# 场景分析模板
SCENE_TEMPLATES = {
    "flood": """
你是一个汛期巡查员，针对上面的照片请从下面几个方面来分析安全隐患：
【场景解析】
【潜在风险或安全隐患】
【重大事故隐患提示】
【法律依据】（请列出具体条目）
【整改建议】
""",
    "safety": """
你是一个安全巡查员，针对上面的照片请从下面几个方面来分析安全隐患：
【场景解析】
【潜在风险或安全隐患】
【重大事故隐患提示】
【法律依据】（请列出具体条目）
【整改建议】
""",
    "fire": """
你是一个消防巡查员，针对上面的照片请从下面几个方面来分析安全隐患：
【场景解析】
【潜在风险或安全隐患】
【重大事故隐患提示】
【法律依据】（请列出具体条目）
【整改建议】
"""
}

# 已知支持图像处理的模型列表
OLLAMA_MULTIMODAL_MODELS = [
    "gemma3:27b","llava", "bakllava", "llava-llama3", "llava-13b", "llava-7b", "llava-v1.6-mistral-7b",
    "llava-v1.6-34b", "llava-next"
]

OPENAI_MULTIMODAL_MODELS = [
    "gemma3:27b","gpt-4-vision-preview", "gpt-4o", "gpt-4o-mini"
]

# 添加Anthropic的多模态模型
ANTHROPIC_MULTIMODAL_MODELS = [
    "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"
]

# 将所有多模态模型合并到一个列表
ALL_MULTIMODAL_MODELS = OLLAMA_MULTIMODAL_MODELS + OPENAI_MULTIMODAL_MODELS + ANTHROPIC_MULTIMODAL_MODELS

async def analyze_scene_stream(
    scene: str,
    image_path: str,
) -> AsyncGenerator[str, None]:
    """
    根据选定的场景分析图片内容，流式返回结果。
    """
    # 引用全局变量，防止局部修改导致的引用错误
    global OLLAMA_DIRECT_API

    logger.info(f"开始场景分析: '{scene}'")
    logger.info(f"图片路径: {image_path}")
    logger.info(f"初始OLLAMA_DIRECT_API设置: {OLLAMA_DIRECT_API}")

    # 添加思考过程标记
    #yield "<think>正在分析图片并根据选定场景进行评估...</think>"

    # 1. 验证图片
    try:
        if not os.path.exists(image_path):
            error_msg = f"图片文件不存在: {image_path}"
            logger.error(error_msg)
            yield f"错误：{error_msg}"
            return

        if os.path.getsize(image_path) == 0:
            error_msg = f"图片文件为空: {image_path}"
            logger.error(error_msg)
            yield f"错误：{error_msg}"
            return

        image = Image.open(image_path)
        width, height = image.size
        logger.info(f"成功加载图片: {image_path}, 尺寸: {width}x{height}, 格式: {image.format}")

        # 确保图片格式有效
        if not image.format:
            logger.warning("图片格式未知，将其转换为JPEG格式")
            image = image.convert('RGB')

        # 转换图片为base64，以便可以传递给模型（如果需要）
        buffered = io.BytesIO()
        image.save(buffered, format=image.format or "JPEG")
        img_str = base64.b64encode(buffered.getvalue()).decode()
        logger.info(f"图片成功转换为base64格式，长度: {len(img_str)}")

    except Exception as e:
        logger.error(f"加载图片失败: {str(e)}", exc_info=True)
        yield f"错误：加载图片时出错。错误信息: {str(e)}"
        return

    # 2. 配置LLM
    config = configparser.ConfigParser()
    try:
        # 尝试从当前目录、上级目录和项目根目录加载配置文件
        config_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini"),  # src目录中
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.ini"),  # 项目根目录
            "config.ini"  # 当前工作目录
        ]

        config_loaded = False
        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    # 明确指定 UTF-8 编码
                    config.read(config_path, encoding='utf-8')
                    logger.info(f"成功从 {config_path} 加载配置文件")
                    config_loaded = True
                    break
                except Exception as e:
                    logger.warning(f"使用 UTF-8 编码从 {config_path} 加载配置文件失败: {str(e)}")
                    try:
                        config.read(config_path, encoding='latin1')
                        logger.info(f"成功从 {config_path} 使用latin1编码加载配置文件")
                        config_loaded = True
                        break
                    except Exception as e2:
                        logger.warning(f"使用 latin1 编码从 {config_path} 加载配置文件失败: {str(e2)}")

        if not config_loaded:
            logger.error("未能从任何位置加载配置文件")
            yield "错误：加载服务器配置失败，请联系管理员查看日志。"
            return

    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}", exc_info=True)
        yield "错误：加载服务器配置失败，请联系管理员查看日志。"
        return

    # 初始化回调处理器
    callback_handler = AsyncIteratorCallbackHandler()

    # 获取LLM配置
    llm_type = config.get("LLM", "LLM_TYPE", fallback="ollama")
    model_name = config.get("LLM", "MODEL", fallback=DEFAULT_MULTIMODAL_MODEL)  # 使用环境变量默认值
    logger.info(f"配置的LLM类型: {llm_type}, 模型: {model_name}")

    # 获取场景模板
    if scene not in SCENE_TEMPLATES:
        error_msg = f"未知场景类型: {scene}"
        logger.error(error_msg)
        yield f"错误：{error_msg}"
        return

    scene_template = SCENE_TEMPLATES[scene]
    logger.info(f"使用场景模板: {scene}")

    # 添加思考过程
    #yield f"<think>使用{llm_type}类型的语言模型: {model_name}\n\n正在初始化模型...\n\n场景类型: {scene}</think>"

    try:
        # 根据配置选择LLM类型
        if llm_type.lower() == "openai":
            openai_api_key = config.get("LLM", "OPENAI_API_KEY", fallback=os.getenv("OPENAI_API_KEY"))
            openai_api_base = config.get("LLM", "OPENAI_API_BASE", fallback=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1"))

            if not openai_api_key:
                logger.error("OpenAI API Key未配置")
                yield "错误：OpenAI API Key 未配置。"
                return

            logger.info(f"使用OpenAI模型: {model_name}, API Base: {openai_api_base}")

            # 检查是否是多模态模型
            is_multimodal = any(mm in model_name for mm in OPENAI_MULTIMODAL_MODELS)

            if is_multimodal:
                from langchain_openai import ChatOpenAI
                from langchain_core.messages import HumanMessage

                # 创建包含图片的消息
                img_data = f"data:image/{image.format.lower() if image.format else 'jpeg'};base64,{img_str}"

                messages = [
                    HumanMessage(
                        content=[
                            {"type": "text", "text": scene_template},
                            {"type": "image_url", "image_url": {"url": img_data}}
                        ]
                    )
                ]

                logger.info("创建包含文本和图片的OpenAI多模态消息")

                llm = ChatOpenAI(
                    model_name=model_name,
                    openai_api_key=openai_api_key,
                    openai_api_base=openai_api_base,
                    streaming=True,
                    callbacks=[callback_handler],
                    temperature=0.7
                )

                # 创建异步任务
                task = asyncio.create_task(
                    llm.agenerate([messages])
                )

            else:
                logger.error(f"模型 {model_name} 可能不支持图像理解功能")
                yield f"错误：所选模型 {model_name} 可能不支持图像理解功能。请在配置中使用支持多模态的模型（如 {', '.join(OPENAI_MULTIMODAL_MODELS[:2])})。"
                return
        else:
            # 默认使用Ollama
            ollama_base_url = config.get("LLM", "OLLAMA_BASE_URL", fallback="http://localhost:11434")
            logger.info(f"使用Ollama模型: {model_name}, Base URL: {ollama_base_url}")

            # 检查是否是多模态模型
            is_multimodal = any(mm in model_name.lower() for mm in OLLAMA_MULTIMODAL_MODELS)

            if not is_multimodal:
                logger.warning(f"Ollama模型 {model_name} 可能不支持图像理解功能")
                logger.info(f"尝试退回到默认多模态模型: {DEFAULT_MULTIMODAL_MODEL}")
                model_name = DEFAULT_MULTIMODAL_MODEL

            # 根据环境变量决定使用直接API还是Langchain
            use_direct_api = OLLAMA_DIRECT_API  # 使用局部变量保存当前状态
            if use_direct_api:
                try:
                    # 创建基本Ollama实例 - 只用于日志
                    logger.info("使用Ollama直接API接口调用")

                    # 创建提示模板
                    prompt = PromptTemplate(
                        template=scene_template,
                        input_variables=[]
                    )

                    formatted_prompt = prompt.format()
                    logger.info(f"格式化后的提示词: {formatted_prompt}")

                    # 准备通过Ollama的REST API直接处理图片
                    # 获取图片内容
                    with open(image_path, "rb") as f:
                        image_content = f.read()

                    # 使用Base64编码图片
                    image_base64 = base64.b64encode(image_content).decode('utf-8')

                    # 通过Ollama REST API直接发送请求
                    generate_url = f"{ollama_base_url}/api/generate"
                    logger.info(f"准备调用Ollama API: {generate_url}")

                    payload = {
                        "model": model_name,
                        "prompt": formatted_prompt,
                        "stream": True,
                        "options": {
                            "temperature": 0.7
                        },
                        "images": [image_base64]
                    }

                    # 创建异步任务
                    async def stream_from_ollama_api():
                        try:
                            response = requests.post(generate_url, json=payload, stream=True)
                            response.raise_for_status()

                            for line in response.iter_lines():
                                if line:
                                    try:
                                        data = json.loads(line)
                                        if "response" in data:
                                            yield data["response"]
                                    except json.JSONDecodeError:
                                        logger.error(f"无法解析Ollama API响应: {line}")

                            logger.info("Ollama API流式响应结束")
                        except requests.RequestException as e:
                            logger.error(f"Ollama API请求失败: {str(e)}")
                            yield f"错误：Ollama API请求失败: {str(e)}"

                    # 创建异步任务 - 这里我们需要创建一个生成器对象
                    async def ollama_api_wrapper():
                        async for token in stream_from_ollama_api():
                            yield token
                            # 同时将token传递给callback_handler，以便它能够流式输出
                            await callback_handler.on_llm_new_token(token)

                    # 启动任务
                    ollama_stream_task = asyncio.create_task(ollama_api_wrapper().__anext__())
                    task = ollama_stream_task

                except Exception as e:
                    logger.error(f"配置Ollama直接API时出错: {str(e)}", exc_info=True)
                    # 降级到使用Langchain
                    logger.info("降级到使用Langchain方式")
                    use_direct_api = False  # 修改局部变量，而不是全局变量

            # 如果不使用直接API或者直接API失败，使用Langchain
            if not use_direct_api:
                try:
                    # 尝试使用ChatOllama，更现代的接口
                    logger.info("尝试使用ChatOllama接口")

                    from langchain_community.chat_models import ChatOllama
                    from langchain_core.messages import HumanMessage

                    chat_ollama = ChatOllama(
                        model=model_name,
                        base_url=ollama_base_url,
                        callbacks=[callback_handler],
                        temperature=0.7
                    )

                    # 从磁盘读取图片内容
                    with open(image_path, "rb") as image_file:
                        image_data = image_file.read()
                        image_base64 = base64.b64encode(image_data).decode()

                    # 构建消息
                    message = HumanMessage(
                        content=[
                            {
                                "type": "text",
                                "text": scene_template
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}"
                                }
                            }
                        ]
                    )

                    # 创建异步任务
                    task = asyncio.create_task(chat_ollama.agenerate([[message]]))

                except Exception as chat_error:
                    logger.error(f"使用ChatOllama失败: {str(chat_error)}", exc_info=True)
                    # 最后尝试使用老式的Ollama接口
                    logger.info("尝试使用老式Ollama接口")

                    try:
                        # 创建提示模板
                        prompt = PromptTemplate(
                            template=scene_template,
                            input_variables=[]
                        )

                        formatted_prompt = prompt.format()

                        # 使用最基础的方式
                        llm = Ollama(
                            model=model_name,
                            base_url=ollama_base_url,
                            callbacks=[callback_handler],
                            temperature=0.7
                        )

                        # 没有图片参数，回退到纯文本分析
                        logger.warning("无法向Ollama传递图片，回退到纯文本分析模式")
                        yield "<think>警告：无法向Ollama传递图片，将仅分析您的文字问题</think>"

                        # 创建异步任务
                        task = asyncio.create_task(llm.agenerate([formatted_prompt]))

                    except Exception as final_e:
                        logger.error(f"所有Ollama调用方法都失败: {str(final_e)}", exc_info=True)
                        yield f"错误：无法初始化支持图像的Ollama模型。建议在配置中使用OpenAI支持多模态的模型，或更新Langchain版本。错误: {str(final_e)}"
                        return
    except Exception as e:
        logger.error(f"初始化LLM失败: {str(e)}", exc_info=True)
        yield f"错误：初始化语言模型失败，请检查配置和模型服务状态。错误: {str(e)}"
        return

    # 添加思考过程
    #yield "<think>模型初始化完成，正在根据场景分析图片内容...</think>"

    # 异步执行查询并流式返回
    logger.info(f"开始异步执行场景分析，使用模型: {model_name}")
    try:
        token_count = 0
        logger.info("开始从回调处理器获取token")
        async for token in callback_handler.aiter():
            token_count += 1
            if token_count % 50 == 0:
                logger.info(f"已接收 {token_count} 个token")
            yield token
        logger.debug(f"Callback aiter() 迭代完成，共接收 {token_count} 个token")

        # 等待任务完成
        result = await task
        logger.info("场景分析执行完成。")

    except Exception as e:
        logger.error(f"场景分析失败: {str(e)}", exc_info=True)
        yield f"\n错误：在生成回答时发生内部错误。请查看日志。错误: {str(e)}"
        return

    logger.info("场景分析流处理结束。")
