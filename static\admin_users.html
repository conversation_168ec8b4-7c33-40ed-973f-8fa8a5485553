<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 应急指挥智能体</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .tabs {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-button {
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            transition: all 0.2s;
        }

        .tab-button.active {
            color: #007bff;
            border-bottom: 2px solid #007bff;
        }

        .tab-content {
            padding: 20px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .user-table th,
        .user-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .user-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .user-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .role-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .role-admin {
            background: #e7f3ff;
            color: #0066cc;
        }

        .role-user {
            background: #f0f0f0;
            color: #666;
        }

        .actions {
            display: flex;
            gap: 5px;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h3 {
            margin: 0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            min-height: 80px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-users"></i> 用户管理</h1>
        <div class="header-actions">
            <a href="/app" class="btn btn-secondary">
                <i class="fas fa-home"></i> 返回主页
            </a>
            <button class="btn btn-primary" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> 退出登录
            </button>
        </div>
    </div>

    <div class="container">
        <div class="tabs">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('pending')">
                    <i class="fas fa-clock"></i> 待审核用户
                </button>
                <button class="tab-button" onclick="switchTab('all')">
                    <i class="fas fa-users"></i> 所有用户
                </button>
            </div>

            <div class="tab-content">
                <div id="pending-tab" class="tab-pane active">
                    <div id="pending-loading" class="loading">
                        <div class="spinner"></div>
                        <p>加载中...</p>
                    </div>
                    <div id="pending-content" style="display: none;">
                        <table class="user-table" id="pending-table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>注册时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="pending-tbody">
                            </tbody>
                        </table>
                        <div id="pending-empty" class="empty-state" style="display: none;">
                            <i class="fas fa-inbox" style="font-size: 48px; color: #ccc; margin-bottom: 10px;"></i>
                            <p>暂无待审核用户</p>
                        </div>
                    </div>
                </div>

                <div id="all-tab" class="tab-pane">
                    <div id="all-loading" class="loading">
                        <div class="spinner"></div>
                        <p>加载中...</p>
                    </div>
                    <div id="all-content" style="display: none;">
                        <table class="user-table" id="all-table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                    <th>审核时间</th>
                                    <th>审核人</th>
                                </tr>
                            </thead>
                            <tbody id="all-tbody">
                            </tbody>
                        </table>
                        <div id="all-empty" class="empty-state" style="display: none;">
                            <i class="fas fa-inbox" style="font-size: 48px; color: #ccc; margin-bottom: 10px;"></i>
                            <p>暂无用户数据</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div id="approval-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">审核用户</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="form-group">
                <label for="approval-reason">审核意见（可选）：</label>
                <textarea id="approval-reason" placeholder="请输入审核意见..."></textarea>
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button id="confirm-btn" class="btn btn-success" onclick="confirmAction()">确认</button>
            </div>
        </div>
    </div>

    <script src="/static/js/admin-users.js"></script>
</body>
</html>
