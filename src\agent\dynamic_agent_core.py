"""
动态代理核心模块 - 支持用户配置工具和系统内置工具的统一管理
"""

import os
import re
import json
import configparser
import logging
from langchain_community.chat_models import ChatOllama
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import PromptTemplate
from langchain_core.callbacks.base import BaseCallbackHandler
from langchain.tools import Tool

from .api_tools import (
    query_event_surround_resource,
    preview_camera,
    one_map_position_resource,
    event_stop_plan,
    event_start_plan,
    query_all_event_infos,
    search_emergency_plan,
    query_area_plans,
    query_resource_cameras,
    start_call_by_name,
    query_model_resources,
    start_real_time_travel,
    start_meeting,
    end_meeting
)
from .dynamic_tools_manager import dynamic_tools_manager

# 创建logger
logger = logging.getLogger("dynamic_agent_core")

# 过滤模型输出中的<think>标签和内容
def filter_think_tags(text):
    """
    过滤掉模型输出中的<think>...</think>标签及其内容
    """
    if not text:
        return text
    filtered_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    return filtered_text

# 创建一个自定义回调处理器来过滤输出
class OutputFilterCallbackHandler(BaseCallbackHandler):
    """过滤模型输出的回调处理器"""
    
    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """流式输出时过滤token"""
        pass
    
    def on_llm_end(self, response, **kwargs) -> None:
        """当LLM完成生成时过滤输出"""
        if hasattr(response, 'generations') and response.generations:
            for gen_list in response.generations:
                for gen in gen_list:
                    if hasattr(gen, 'text'):
                        gen.text = filter_think_tags(gen.text)
                    # 处理message类型的输出
                    if hasattr(gen, 'message') and hasattr(gen.message, 'content'):
                        gen.message.content = filter_think_tags(gen.message.content)
    
    def on_chain_end(self, outputs, **kwargs) -> None:
        """当Chain完成时过滤输出"""
        if 'output' in outputs and isinstance(outputs['output'], str):
            outputs['output'] = filter_think_tags(outputs['output'])


class DynamicCustomFunctionCallingAgent:
    """动态自定义Function Calling代理，整合用户配置工具和系统内置工具"""
    
    def __init__(self, llm, output_filter):
        self.llm = llm
        self.output_filter = output_filter
        
        # 获取系统内置工具和用户配置工具
        self.builtin_functions = self._get_builtin_functions()
        self.dynamic_functions = dynamic_tools_manager.get_tools_functions_definition()
        self.all_functions = self.builtin_functions + self.dynamic_functions
        
        # 创建函数名到函数的映射
        self.builtin_function_map = self._get_builtin_function_map()
        
        logger.info(f"动态Function Calling代理初始化完成，内置工具: {len(self.builtin_functions)}, 用户工具: {len(self.dynamic_functions)}")
    
    def _get_builtin_functions(self):
        """获取系统内置函数定义"""
        return [
            {
                "name": "query_event_surround_resource",
                "description": "查询事件周边的应急资源。resource_type为可选参数，可以是：队伍、仓库、专家、避难场所、医疗卫生、防护目标、企业信息、物资、装备、医院、危险源、视频，或者留空查询所有类型",
                "parameters": {
                    "event_name": "string",
                    "distance": "int",
                    "resource_type": "string|optional"
                }
            },
            {
                "name": "preview_camera",
                "parameters": {
                    "camera_id": "string"
                }
            },
            {
                "name": "one_map_position_resource",
                "parameters": {
                    "resource_name": "string"
                }
            },
            {
                "name": "event_stop_plan",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "event_start_plan",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "query_all_event_infos",
                "parameters": {
                    "page": 1,
                    "page_size": 50
                }
            },
            {
                "name": "search_emergency_plan",
                "parameters": {
                    "page": 1,
                    "page_size": 20
                }
            },
            {
                "name": "query_area_plans",
                "parameters": {
                    "area_name": "string"
                }
            },
            {
                "name": "query_resource_cameras",
                "parameters": {
                    "resource_name": "string",
                    "resource_type": "string"
                }
            },
            {
                "name": "start_call_by_name",
                "description": "根据队伍/人名发起指定的呼叫;resource_type: rescue_team, person;calling_type:0(视频通话),1(电话)",
                "parameters": {
                    "resource_name": "string",
                    "resource_type": "string",
                    "calling_type": "int"
                }
            },
            {
                "name": "query_model_resources",
                "parameters": {
                    "model_name": "string"
                }
            },
            {
                "name": "start_real_time_travel",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "start_meeting",
                "parameters": {
                    "meeting_name": "string"
                }
            },
            {
                "name": "end_meeting",
                "parameters": {
                    "meeting_name": "string"
                }
            }
        ]
    
    def _get_builtin_function_map(self):
        """获取系统内置函数映射"""
        return {
            "query_event_surround_resource": query_event_surround_resource,
            "preview_camera": preview_camera,
            "one_map_position_resource": one_map_position_resource,
            "event_stop_plan": event_stop_plan,
            "event_start_plan": event_start_plan,
            "query_all_event_infos": query_all_event_infos,
            "search_emergency_plan": search_emergency_plan,
            "query_area_plans": query_area_plans,
            "query_resource_cameras": query_resource_cameras,
            "start_call_by_name": start_call_by_name,
            "query_model_resources": query_model_resources,
            "start_real_time_travel": start_real_time_travel,
            "start_meeting": start_meeting,
            "end_meeting": end_meeting
        }
    
    def _create_function_calling_prompt(self, user_query: str, chat_history: str = "") -> str:
        """创建函数调用提示词（参考jiuan_agent格式）"""
        # 简化的函数列表JSON
        functions_json = json.dumps(self.all_functions, ensure_ascii=False)

        # 构建历史对话上下文
        history_context = ""
        if chat_history:
            history_context = f" 历史对话： {chat_history}"

        # 使用与jiuan_agent相同的提示词格式，包含历史对话
        prompt = f"你是一个只能调用函数的助手，必须根据用户的问题选择一个函数，并填写参数。你的回复必须严格按照以下格式返回，不能输出任何自然语言或解释说明： 你只能输出如下格式： {{ \"function_call\": {{ \"name\": \"函数名称\", \"arguments\": \"参数 JSON 字符串\" }} }} 可用函数列表： {functions_json}{history_context} 用户问题：{user_query}"

        return prompt
    
    def _parse_function_call(self, response: str) -> dict:
        """解析函数调用响应"""
        try:
            # 清理响应文本
            response = response.strip()
            
            # 尝试直接解析JSON
            if response.startswith('{') and response.endswith('}'):
                return json.loads(response)
            
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
            
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {str(e)}, 响应: {response}")
            return None
        except Exception as e:
            logger.error(f"解析函数调用时出错: {str(e)}")
            return None
    
    def _execute_function(self, function_call: dict) -> str:
        """执行函数调用（支持内置工具和动态工具）"""
        try:
            function_name = function_call.get("function_call", {}).get("name")
            arguments_str = function_call.get("function_call", {}).get("arguments", "{}")

            if not function_name:
                return "错误：未找到函数名称"

            # 解析参数
            try:
                arguments = json.loads(arguments_str) if isinstance(arguments_str, str) else arguments_str
            except json.JSONDecodeError:
                return f"错误：参数解析失败 - {arguments_str}"

            # 检查是否为内置工具
            if function_name in self.builtin_function_map:
                return self._execute_builtin_function(function_name, arguments)
            else:
                # 执行用户配置的动态工具
                return dynamic_tools_manager.execute_dynamic_tool(function_name, arguments)

        except Exception as e:
            logger.error(f"执行函数时出错: {str(e)}")
            return f"执行函数时出错: {str(e)}"
    
    def _execute_builtin_function(self, function_name: str, arguments: dict) -> str:
        """执行内置函数（使用优化后的映射表机制）"""
        func = self.builtin_function_map[function_name]
        
        # 参数转换映射表
        param_converters = {
            "query_event_surround_resource": lambda args: self._build_param_string([
                args.get("event_name", ""),
                args.get("distance", 5),
                args.get("resource_type", "")
            ], skip_empty_tail=True),
            
            "preview_camera": lambda args: args.get("camera_id", ""),
            
            "one_map_position_resource": lambda args: f"{args.get('resource_name', '')},",
            
            "event_stop_plan": lambda args: args.get("event_name", ""),
            "event_start_plan": lambda args: args.get("event_name", ""),
            
            "query_all_event_infos": lambda args: f"{args.get('page', 1)},{args.get('page_size', 50)},,",
            
            "search_emergency_plan": lambda args: f",{args.get('page', 1)},{args.get('page_size', 20)}",
            
            "query_area_plans": lambda args: args.get("area_name", ""),
            
            "query_resource_cameras": lambda args: self._build_param_string([
                args.get("resource_name", ""),
                args.get("resource_type", "")
            ]),
            
            "start_call_by_name": lambda args: self._build_param_string([
                args.get("resource_name", ""),
                args.get("resource_type", ""),
                args.get("calling_type", "0")
            ]),
            
            "query_model_resources": lambda args: args.get("model_name", ""),
            
            "start_real_time_travel": lambda args: args.get("event_name", ""),
            
            "start_meeting": lambda args: args.get("meeting_name", ""),
            "end_meeting": lambda args: args.get("meeting_name", "")
        }
        
        if function_name not in param_converters:
            return f"错误：未实现的函数调用 - {function_name}"
        
        # 转换参数
        converted_param = param_converters[function_name](arguments)
        
        # 特殊处理start_real_time_travel
        if function_name == "start_real_time_travel":
            return func(converted_param) if converted_param else func()
        
        return func(converted_param)
    
    def _build_param_string(self, params: list, skip_empty_tail: bool = False) -> str:
        """构建参数字符串"""
        if skip_empty_tail:
            # 移除尾部的空参数
            while params and not str(params[-1]).strip():
                params.pop()
        
        return ",".join(str(p) for p in params)
    
    def invoke(self, inputs: dict) -> dict:
        """调用代理处理用户输入"""
        try:
            user_query = inputs.get("input", "")
            chat_history = inputs.get("chat_history", "")

            # 创建函数调用提示词，传递历史对话
            prompt = self._create_function_calling_prompt(user_query, chat_history)
            logger.info(f"生成的提示词: {prompt}")
            
            # 调用LLM
            response = self.llm.invoke(prompt)
            
            # 获取响应内容
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)
            
            logger.info(f"LLM响应: {response_text}")
            
            # 解析函数调用
            function_call = self._parse_function_call(response_text)
            if not function_call:
                return {"output": f"错误：无法解析函数调用 - {response_text}"}
            
            logger.info(f"解析的函数调用: {function_call}")
            
            # 执行函数
            result = self._execute_function(function_call)
            
            return {"output": result}
            
        except Exception as e:
            logger.error(f"代理调用失败: {str(e)}")
            return {"output": f"处理查询时出错: {str(e)}"}
