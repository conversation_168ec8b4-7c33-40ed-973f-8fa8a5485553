"""
动态代理核心模块 - 支持用户配置工具和系统内置工具的统一管理
"""

import os
import re
import json
import configparser
import logging
from langchain_community.chat_models import ChatOllama
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import PromptTemplate
from langchain_core.callbacks.base import BaseCallbackHandler
from langchain.tools import Tool

from .agent_core import CustomFunctionCallingAgent
from .dynamic_tools_manager import dynamic_tools_manager

# 创建logger
logger = logging.getLogger("dynamic_agent_core")

# 过滤模型输出中的<think>标签和内容
def filter_think_tags(text):
    """
    过滤掉模型输出中的<think>...</think>标签及其内容
    """
    if not text:
        return text
    filtered_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    return filtered_text

# 创建一个自定义回调处理器来过滤输出
class OutputFilterCallbackHandler(BaseCallbackHandler):
    """过滤模型输出的回调处理器"""
    
    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """流式输出时过滤token"""
        pass
    
    def on_llm_end(self, response, **kwargs) -> None:
        """当LLM完成生成时过滤输出"""
        if hasattr(response, 'generations') and response.generations:
            for gen_list in response.generations:
                for gen in gen_list:
                    if hasattr(gen, 'text'):
                        gen.text = filter_think_tags(gen.text)
                    # 处理message类型的输出
                    if hasattr(gen, 'message') and hasattr(gen.message, 'content'):
                        gen.message.content = filter_think_tags(gen.message.content)
    
    def on_chain_end(self, outputs, **kwargs) -> None:
        """当Chain完成时过滤输出"""
        if 'output' in outputs and isinstance(outputs['output'], str):
            outputs['output'] = filter_think_tags(outputs['output'])


class DynamicCustomFunctionCallingAgent(CustomFunctionCallingAgent):
    """动态自定义Function Calling代理，继承CustomFunctionCallingAgent并整合用户配置工具"""

    def __init__(self, llm, output_filter):
        # 获取系统内置工具和用户配置工具
        builtin_functions = self._get_builtin_functions_from_agent_core()
        dynamic_functions = dynamic_tools_manager.get_tools_functions_definition()
        all_functions = builtin_functions + dynamic_functions

        # 调用父类构造函数
        super().__init__(llm, all_functions, output_filter)

        # 保存动态工具管理器的引用
        self.dynamic_tools_manager = dynamic_tools_manager

        logger.info(f"动态Function Calling代理初始化完成，内置工具: {len(builtin_functions)}, 用户工具: {len(dynamic_functions)}")

    def _get_builtin_functions_from_agent_core(self):
        """从agent_core获取系统内置函数定义"""
        return [
            {
                "name": "query_event_surround_resource",
                "description": "查询事件周边的应急资源，距离的单位是公里。resource_type为可选参数，可以是：队伍、仓库、专家、避难场所、医疗卫生、防护目标、企业信息、物资、装备、医院、危险源、视频，或者留空查询所有类型",
                "parameters": {
                    "event_name": "string",
                    "distance": "int",
                    "resource_type": "string|optional"
                }
            },
            {
                "name": "preview_camera",
                "description": "预览摄像头,输入格式: 摄像头ID或名称,例如：camera001",
                "parameters": {
                    "camera_id": "string"
                }
            },
            {
                "name": "one_map_position_resource",
                "description": "一张图查看并定位某资源，参数resource_name为资源名称",
                "parameters": {
                    "resource_name": "string"
                }
            },
            {
                "name": "event_stop_plan",
                "description": "终止事件预案,输入格式: 事件名称 例如: 西兴街道火灾事件",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "event_start_plan",
                "description": "启动事件预案,输入格式: 事件名称,预案名称（为空）,例如: 西兴街道火灾事件",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "query_all_event_infos",
                "description": "搜索事件,输入格式: 页码(默认1,类型是int),每页大小(默认50,类型是int),事件状态(可选,默认为空),事件名称(可选,当为空时查所有事件),事件等级(可选,默认为空)。例如: 1,50,4,洪涝灾害事件,1",
                "parameters": {
                    "page": 1,
                    "page_size": 50
                }
            },
            {
                "name": "search_emergency_plan",
                "description": "搜索应急预案,输入格式: 预案类型（为空时查所有预案）,页码(默认为1),页面大小(默认20),例如: 森林火灾,1,20",
                "parameters": {
                    "page": 1,
                    "page_size": 20
                }
            },
            {
                "name": "query_area_plans",
                "description": "根据区域名称查询预案信息,输入格式: 区域名称（市/区）,例如: 杭州市",
                "parameters": {
                    "area_name": "string"
                }
            },
            {
                "name": "query_resource_cameras",
                "description": "查询资源周边的视频信息,输入格式: 资源名称,资源类型,例如: 某某防汛物资仓库,repository",
                "parameters": {
                    "resource_name": "string",
                    "resource_type": "string"
                }
            },
            {
                "name": "start_call_by_name",
                "description": "根据队伍/人名发起指定的呼叫;resource_type: 资源类型:rescue_team(救援队伍),person(人员);calling_type:0(视频通话),1(电话)",
                "parameters": {
                    "resource_name": "string",
                    "resource_type": "string",
                    "calling_type": "int"
                }
            },
            {
                "name": "query_model_resources",
                "description": "按照事件模型名称搜索附近应急资源,输入格式: 模型名称,例如: 森林火灾模型",
                "parameters": {
                    "model_name": "string"
                }
            },
            {
                "name": "start_real_time_travel",
                "description": "启动一键调度跟进某某事件所有救援队伍实时轨迹，输入格式：事件名称，例如暴雨事件",
                "parameters": {
                    "event_name": "string"
                }
            },
            {
                "name": "start_meeting",
                "description": "开启会议,输入格式: 会议名称,例如: 应急指挥会议",
                "parameters": {
                    "meeting_name": "string"
                }
            },
            {
                "name": "end_meeting",
                "description": "结束会议,输入格式: 会议名称,例如: 应急指挥会议",
                "parameters": {
                    "meeting_name": "string"
                }
            }
        ]

    def _execute_function(self, function_call: dict) -> str:
        """重写执行函数方法，支持内置工具和动态工具"""
        try:
            function_name = function_call.get("function_call", {}).get("name")
            arguments_str = function_call.get("function_call", {}).get("arguments", "{}")

            if not function_name:
                return "错误：未找到函数名称"

            # 解析参数
            try:
                arguments = json.loads(arguments_str) if isinstance(arguments_str, str) else arguments_str
            except json.JSONDecodeError:
                return f"错误：参数解析失败 - {arguments_str}"

            # 检查是否为内置工具（使用父类的function_map）
            if function_name in self.function_map:
                # 使用父类的方法执行内置工具
                return self._convert_and_call_function(function_name, arguments)
            else:
                # 执行用户配置的动态工具
                return self.dynamic_tools_manager.execute_dynamic_tool(function_name, arguments)

        except Exception as e:
            logger.error(f"执行函数时出错: {str(e)}")
            return f"执行函数时出错: {str(e)}"


def create_dynamic_function_calling_agent():
    """
    创建动态Function Calling模式的代理，整合系统内置工具和用户配置工具
    """
    try:
        # 加载配置
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')

        # 初始化LLM（复用agent_core的方法）
        from .agent_core import initialize_llm
        llm = initialize_llm(config)

        # 添加输出过滤回调
        output_filter = OutputFilterCallbackHandler()
        if not hasattr(llm, 'callbacks') or llm.callbacks is None:
            llm.callbacks = [output_filter]
        else:
            llm.callbacks.append(output_filter)

        # 创建动态Function Calling代理执行器
        return DynamicCustomFunctionCallingAgent(llm, output_filter)
    except Exception as e:
        logger.error(f"创建动态Function Calling代理失败: {str(e)}")
        return None


def check_dynamic_tool_trigger(user_input: str) -> bool:
    """
    检查用户输入是否应该触发动态工具调用
    使用改进的关键词组合匹配逻辑
    """
    try:
        # 使用动态工具管理器的新匹配逻辑
        matched_tool = dynamic_tools_manager.check_tool_match(user_input)
        return matched_tool is not None
    except Exception as e:
        logger.error(f"检查动态工具触发失败: {str(e)}")
        return False
