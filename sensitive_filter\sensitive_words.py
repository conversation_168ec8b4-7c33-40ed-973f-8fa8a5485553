#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
敏感词过滤模块
用于检测和过滤用户查询中的敏感词
"""

import re
import os
import json
from enum import Enum
from typing import Tuple, List, Dict, Optional

class SensitivityLevel(Enum):
    """敏感级别枚举"""
    NONE = 0        # 无敏感内容
    MEDIUM = 1      # 中度敏感（如脏话）
    HIGH = 2        # 高度敏感（如政治敏感词）

class SensitiveWordFilter:
    """敏感词过滤器"""
    
    def __init__(self, sensitive_words_file: str = None):
        """
        初始化敏感词过滤器
        
        Args:
            sensitive_words_file: 敏感词配置文件路径，如果为None则使用默认配置
        """
        self.high_risk_words = []
        self.medium_risk_words = []
        
        if sensitive_words_file and os.path.exists(sensitive_words_file):
            self._load_from_file(sensitive_words_file)
        else:
            # 默认的敏感词列表（示例）
            # 实际应用中应该从配置文件加载完整的敏感词列表
            self._load_default_words()
    
    def _load_from_file(self, file_path: str) -> None:
        """从文件加载敏感词列表"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.high_risk_words = data.get('high_risk', [])
                self.medium_risk_words = data.get('medium_risk', [])
        except Exception as e:
            print(f"加载敏感词文件失败: {str(e)}")
            # 加载失败时使用默认配置
            self._load_default_words()
    
    def _load_default_words(self) -> None:
        """加载默认的敏感词列表"""
        # 这里只是示例，实际应用中应该有更完整的列表
        # 高危词（政治敏感词示例）
        self.high_risk_words = [
            "政治敏感词1", "政治敏感词2", "政治敏感词3"
        ]
        
        # 中危词（脏话示例）
        self.medium_risk_words = [
            "脏话1", "脏话2", "脏话3"
        ]
    
    def check_text(self, text: str) -> Tuple[SensitivityLevel, List[str]]:
        """
        检查文本中是否包含敏感词
        
        Args:
            text: 要检查的文本
            
        Returns:
            Tuple[SensitivityLevel, List[str]]: 敏感级别和检测到的敏感词列表
        """
        if not text:
            return SensitivityLevel.NONE, []
        
        # 检查高危词
        high_risk_found = []
        for word in self.high_risk_words:
            if word in text:
                high_risk_found.append(word)
        
        if high_risk_found:
            return SensitivityLevel.HIGH, high_risk_found
        
        # 检查中危词
        medium_risk_found = []
        for word in self.medium_risk_words:
            if word in text:
                medium_risk_found.append(word)
        
        if medium_risk_found:
            return SensitivityLevel.MEDIUM, medium_risk_found
        
        return SensitivityLevel.NONE, []
    
    def get_response_for_sensitive_query(self, level: SensitivityLevel) -> str:
        """
        根据敏感级别返回相应的回复
        
        Args:
            level: 敏感级别
            
        Returns:
            str: 回复内容
        """
        if level == SensitivityLevel.HIGH:
            return "我无法提供相关信息。如果你有其他的问题，我会很乐意为你解答。"
        elif level == SensitivityLevel.MEDIUM:
            return "请使用文明用语。如果你有其他的问题，我会很乐意为你解答。"
        else:
            return ""  # 无敏感内容，返回空字符串

# 创建全局过滤器实例
filter_instance = None

def get_filter_instance(config_file: str = None) -> SensitiveWordFilter:
    """
    获取过滤器实例（单例模式）
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        SensitiveWordFilter: 过滤器实例
    """
    global filter_instance
    if filter_instance is None:
        filter_instance = SensitiveWordFilter(config_file)
    return filter_instance

# 便捷函数
def check_sensitive_words(text: str, config_file: str = None) -> Tuple[SensitivityLevel, List[str]]:
    """
    检查文本中是否包含敏感词
    
    Args:
        text: 要检查的文本
        config_file: 配置文件路径
        
    Returns:
        Tuple[SensitivityLevel, List[str]]: 敏感级别和检测到的敏感词列表
    """
    filter_instance = get_filter_instance(config_file)
    return filter_instance.check_text(text)

def get_response_for_sensitive_query(level: SensitivityLevel) -> str:
    """
    根据敏感级别返回相应的回复
    
    Args:
        level: 敏感级别
        
    Returns:
        str: 回复内容
    """
    filter_instance = get_filter_instance()
    return filter_instance.get_response_for_sensitive_query(level) 