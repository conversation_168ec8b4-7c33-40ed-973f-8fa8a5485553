#!/usr/bin/env python3
"""
测试动态代理改进功能
验证关键词组合匹配、系统工具整合和代码复用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agent.dynamic_tools_manager import DynamicToolsManager
from src.agent.dynamic_agent_core import create_dynamic_function_calling_agent, check_dynamic_tool_trigger

def test_keyword_combinations():
    """测试关键词组合匹配功能"""
    print("=== 测试关键词组合匹配功能 ===")
    
    # 创建测试用的工具管理器
    manager = DynamicToolsManager()
    
    # 测试不同的关键词组合格式
    test_cases = [
        # 格式：(关键词配置, 用户输入, 期望结果)
        ("事件,周边", "查询事件周边资源", True),
        ("事件,周边", "查询事件信息", False),
        ("事件,周边;事件,附近", "查询事件附近的资源", True),
        ("事件,周边;事件,附近", "查询事件周边的资源", True),
        ("事件,周边;事件,附近", "查询资源信息", False),
        ("查询", "查询事件信息", True),
        ("查询;搜索", "搜索资源", True),
    ]
    
    for keywords_str, user_input, expected in test_cases:
        result = manager._match_keyword_combinations(user_input.lower(), keywords_str)
        status = "✓" if result == expected else "✗"
        print(f"{status} 关键词: '{keywords_str}' | 输入: '{user_input}' | 期望: {expected} | 实际: {result}")
    
    print()

def test_dynamic_agent_creation():
    """测试动态代理创建"""
    print("=== 测试动态代理创建 ===")
    
    try:
        agent = create_dynamic_function_calling_agent()
        if agent:
            print("✓ 动态代理创建成功")
            
            # 检查是否包含系统内置工具
            builtin_tools = [func for func in agent.functions if func['name'] in [
                'query_event_surround_resource', 'preview_camera', 'start_meeting'
            ]]
            print(f"✓ 包含系统内置工具数量: {len(builtin_tools)}")
            
            # 检查是否包含动态工具（如果有的话）
            dynamic_tools = [func for func in agent.functions if func['name'] not in agent.function_map]
            print(f"✓ 包含动态工具数量: {len(dynamic_tools)}")
            
            print(f"✓ 总工具数量: {len(agent.functions)}")
        else:
            print("✗ 动态代理创建失败")
    except Exception as e:
        print(f"✗ 动态代理创建异常: {str(e)}")
    
    print()

def test_inheritance():
    """测试继承关系"""
    print("=== 测试继承关系 ===")
    
    try:
        from src.agent.dynamic_agent_core import DynamicCustomFunctionCallingAgent
        from src.agent.agent_core import CustomFunctionCallingAgent
        
        # 检查继承关系
        is_subclass = issubclass(DynamicCustomFunctionCallingAgent, CustomFunctionCallingAgent)
        print(f"✓ DynamicCustomFunctionCallingAgent 继承自 CustomFunctionCallingAgent: {is_subclass}")
        
        # 检查方法复用
        agent = create_dynamic_function_calling_agent()
        if agent:
            has_parent_methods = (
                hasattr(agent, '_convert_and_call_function') and
                hasattr(agent, '_create_function_calling_prompt') and
                hasattr(agent, '_parse_function_call')
            )
            print(f"✓ 复用父类方法: {has_parent_methods}")
        
    except Exception as e:
        print(f"✗ 继承关系测试异常: {str(e)}")
    
    print()

def test_tool_trigger_detection():
    """测试工具触发检测"""
    print("=== 测试工具触发检测 ===")
    
    # 模拟一些用户输入
    test_inputs = [
        "查询事件周边资源",
        "启动会议",
        "今天天气怎么样",
        "事件附近有什么资源",
        "普通聊天内容"
    ]
    
    for user_input in test_inputs:
        should_trigger = check_dynamic_tool_trigger(user_input)
        print(f"输入: '{user_input}' | 触发动态工具: {should_trigger}")
    
    print()

def main():
    """主测试函数"""
    print("开始测试动态代理改进功能...\n")
    
    test_keyword_combinations()
    test_dynamic_agent_creation()
    test_inheritance()
    test_tool_trigger_detection()
    
    print("测试完成！")

if __name__ == "__main__":
    main()
