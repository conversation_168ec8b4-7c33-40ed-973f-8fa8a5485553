<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反馈数据管理 - 消防智能体系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/feedback.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="/static/css/admin_feedback.css">
</head>
<body>
    <div class="container">
        <header class="admin-header">
            <div class="logo">
                <img src="/static/images/yingji.png" alt="消防智能体系统" class="logo-img">
                <h1>消防指挥智能体 - 反馈数据管理</h1>
            </div>
            <div class="admin-actions">
                <a href="/" class="btn btn-secondary"><i class="fas fa-home"></i> 返回主页</a>
            </div>
        </header>

        <main class="admin-main">
            <div class="filter-section">
                <h2>筛选条件</h2>
                <div class="filter-controls">
                    <div class="filter-group">
                        <label for="rating-filter">评分筛选:</label>
                        <select id="rating-filter" class="filter-select">
                            <option value="">全部评分</option>
                            <option value="1">正面评价</option>
                            <option value="0">负面评价</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="deleted-filter">显示已删除:</label>
                        <input type="checkbox" id="deleted-filter" class="filter-checkbox">
                    </div>
                    <div class="filter-group">
                        <button id="apply-filters" class="btn btn-primary">应用筛选</button>
                        <button id="reset-filters" class="btn btn-secondary">重置筛选</button>
                    </div>
                </div>
            </div>

            <div class="data-section">
                <h2>反馈数据列表 <span id="total-count">(0)</span></h2>
                <div class="data-controls">
                    <div class="pagination-info">
                        显示 <span id="showing-from">0</span> - <span id="showing-to">0</span> 条，共 <span id="total-items">0</span> 条
                    </div>
                    <div class="page-size-control">
                        每页显示:
                        <select id="page-size" class="page-size-select">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>

                <div class="feedback-list" id="feedback-list">
                    <!-- 数据将通过JavaScript动态加载 -->
                    <div class="loading-indicator">
                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                    </div>
                </div>

                <div class="pagination-controls">
                    <button id="prev-page" class="btn btn-secondary" disabled><i class="fas fa-chevron-left"></i> 上一页</button>
                    <span class="page-info">第 <span id="current-page">1</span> 页，共 <span id="total-pages">1</span> 页</span>
                    <button id="next-page" class="btn btn-secondary" disabled>下一页 <i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        </main>

        <footer class="admin-footer">
            <p>&copy; 2024 消防智能体系统 - 管理员界面</p>
        </footer>
    </div>

    <!-- 反馈详情模态框 -->
    <div id="feedback-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>反馈详情</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="feedback-detail">
                    <div class="detail-group">
                        <h3>用户问题</h3>
                        <div id="modal-user-message" class="detail-content"></div>
                    </div>
                    <div class="detail-group">
                        <h3>系统回答</h3>
                        <div id="modal-assistant-response" class="detail-content"></div>
                    </div>
                    <div class="detail-group">
                        <h3>思考过程</h3>
                        <div id="modal-thinking-process" class="detail-content"></div>
                    </div>
                    <div class="detail-group">
                        <h3>参考文档</h3>
                        <div id="modal-source-documents" class="detail-content"></div>
                    </div>
                    <div class="detail-group">
                        <h3>用户评价</h3>
                        <div class="rating-display">
                            <span id="modal-rating" class="rating-badge"></span>
                            <span id="modal-created-at" class="timestamp"></span>
                        </div>
                        <div id="modal-comment" class="comment-content"></div>
                    </div>
                    <div class="detail-group">
                        <h3>会话信息</h3>
                        <div class="session-info">
                            <p>会话ID: <span id="modal-session-id"></span></p>
                            <p>消息ID: <span id="modal-message-id"></span></p>
                            <p>状态: <span id="modal-status"></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/admin_feedback.js"></script>
</body>
</html>
