// 加载推荐问题，推荐问题功能
function loadRecommendedQuestions() {
  const recommendedQuestions = document.getElementById('recommended-questions');
  if (!recommendedQuestions) return;

  // 清空容器
  recommendedQuestions.innerHTML = '';

  // 知识库问题
  const knowledgeQuestions = [
    '高层建筑火灾扑救的关键点是什么？',
    '消防指挥中心的主要职责有哪些？',
    '如何处理化学品泄漏引发的火灾？'
  ];

  // 智能体问题
  const agentQuestions = [
    '杭州暴雨事件5公里范围内的应急资源？',
    '发生了杭州暴雨事件，请你告诉我5公里内的应急资源？',
    '帮我查找下杭州暴雨事件详情。',
    '杭州暴雨事件启动预案。',
    '杭州暴雨事件终止预案。',
    '一张图查看防汛抗旱物资储备库。',
    '查询森林火灾预案。',
    '查询所有事件。',
    '预览摄像头138-Camera 01'
  ];

  // 创建知识库问题区域
  const knowledgeContainer = document.createElement('div');
  knowledgeContainer.className = 'question-category';
  
  // 添加知识库标题
  const knowledgeTitle = document.createElement('div');
  knowledgeTitle.className = 'category-title';
  knowledgeTitle.innerHTML = '<i class="fas fa-book"></i> 知识库问题';
  knowledgeContainer.appendChild(knowledgeTitle);

  // 添加知识库问题
  knowledgeQuestions.forEach(question => {
    const questionItem = document.createElement('div');
    questionItem.className = 'question-item knowledge-question';
    questionItem.textContent = question;

    questionItem.addEventListener('click', () => {
      const messageInput = document.getElementById('message-input');
      if (messageInput) {
        messageInput.value = question;
        messageInput.focus();
      }
    });

    knowledgeContainer.appendChild(questionItem);
  });

  // 创建智能体问题区域
  const agentContainer = document.createElement('div');
  agentContainer.className = 'question-category';
  
  // 添加智能体标题
  const agentTitle = document.createElement('div');
  agentTitle.className = 'category-title';
  agentTitle.innerHTML = '<i class="fas fa-robot"></i> 智能体问题';
  agentContainer.appendChild(agentTitle);

  // 添加智能体问题
  agentQuestions.forEach(question => {
    const questionItem = document.createElement('div');
    questionItem.className = 'question-item agent-question';
    questionItem.textContent = question;

    questionItem.addEventListener('click', () => {
      const messageInput = document.getElementById('message-input');
      if (messageInput) {
        messageInput.value = question;
        messageInput.focus();
      }
    });

    agentContainer.appendChild(questionItem);
  });

  // 将两个分类添加到推荐问题区域
  recommendedQuestions.appendChild(knowledgeContainer);
  recommendedQuestions.appendChild(agentContainer);
}
