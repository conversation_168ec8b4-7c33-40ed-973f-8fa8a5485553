# 久安大模型 API工具调用文档

## 文档概述

本文档详细介绍了久安大模型系统中所有可用的API工具，包括工具名称、URL、参数、返回值等信息，旨在为Cursor等代码生成工具提供完整的Function Calling配置参考。

## 目录

1. [工具概览](#工具概览)
2. [静态API工具详情](#静态api工具详情)
3. [动态工具管理系统](#动态工具管理系统)
4. [参数提取器](#参数提取器)
5. [混淆分析及优化建议](#混淆分析及优化建议)
6. [Function Calling配置](#function-calling配置)
7. [使用示例](#使用示例)

## 工具概览

### 静态API工具（14个）

| 序号 | 工具名称 | 功能描述 | API端点 |
|------|----------|----------|----------|
| 1 | query_event_surround_resource | 查询事件周边资源 | queryEventSurroundResource |
| 2 | preview_camera | 预览摄像头 | oneMapPreviewCameraByName |
| 3 | one_map_position_resource | 一张图查看并定位资源 | oneMapPositionResourceByName |
| 4 | event_stop_plan | 终止事件预案 | stopEventPlan |
| 5 | event_start_plan | 启动事件预案 | startEventPlan |
| 6 | query_all_event_infos | 查询所有事件信息 | queryAllEventInfos |
| 7 | search_emergency_plan | 搜索应急预案 | queryPlanInfo |
| 8 | query_area_plans | 根据区域名称查询预案信息 | queryPlanInfosByAreaName |
| 9 | query_resource_cameras | 查询资源周边的视频信息 | queryResourceArroundCameraInfos |
| 10 | start_call_by_name | 根据队伍/人名发起指定的呼叫 | startVideoOrPhoneCallByName |
| 11 | query_model_resources | 按照事件模型名称搜索附近应急资源 | queryArroundResourceByModelName |
| 12 | start_real_time_travel | 启动一键调度跟进救援队伍实时轨迹 | followUpEventAllTeamRealTimeTravel |
| 13 | start_meeting | 开启会议 | startPlanMeetingByPlanGroupName |
| 14 | end_meeting | 结束会议 | endPlanMeetingByPlanGroupName |

### 动态工具管理系统

系统还包含一个动态工具管理系统，支持从数据库动态加载和执行用户配置的API工具。

## 静态API工具详情

### 1. query_event_surround_resource - 查询事件周边资源

**功能描述：** 查询指定事件周边范围内的应急资源

**API端点：** `queryEventSurroundResource`

**请求方法：** POST

**参数格式：** `事件名称,距离,资源类型(可选)`

**参数详情：**
- `eventName` (string, 必填): 事件名称
- `arroundDistance` (int, 必填): 查询距离（公里）
- `resourceType` (string, 可选): 资源类型过滤

**资源类型映射：**
```python
{
    "rescue_team": "救援队伍",
    "repository": "物资仓库", 
    "medical_health": "医疗健康机构",
    "protection_target": "保护目标",
    "emergency_expert": "应急专家",
    "material": "应急物资",
    "equipment": "应急设备",
    "common": "通用设施",
    "enterpriseInfo": "企业信息",
    "refuge": "避难场所",
    "hospital": "医院",
    "hazard": "危险源",
    "camera": "监控设备",
    "video_terminal": "视频监控",
    "monitor": "监控点位",
    "person": "人员",
    "guaranteeEquipment": "保障装备",
    "communicationSupportTeam": "通信保障队伍",
    "other": "其他资源"
}
```

**返回值格式：**
```
事件名称X公里范围内的应急资源包括：

救援队伍:
- XX救援队伍，距离X公里，联系电话XXX

物资仓库:
- XX仓库，距离X公里，联系电话XXX
...
```

**使用示例：**
```
输入: "西兴街道火灾事件,5,救援队伍"
输出: "西兴街道火灾事件5公里范围内的应急资源包括：\n\n救援队伍:\n- XX救援队伍，距离2.3公里，联系电话13800138000"
```

### 2. preview_camera - 预览摄像头

**功能描述：** 开启指定摄像头的预览功能

**API端点：** `oneMapPreviewCameraByName`

**请求方法：** POST

**参数格式：** `摄像头ID`

**参数详情：**
- `cameraName` (string, 必填): 摄像头标识符

**返回值格式：**
```
"已成功开启 [摄像头ID] 摄像头预览"
```

**使用示例：**
```
输入: "camera001"
输出: "已成功开启 camera001 摄像头预览"
```

### 3. one_map_position_resource - 一张图查看并定位资源

**功能描述：** 在地图上查看并定位指定资源

**API端点：** `oneMapPositionResourceByName`

**请求方法：** POST

**参数格式：** `资源名称`

**参数详情：**
- `resourceName` (string, 必填): 资源名称
- `resourceType` (string, 可选): 资源类型，默认为空

**返回值格式：**
```
"[资源名称]成功定位！"
```

**使用示例：**
```
输入: "防汛抗旱物资储备库"
输出: "防汛抗旱物资储备库成功定位！"
```

### 4. event_stop_plan - 终止事件预案

**功能描述：** 终止指定事件的预案

**API端点：** `stopEventPlan`

**请求方法：** POST

**参数格式：** `事件名称,预案名称`

**参数详情：**
- `eventName` (string, 必填): 事件名称
- `planName` (string, 可选): 预案名称

**返回值格式：**
```
"已成功终止事件[事件名称]的[预案名称]预案"
```

**使用示例：**
```
输入: "西兴街道火灾事件,消防预案"
输出: "已成功终止事件西兴街道火灾事件的消防预案"
```

### 5. event_start_plan - 启动事件预案

**功能描述：** 启动指定事件的预案

**API端点：** `startEventPlan`

**请求方法：** POST

**参数格式：** `事件名称,预案名称`

**参数详情：**
- `eventName` (string, 必填): 事件名称
- `planName` (string, 可选): 预案名称

**返回值格式：**
```
"已成功启动事件[事件名称]的[预案名称]预案"
```

**使用示例：**
```
输入: "西兴街道火灾事件,消防预案"
输出: "已成功启动事件西兴街道火灾事件的消防预案"
```

### 6. query_all_event_infos - 查询所有事件信息

**功能描述：** 查询系统中的所有事件信息

**API端点：** `queryAllEventInfos`

**请求方法：** POST

**参数格式：** `页码,页大小,事件状态,事件名称,事件等级`

**参数详情：**
- `pageNo` (string, 可选, 默认"1"): 页码
- `pageSize` (string, 可选, 默认"150"): 页大小
- `eventStatus` (string, 可选): 事件状态
- `eventName` (string, 可选): 事件名称
- `eventLevel` (string, 可选): 事件等级

**事件状态映射：**
```python
{
    "处理中": "4",
    "待处理": "3", 
    "已归档": "5",
    "已完成": "5"
}
```

**返回值格式：**
```
当前系统中共有X个事件，以下是所有事件的信息：

1. 事件名称：XX事件
- 事件类型：XX
- 事件子类型：XX
- 事件等级：X
- 事件状态：XX
- 经纬度：X, X
...
```

**使用示例：**
```
输入: "1,50,4,暴雨事件,2"
输出: "当前系统中共有10个事件，以下是所有事件的信息：\n\n1. 事件名称：暴雨事件\n- 事件类型：自然灾害\n- 事件等级：2\n- 事件状态：处理中"
```

### 7. search_emergency_plan - 搜索应急预案

**功能描述：** 搜索系统中的应急预案

**API端点：** `queryPlanInfo`

**请求方法：** POST

**参数格式：** `预案类型,页码,页面大小`

**参数详情：**
- `planTypeName` (string, 可选): 预案类型
- `pageNo` (string, 可选, 默认"1"): 页码
- `pageSize` (string, 可选, 默认"120"): 页面大小

**返回值格式：**
```
当前系统中共有X个预案，以下是所有预案的信息：

1. 预案名称：XX预案
- 预案类型：XX
- 预案状态：XX
- 预案标签：XX
...
```

**使用示例：**
```
输入: "森林火灾,1,20"
输出: "当前系统中共有5个预案，以下是所有预案的信息：\n\n1. 预案名称：森林火灾应急预案\n- 预案类型：森林火灾\n- 预案状态：已审核"
```

### 8. query_area_plans - 根据区域名称查询预案信息

**功能描述：** 查询指定区域的应急预案

**API端点：** `queryPlanInfosByAreaName`

**请求方法：** POST

**参数格式：** `区域名称`

**参数详情：**
- `areaName` (string, 必填): 区域名称（市/区）

**返回值格式：**
```
[区域名称]共有X个预案，以下是预案信息：

1. 预案名称：XX预案
- 预案类型：XX
- 预案状态：XX
- 预案标签：XX
...
```

**使用示例：**
```
输入: "杭州市"
输出: "杭州市共有15个预案，以下是预案信息：\n\n1. 预案名称：杭州市防汛应急预案\n- 预案类型：防汛\n- 预案状态：已审核"
```

### 9. query_resource_cameras - 查询资源周边的视频信息

**功能描述：** 查询指定资源周边的视频监控设备

**API端点：** `queryResourceArroundCameraInfos`

**请求方法：** POST

**参数格式：** `资源名称,资源类型`

**参数详情：**
- `resourceName` (string, 必填): 资源名称
- `resourceType` (string, 必填): 资源类型

**有效资源类型：**
```python
{
    "protection_target": "防护目标",
    "rescue_team": "救援队伍", 
    "repository": "应急仓库",
    "enterpriseInfo": "企业",
    "person": "人员",
    "refuge": "避难场所",
    "medical_health": "医疗卫生"
}
```

**返回值格式：**
```
已找到[资源类型][资源名称]周边的X个监控点，并已打开所有监控画面。

1. 监控点名称
   ID: XXX
...
```

**使用示例：**
```
输入: "防汛物资仓库,repository"
输出: "已找到应急仓库防汛物资仓库周边的3个监控点，并已打开所有监控画面。\n\n1. 入口监控\n   ID: CAM001"
```

### 10. start_call_by_name - 根据队伍/人名发起指定的呼叫

**功能描述：** 向指定的人员或救援队伍发起视频或电话呼叫

**API端点：** `startVideoOrPhoneCallByName`

**请求方法：** POST

**参数格式：** `资源名称,资源类型,呼叫类型`

**参数详情：**
- `resourceName` (string, 必填): 资源名称（人员姓名或队伍名称）
- `resourceType` (string, 必填): 资源类型
- `callingType` (string, 必填): 呼叫类型

**有效资源类型：**
- `rescue_team`: 救援队伍
- `person`: 人员

**呼叫类型：**
- `0`: 视频通话
- `1`: 电话通话

**返回值格式：**
```
"已成功为[资源类型] [资源名称] 发起[呼叫类型]"
```

**使用示例：**
```
输入: "李四,person,0"
输出: "已成功为人员 李四 发起视频通话"
```

### 11. query_model_resources - 按照事件模型名称搜索附近应急资源

**功能描述：** 根据事件模型名称搜索周边的应急资源

**API端点：** `queryArroundResourceByModelName`

**请求方法：** POST

**参数格式：** `模型名称`

**参数详情：**
- `modelName` (string, 必填): 事件模型名称

**返回值格式：**
```
已成功查询到[模型名称]模型信息:
- 模型描述: XXX
- 模型中心位置: 经度X, 纬度X
- 包含X个图层

系统已启动对该模型周边应急资源的查询。请查看地图上的标注，查看该模型周边的应急资源分布情况。
```

**使用示例：**
```
输入: "森林火灾模型"
输出: "已成功查询到森林火灾模型模型信息:\n- 模型描述: 森林火灾应急处置模型\n- 模型中心位置: 经度120.15, 纬度30.28\n- 包含3个图层\n\n系统已启动对该模型周边应急资源的查询..."
```

### 12. start_real_time_travel - 启动一键调度跟进救援队伍实时轨迹

**功能描述：** 启动一键调度功能，跟进所有救援队伍的实时轨迹

**API端点：** `followUpEventAllTeamRealTimeTravel`

**请求方法：** POST

**参数格式：** `事件名称(可选)`

**参数详情：**
- `eventName` (string, 可选): 事件名称

**返回值格式：**
```
"已成功启动一键调度，正在跟进所有救援队伍的实时轨迹"
```

**使用示例：**
```
输入: "西兴街道火灾事件"
输出: "已成功启动一键调度，正在跟进所有救援队伍的实时轨迹"
```

### 13. start_meeting - 开启会议

**功能描述：** 开启指定名称的会议

**API端点：** `startPlanMeetingByPlanGroupName`

**请求方法：** POST

**参数格式：** `会议名称`

**参数详情：**
- `meetingName` (string, 必填): 会议名称

**返回值格式：**
```
"已成功开启[会议名称]会议"
```

**使用示例：**
```
输入: "应急指挥会议"
输出: "已成功开启应急指挥会议会议"
```

### 14. end_meeting - 结束会议

**功能描述：** 结束指定名称的会议

**API端点：** `endPlanMeetingByPlanGroupName`

**请求方法：** POST

**参数格式：** `会议名称`

**参数详情：**
- `meetingName` (string, 必填): 会议名称

**返回值格式：**
```
"已成功结束[会议名称]会议"
```

**使用示例：**
```
输入: "应急指挥会议"
输出: "已成功结束应急指挥会议会议"
```

## 动态工具管理系统

### DynamicToolsManager 类

动态工具管理器支持从数据库动态加载和执行用户配置的API工具。

**主要功能：**
1. 从数据库加载工具配置
2. 自动构建Function Calling定义
3. 支持关键词匹配工具选择
4. 动态执行API调用

**工具配置结构：**
```python
{
    "tool_name_en": "工具英文名称",
    "description": "工具描述",
    "keywords": "关键词1,关键词2,关键词3",
    "post_path": "/api/endpoint",
    "request_params": {
        "param1": {
            "type": "string",
            "default": "默认值",
            "description": "参数描述"
        }
    },
    "response_params": {
        "field1": {
            "description": "字段描述"
        }
    }
}
```

**核心方法：**
- `get_tools_functions_definition()`: 获取Function Calling定义
- `check_tool_match(user_input)`: 检查用户输入匹配的工具
- `execute_dynamic_tool(tool_name, arguments)`: 执行动态工具

## 参数提取器

系统包含智能参数提取器，能够从自然语言输入中提取API调用所需的参数。

### 主要提取器

1. **extract_event_params** - 提取事件相关参数
   - 支持多种表达方式：`XX事件X公里范围内`、`XX事件附近X公里`等
   - 自动识别事件名称、距离、资源类型

2. **extract_camera_params** - 提取摄像头ID
   - 支持格式：`数字-Camera 数字`、`摄像头ID`、`Camera ID`等
   - 智能清理和规范化ID格式

3. **extract_resource_params** - 提取资源名称
   - 支持完整资源名称匹配
   - 智能识别资源类型

4. **extract_plan_params** - 提取预案类型
   - 支持多种查询表达方式
   - 自动识别预案类型关键词

5. **extract_call_params** - 提取呼叫参数
   - 智能识别呼叫类型（视频/电话）
   - 区分人员和救援队伍类型

6. **extract_model_name** - 提取模型名称
   - 支持多种模型表达方式
   - 自动添加"模型"后缀

7. **extract_real_time_travel** - 提取实时轨迹参数
   - 关键词组合识别
   - 事件名称提取

8. **extract_meeting_name** - 提取会议名称
   - 支持多种会议表达方式
   - 常见会议类型识别

## 混淆分析及优化建议

### 潜在混淆点分析

1. **功能相似的工具**
   - `query_event_surround_resource` vs `query_model_resources`
   - `search_emergency_plan` vs `query_area_plans`
   - `start_meeting` vs `end_meeting`

2. **参数格式不一致**
   - 部分工具使用逗号分隔的字符串参数
   - 部分工具支持可选参数的位置变化

3. **命名规范不统一**
   - 部分工具使用驼峰命名
   - 部分工具使用下划线命名

### 优化建议

1. **工具命名规范化**
   ```python
   # 建议的命名规范
   query_event_surrounding_resources  # 替代 query_event_surround_resource
   query_model_surrounding_resources   # 替代 query_model_resources
   search_emergency_plans             # 替代 search_emergency_plan
   query_area_emergency_plans         # 替代 query_area_plans
   ```

2. **参数格式统一化**
   ```python
   # 建议使用JSON格式参数
   {
       "event_name": "事件名称",
       "distance": 5,
       "resource_type": "rescue_team"
   }
   ```

3. **工具描述优化**
   - 添加更详细的使用场景说明
   - 明确参数约束和可选性
   - 提供更多示例

4. **功能分组优化**
   ```python
   # 按功能域分组
   RESOURCE_TOOLS = [
       "query_event_surrounding_resources",
       "query_model_surrounding_resources", 
       "one_map_position_resource"
   ]
   
   PLAN_TOOLS = [
       "search_emergency_plans",
       "query_area_emergency_plans",
       "event_start_plan",
       "event_stop_plan"
   ]
   
   COMMUNICATION_TOOLS = [
       "start_call_by_name",
       "start_meeting",
       "end_meeting"
   ]
   ```

## Function Calling配置

### 完整的Function Calling Schema

```json
{
  "functions": [
    {
      "name": "query_event_surround_resource",
      "description": "查询指定事件周边范围内的应急资源，支持按资源类型过滤",
      "parameters": {
        "type": "object",
        "properties": {
          "event_name": {
            "type": "string",
            "description": "事件名称，例如：西兴街道火灾事件"
          },
          "distance": {
            "type": "integer",
            "description": "查询距离（公里），例如：5"
          },
          "resource_type": {
            "type": "string",
            "description": "可选参数，资源类型，例如：rescue_team（救援队伍）、repository（物资仓库）等",
            "enum": ["rescue_team", "repository", "medical_health", "protection_target", "emergency_expert", "material", "equipment", "common", "enterpriseInfo", "refuge", "hospital", "hazard", "camera", "video_terminal", "monitor", "person", "guaranteeEquipment", "communicationSupportTeam", "other"]
          }
        },
        "required": ["event_name", "distance"]
      }
    },
    {
      "name": "preview_camera",
      "description": "开启指定摄像头的预览功能",
      "parameters": {
        "type": "object",
        "properties": {
          "camera_id": {
            "type": "string",
            "description": "摄像头标识符，例如：camera001或1-Camera 1"
          }
        },
        "required": ["camera_id"]
      }
    },
    {
      "name": "one_map_position_resource",
      "description": "在地图上查看并定位指定资源",
      "parameters": {
        "type": "object",
        "properties": {
          "resource_name": {
            "type": "string",
            "description": "资源名称，例如：防汛抗旱物资储备库"
          },
          "resource_type": {
            "type": "string",
            "description": "可选参数，资源类型，默认为空"
          }
        },
        "required": ["resource_name"]
      }
    },
    {
      "name": "event_start_plan",
      "description": "启动指定事件的应急预案",
      "parameters": {
        "type": "object",
        "properties": {
          "event_name": {
            "type": "string",
            "description": "事件名称，例如：西兴街道火灾事件"
          },
          "plan_name": {
            "type": "string",
            "description": "可选参数，预案名称，例如：消防预案"
          }
        },
        "required": ["event_name"]
      }
    },
    {
      "name": "event_stop_plan",
      "description": "终止指定事件的应急预案",
      "parameters": {
        "type": "object",
        "properties": {
          "event_name": {
            "type": "string",
            "description": "事件名称，例如：西兴街道火灾事件"
          },
          "plan_name": {
            "type": "string",
            "description": "可选参数，预案名称，例如：消防预案"
          }
        },
        "required": ["event_name"]
      }
    },
    {
      "name": "query_all_event_infos",
      "description": "查询系统中的所有事件信息，支持分页和条件过滤",
      "parameters": {
        "type": "object",
        "properties": {
          "page_no": {
            "type": "string",
            "description": "可选参数，页码，默认为1"
          },
          "page_size": {
            "type": "string",
            "description": "可选参数，页大小，默认为150"
          },
          "event_status": {
            "type": "string",
            "description": "可选参数，事件状态，例如：4（处理中）、3（待处理）、5（已归档）"
          },
          "event_name": {
            "type": "string",
            "description": "可选参数，事件名称过滤"
          },
          "event_level": {
            "type": "string",
            "description": "可选参数，事件等级，例如：1、2、3、4、5"
          }
        },
        "required": []
      }
    },
    {
      "name": "search_emergency_plan",
      "description": "搜索系统中的应急预案",
      "parameters": {
        "type": "object",
        "properties": {
          "plan_type_name": {
            "type": "string",
            "description": "可选参数，预案类型，例如：森林火灾"
          },
          "page_no": {
            "type": "string",
            "description": "可选参数，页码，默认为1"
          },
          "page_size": {
            "type": "string",
            "description": "可选参数，页面大小，默认为120"
          }
        },
        "required": []
      }
    },
    {
      "name": "query_area_plans",
      "description": "查询指定区域的应急预案",
      "parameters": {
        "type": "object",
        "properties": {
          "area_name": {
            "type": "string",
            "description": "区域名称（市/区），例如：杭州市"
          }
        },
        "required": ["area_name"]
      }
    },
    {
      "name": "query_resource_cameras",
      "description": "查询指定资源周边的视频监控设备",
      "parameters": {
        "type": "object",
        "properties": {
          "resource_name": {
            "type": "string",
            "description": "资源名称，例如：防汛物资仓库"
          },
          "resource_type": {
            "type": "string",
            "description": "资源类型",
            "enum": ["protection_target", "rescue_team", "repository", "enterpriseInfo", "person", "refuge", "medical_health"]
          }
        },
        "required": ["resource_name", "resource_type"]
      }
    },
    {
      "name": "start_call_by_name",
      "description": "向指定的人员或救援队伍发起视频或电话呼叫",
      "parameters": {
        "type": "object",
        "properties": {
          "resource_name": {
            "type": "string",
            "description": "资源名称（人员姓名或队伍名称），例如：李四"
          },
          "resource_type": {
            "type": "string",
            "description": "资源类型",
            "enum": ["rescue_team", "person"]
          },
          "calling_type": {
            "type": "string",
            "description": "呼叫类型：0（视频通话）、1（电话通话）",
            "enum": ["0", "1"]
          }
        },
        "required": ["resource_name", "resource_type", "calling_type"]
      }
    },
    {
      "name": "query_model_resources",
      "description": "根据事件模型名称搜索周边的应急资源",
      "parameters": {
        "type": "object",
        "properties": {
          "model_name": {
            "type": "string",
            "description": "事件模型名称，例如：森林火灾模型"
          }
        },
        "required": ["model_name"]
      }
    },
    {
      "name": "start_real_time_travel",
      "description": "启动一键调度功能，跟进所有救援队伍的实时轨迹",
      "parameters": {
        "type": "object",
        "properties": {
          "event_name": {
            "type": "string",
            "description": "可选参数，事件名称，例如：西兴街道火灾事件"
          }
        },
        "required": []
      }
    },
    {
      "name": "start_meeting",
      "description": "开启指定名称的会议",
      "parameters": {
        "type": "object",
        "properties": {
          "meeting_name": {
            "type": "string",
            "description": "会议名称，例如：应急指挥会议"
          }
        },
        "required": ["meeting_name"]
      }
    },
    {
      "name": "end_meeting",
      "description": "结束指定名称的会议",
      "parameters": {
        "type": "object",
        "properties": {
          "meeting_name": {
            "type": "string",
            "description": "会议名称，例如：应急指挥会议"
          }
        },
        "required": ["meeting_name"]
      }
    }
  ]
}
```

## 使用示例

### Python集成示例

```python
import json
import requests
from typing import Dict, Any

class EmergencyAPIManager:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.token = token
        self.headers = {
            "Content-Type": "application/json",
            "Token": token
        }
    
    def query_event_surround_resource(self, event_name: str, distance: int, resource_type: str = "") -> str:
        """查询事件周边资源"""
        url = f"{self.base_url}/queryEventSurroundResource"
        payload = {
            "eventName": event_name,
            "arroundDistance": distance,
            "resourceType": resource_type
        }
        response = requests.post(url, json=payload, headers=self.headers, verify=False)
        return response.json()
    
    def preview_camera(self, camera_id: str) -> str:
        """预览摄像头"""
        url = f"{self.base_url}/oneMapPreviewCameraByName"
        payload = {"cameraName": camera_id}
        response = requests.post(url, json=payload, headers=self.headers, verify=False)
        return response.json()
    
    # 其他工具方法...

# 使用示例
api_manager = EmergencyAPIManager(
    base_url="https://************",
    token="your_token_here"
)

# 查询事件周边资源
result = api_manager.query_event_surround_resource(
    event_name="西兴街道火灾事件",
    distance=5,
    resource_type="rescue_team"
)
print(result)
```

### Function Calling集成示例

```python
# OpenAI Function Calling示例
import openai

client = openai.OpenAI()

functions = [
    {
        "name": "query_event_surround_resource",
        "description": "查询指定事件周边范围内的应急资源",
        "parameters": {
            "type": "object",
            "properties": {
                "event_name": {"type": "string", "description": "事件名称"},
                "distance": {"type": "integer", "description": "查询距离（公里）"},
                "resource_type": {"type": "string", "description": "资源类型（可选）"}
            },
            "required": ["event_name", "distance"]
        }
    }
    # 其他工具定义...
]

response = client.chat.completions.create(
    model="gpt-4",
    messages=[{"role": "user", "content": "查询西兴街道火灾事件5公里范围内的救援队伍"}],
    functions=functions,
    function_call="auto"
)

if response.choices[0].message.function_call:
    function_name = response.choices[0].message.function_call.name
    arguments = json.loads(response.choices[0].message.function_call.arguments)
    
    # 执行对应的API调用
    result = api_manager.query_event_surround_resource(**arguments)
    print(result)
```

## 注意事项

1. **API认证**: 所有API调用都需要在请求头中包含Token认证信息
2. **SSL验证**: 当前系统使用`verify=False`禁用SSL验证，生产环境应配置 proper SSL证书
3. **参数验证**: 调用前应验证参数的有效性，特别是枚举类型参数
4. **错误处理**: 建议实现完善的错误处理机制，处理网络异常和API错误响应
5. **日志记录**: 建议记录API调用日志，便于调试和监控

## 更新日志

- **V1.0.0**: 初始版本，包含14个核心API工具
- **V1.1.0**: 添加动态工具管理系统支持
- **V1.2.0**: 优化参数提取器，提升自然语言理解能力
- **V1.2.3**: 完善工具文档，添加Function Calling配置示例

