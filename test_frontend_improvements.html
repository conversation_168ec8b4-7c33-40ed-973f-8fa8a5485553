<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端改进测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #2ecc71; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .param-item {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }
        .param-item input, .param-item select {
            flex: 1;
        }
        .help-text {
            margin-top: 8px;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-size: 13px;
            color: #495057;
            line-height: 1.4;
        }
        .help-text code {
            background-color: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端改进测试页面</h1>
        <p>这个页面用于测试前端改进的各项功能</p>

        <!-- 测试1: 复选框位置 -->
        <div class="test-section">
            <h3>测试1: 复选框位置修复</h3>
            <div class="form-group">
                <label for="testCheckbox" class="checkbox-group">
                    <input type="checkbox" id="testCheckbox" checked>
                    <span>启用此工具</span>
                </label>
            </div>
            <p><strong>预期效果:</strong> 复选框应该紧贴在"启用此工具"文字前面，间距合适。</p>
        </div>

        <!-- 测试2: 关键词组合配置 -->
        <div class="test-section">
            <h3>测试2: 关键词组合配置</h3>
            <div class="form-group">
                <label for="keywordsTest">关键词组合*</label>
                <textarea id="keywordsTest" placeholder="支持多种组合格式：&#10;1. 单个关键词：查询&#10;2. 词组合（逗号分隔）：事件,周边&#10;3. 多个组合（分号分隔）：事件,周边;事件,附近;查询,资源"></textarea>
                <div class="help-text">
                    <strong>关键词组合说明：</strong><br>
                    • 单个关键词：直接输入，如 "查询"<br>
                    • 词组合：用逗号分隔同一组合内的词，如 "事件,周边"（用户输入需同时包含"事件"和"周边"才触发）<br>
                    • 多个组合：用分号分隔不同组合，如 "事件,周边;事件,附近"（满足任一组合即可触发）<br>
                    • 示例：<code>事件,周边;事件,附近;查询,资源</code>
                </div>
            </div>
            <button class="btn btn-primary" onclick="testKeywords()">测试关键词匹配</button>
            <div id="keywordResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试3: 返回参数配置 -->
        <div class="test-section">
            <h3>测试3: 返回参数配置改进</h3>
            <div class="form-group">
                <label>返回参数配置</label>
                <div class="help-text">
                    <strong>返回参数解析说明：</strong><br>
                    • 支持多层嵌套字段，如：<code>data.name</code>、<code>data[].title</code><br>
                    • 中文名称用于显示，字段路径用于解析数据<br>
                    • 只配置需要解析的字段，不配置的字段不会显示<br>
                    • 示例：字段路径 <code>data</code>，中文名称 <code>数据列表</code>
                </div>
                <div id="responseParamsTest">
                    <!-- 示例参数配置 -->
                    <div class="param-item">
                        <input type="text" placeholder="字段路径 (如: data.name, data[].title)" value="data" style="flex: 2;">
                        <input type="text" placeholder="中文名称 (如: 姓名, 标题)" value="数据列表" style="flex: 1;">
                        <select style="width: 100px;">
                            <option value="array" selected>数组</option>
                            <option value="string">字符串</option>
                            <option value="object">对象</option>
                        </select>
                        <button type="button" class="btn btn-danger">删除</button>
                    </div>
                    <div class="param-item">
                        <input type="text" placeholder="字段路径 (如: data.name, data[].title)" value="data[].name" style="flex: 2;">
                        <input type="text" placeholder="中文名称 (如: 姓名, 标题)" value="设备名称" style="flex: 1;">
                        <select style="width: 100px;">
                            <option value="string" selected>字符串</option>
                            <option value="array">数组</option>
                            <option value="object">对象</option>
                        </select>
                        <button type="button" class="btn btn-danger">删除</button>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="addResponseParam()">添加参数</button>
            </div>
            <button class="btn btn-primary" onclick="testResponseParsing()">测试响应解析</button>
            <div id="responseResult" class="result" style="display: none;"></div>
        </div>

        <!-- 测试4: 导入导出功能 -->
        <div class="test-section">
            <h3>测试4: 导入导出功能</h3>
            <button class="btn btn-success" onclick="testImport()">测试导入配置</button>
            <button class="btn btn-warning" onclick="testExport()">测试导出配置</button>
            <div id="importExportResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 测试关键词匹配
        function testKeywords() {
            const keywords = document.getElementById('keywordsTest').value;
            const testInputs = [
                "查询事件周边资源",
                "事件附近有什么",
                "查询天气情况",
                "搜索资源信息"
            ];
            
            let result = "<h4>关键词匹配测试结果:</h4>";
            result += `<p><strong>配置的关键词:</strong> ${keywords}</p>`;
            
            testInputs.forEach(input => {
                const matches = testKeywordMatch(keywords, input);
                result += `<p>输入: "${input}" - ${matches ? '✅ 匹配' : '❌ 不匹配'}</p>`;
            });
            
            document.getElementById('keywordResult').innerHTML = result;
            document.getElementById('keywordResult').style.display = 'block';
        }

        // 简化的关键词匹配测试
        function testKeywordMatch(keywordsStr, userInput) {
            if (!keywordsStr.trim()) return false;
            
            const userInputLower = userInput.toLowerCase();
            let combinations = [];
            
            if (keywordsStr.includes(';')) {
                combinations = keywordsStr.split(';').map(group => 
                    group.includes(',') ? group.split(',').map(kw => kw.trim().toLowerCase()) : [group.trim().toLowerCase()]
                );
            } else {
                combinations = keywordsStr.includes(',') ? 
                    [keywordsStr.split(',').map(kw => kw.trim().toLowerCase())] : 
                    [[keywordsStr.trim().toLowerCase()]];
            }
            
            return combinations.some(combination => 
                combination.every(keyword => userInputLower.includes(keyword))
            );
        }

        // 添加返回参数
        function addResponseParam() {
            const container = document.getElementById('responseParamsTest');
            const paramDiv = document.createElement('div');
            paramDiv.className = 'param-item';
            paramDiv.innerHTML = `
                <input type="text" placeholder="字段路径 (如: data.name, data[].title)" style="flex: 2;">
                <input type="text" placeholder="中文名称 (如: 姓名, 标题)" style="flex: 1;">
                <select style="width: 100px;">
                    <option value="string">字符串</option>
                    <option value="int">整数</option>
                    <option value="array">数组</option>
                    <option value="object">对象</option>
                </select>
                <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">删除</button>
            `;
            container.appendChild(paramDiv);
        }

        // 测试响应解析
        function testResponseParsing() {
            const sampleResponse = {
                "code": "0",
                "data": [
                    {
                        "name": "IP CAMERA",
                        "type": "common",
                        "longitude": "120.215581",
                        "latitude": "30.251763",
                        "distance": 4.695
                    },
                    {
                        "name": "************",
                        "type": "common", 
                        "longitude": "120.215481",
                        "latitude": "30.252863",
                        "distance": 4.817
                    }
                ],
                "msg": "成功"
            };
            
            let result = "<h4>响应解析测试结果:</h4>";
            result += "<p><strong>示例响应数据:</strong></p>";
            result += `<pre>${JSON.stringify(sampleResponse, null, 2)}</pre>`;
            result += "<p><strong>根据配置解析结果:</strong></p>";
            result += "<p>数据列表: 2 项</p>";
            result += "<p>  1. IP CAMERA</p>";
            result += "<p>  2. ************</p>";
            
            document.getElementById('responseResult').innerHTML = result;
            document.getElementById('responseResult').style.display = 'block';
        }

        // 测试导入功能
        function testImport() {
            const result = "导入功能测试: 需要选择JSON配置文件进行导入";
            document.getElementById('importExportResult').innerHTML = result;
            document.getElementById('importExportResult').style.display = 'block';
        }

        // 测试导出功能
        function testExport() {
            const sampleConfig = [
                {
                    "tool_name_cn": "测试工具",
                    "tool_name_en": "test_tool",
                    "description": "这是一个测试工具",
                    "keywords": "测试,工具;test,tool",
                    "request_params": {},
                    "response_params": {
                        "data": {"type": "array", "description": "数据列表"},
                        "data[].name": {"type": "string", "description": "设备名称"}
                    }
                }
            ];
            
            const blob = new Blob([JSON.stringify(sampleConfig, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test_config_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            const result = "✅ 导出功能测试成功: 已下载测试配置文件";
            document.getElementById('importExportResult').innerHTML = result;
            document.getElementById('importExportResult').style.display = 'block';
        }
    </script>
</body>
</html>
