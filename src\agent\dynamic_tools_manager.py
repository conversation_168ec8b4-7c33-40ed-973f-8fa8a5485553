"""
动态工具管理器 - 支持用户配置的API工具动态加载和执行
"""

import json
import logging
import requests
import configparser
from typing import Dict, List, Optional, Any
from src.db_manager import DBManager

logger = logging.getLogger("dynamic_tools_manager")

class DynamicToolsManager:
    """动态工具管理器"""
    
    def __init__(self):
        self.db_manager = DBManager()
        self.config = self._load_config()
        self.tools_cache = {}
        self.keywords_cache = {}
        self._refresh_cache()
    
    def _load_config(self):
        """加载配置文件"""
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        return config
    
    def _refresh_cache(self):
        """刷新工具缓存"""
        try:
            # 获取所有活跃的工具配置
            tools = self.db_manager.get_all_api_tools(active_only=True)
            
            # 重建缓存
            self.tools_cache = {}
            self.keywords_cache = {}
            
            for tool in tools:
                tool_name = tool['tool_name_en']
                self.tools_cache[tool_name] = tool
                
                # 构建关键词映射
                keywords = [kw.strip() for kw in tool['keywords'].split(',') if kw.strip()]
                for keyword in keywords:
                    if keyword not in self.keywords_cache:
                        self.keywords_cache[keyword] = []
                    self.keywords_cache[keyword].append(tool_name)
            
            logger.info(f"动态工具缓存刷新完成，共加载 {len(self.tools_cache)} 个工具")
        except Exception as e:
            logger.error(f"刷新工具缓存失败: {str(e)}")
    
    def get_tool_keywords(self) -> Dict[str, List[str]]:
        """获取所有工具的关键词映射"""
        return self.keywords_cache.copy()
    
    def get_tools_functions_definition(self) -> List[Dict]:
        """获取所有工具的函数定义（用于Function Calling）"""
        functions = []
        
        for tool_name, tool_config in self.tools_cache.items():
            function_def = {
                "name": tool_name,
                "description": tool_config.get('description', ''),
                "parameters": {}
            }
            
            # 构建参数定义
            request_params = tool_config.get('request_params', {})
            for param_name, param_config in request_params.items():
                param_type = param_config.get('type', 'string')
                # 转换参数类型格式
                if param_type == 'int':
                    param_type = 'int'
                elif param_type == 'float':
                    param_type = 'float'
                elif param_type == 'boolean':
                    param_type = 'boolean'
                else:
                    param_type = 'string'
                
                # 检查是否为可选参数
                if param_config.get('default') is not None:
                    param_type += '|optional'
                
                function_def["parameters"][param_name] = param_type
            
            functions.append(function_def)
        
        return functions
    
    def check_tool_match(self, user_input: str) -> Optional[str]:
        """检查用户输入是否匹配某个工具的关键词"""
        user_input_lower = user_input.lower()
        
        for keyword, tool_names in self.keywords_cache.items():
            if keyword.lower() in user_input_lower:
                # 返回第一个匹配的工具名称
                return tool_names[0] if tool_names else None
        
        return None
    
    def execute_dynamic_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """执行动态配置的工具"""
        try:
            if tool_name not in self.tools_cache:
                return f"错误：未找到工具 {tool_name}"
            
            tool_config = self.tools_cache[tool_name]
            
            # 构建API请求
            api_url = self._build_api_url(tool_config)
            payload = self._build_api_payload(tool_config, arguments)
            
            # 发送API请求
            response = self._send_api_request(api_url, payload)
            
            # 解析响应
            return self._parse_api_response(tool_config, response)
            
        except Exception as e:
            logger.error(f"执行动态工具 {tool_name} 失败: {str(e)}")
            return f"执行工具时出错: {str(e)}"
    
    def _build_api_url(self, tool_config: Dict) -> str:
        """构建API URL"""
        base_url = self.config.get("API", "BASE_URL", fallback="https://10.19.132.32")
        post_path = tool_config['post_path']
        
        # 确保路径以/开头
        if not post_path.startswith('/'):
            post_path = '/' + post_path
        
        return f"{base_url}{post_path}"
    
    def _build_api_payload(self, tool_config: Dict, arguments: Dict[str, Any]) -> Dict:
        """构建API请求载荷"""
        payload = {}
        request_params = tool_config.get('request_params', {})
        
        for param_name, param_config in request_params.items():
            if param_name in arguments:
                value = arguments[param_name]
                # 根据参数类型转换值
                param_type = param_config.get('type', 'string')
                if param_type == 'int':
                    try:
                        value = int(value)
                    except (ValueError, TypeError):
                        value = int(param_config.get('default', 0))
                elif param_type == 'float':
                    try:
                        value = float(value)
                    except (ValueError, TypeError):
                        value = float(param_config.get('default', 0.0))
                elif param_type == 'boolean':
                    value = bool(value)
                
                payload[param_name] = value
            else:
                # 使用默认值
                default_value = param_config.get('default')
                if default_value is not None:
                    payload[param_name] = default_value
        
        return payload
    
    def _send_api_request(self, api_url: str, payload: Dict) -> Dict:
        """发送API请求"""
        headers = {
            'Content-Type': self.config.get("API", "CONTENT_TYPE", fallback="application/json"),
            'Authorization': f'Bearer {self.config.get("API", "TOKEN", fallback="")}'
        }
        
        logger.info(f"发送API请求到: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False, timeout=30)
        response.raise_for_status()
        
        return response.json()
    
    def _parse_api_response(self, tool_config: Dict, response: Dict) -> str:
        """解析API响应"""
        try:
            # 检查响应状态
            if response.get('code') == '0' or response.get('success') == True:
                # 成功响应，格式化返回数据
                return self._format_success_response(tool_config, response)
            else:
                # 错误响应
                error_msg = response.get('msg', response.get('message', '未知错误'))
                return f"API调用失败: {error_msg}"
        except Exception as e:
            logger.error(f"解析API响应失败: {str(e)}")
            return f"解析响应时出错: {str(e)}"
    
    def _format_success_response(self, tool_config: Dict, response: Dict) -> str:
        """格式化成功响应"""
        try:
            response_params = tool_config.get('response_params', {})
            
            if not response_params:
                # 如果没有配置返回参数，直接返回原始响应
                return json.dumps(response, ensure_ascii=False, indent=2)
            
            # 根据配置的返回参数格式化响应
            formatted_parts = []
            
            for field_name, field_config in response_params.items():
                if field_name in response:
                    value = response[field_name]
                    description = field_config.get('description', field_name)
                    
                    # 跳过空值
                    if value is None or value == '':
                        continue
                    
                    # 格式化不同类型的值
                    if isinstance(value, list):
                        if value:  # 非空列表
                            formatted_parts.append(f"{description}: {len(value)} 项")
                            # 如果是资源列表，显示前几项
                            if len(value) <= 3:
                                for i, item in enumerate(value, 1):
                                    if isinstance(item, dict):
                                        name = item.get('name', item.get('resourceName', f'项目{i}'))
                                        formatted_parts.append(f"  {i}. {name}")
                            else:
                                formatted_parts.append(f"  显示前3项，共{len(value)}项")
                                for i in range(3):
                                    item = value[i]
                                    if isinstance(item, dict):
                                        name = item.get('name', item.get('resourceName', f'项目{i+1}'))
                                        formatted_parts.append(f"  {i+1}. {name}")
                    elif isinstance(value, dict):
                        formatted_parts.append(f"{description}: {json.dumps(value, ensure_ascii=False)}")
                    else:
                        formatted_parts.append(f"{description}: {value}")
            
            return '\n'.join(formatted_parts) if formatted_parts else "操作成功完成"
            
        except Exception as e:
            logger.error(f"格式化响应失败: {str(e)}")
            return json.dumps(response, ensure_ascii=False, indent=2)
    
    def refresh_tools(self):
        """刷新工具配置（供外部调用）"""
        self._refresh_cache()

# 全局实例
dynamic_tools_manager = DynamicToolsManager()
