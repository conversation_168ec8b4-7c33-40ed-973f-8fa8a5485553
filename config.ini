[PATHS]
# LibreOffice 可执行文件路径
# Windows 示例: C:\Program Files\LibreOffice\program\soffice.exe
# Linux 示例: /usr/bin/soffice
LIBREOFFICE_PATH = C:\Program Files\LibreOffice\program\soffice.exe
# Tesseract OCR 可执行文件路径
# Windows 示例: D:\Program Files\Tesseract-OCR
# Linux 示例: /usr/bin
TESSERACT_PATH = D:\Program Files\Tesseract-OCR

[DATABASE]
# 向量数据库存储目录
PERSIST_DIRECTORY = Private_GPT/db_DOC_basic_600
VECTOR_DB_TYPE = qdrant
SCORE_THRESHOLD = 0.5

[QDRANT]
HOST = ************
PORT = 7541
# 存储集合名称，如果为空则使用文件夹名称 示例：documents、water_test、spyj
COLLECTION_NAME_STORE = documents
# 查询集合名称，如果为空则使用最新的集合 示例：documents、water_test、spyj
COLLECTION_NAME_QUERY = documents

[POSTGRES]
# PostgreSQL数据库连接信息
HOST = ************
PORT = 7543
DATABASE = postgres
USER = postgres
PASSWORD = hik12345+

[DOCUMENTS]
# 源文档目录
SOURCE_DIRECTORY = Private_GPT\source_documents

[EMBEDDINGS]
# 嵌入模型路径
MODEL_NAME = Private_GPT\sentence-transformers\bge-large-zh-v1.5
# 嵌入模型类型: huggingface, ollama
EMBEDDING_TYPE = huggingface
# Ollama服务的基础URL，当EMBEDDING_TYPE=ollama时使用
BASE_URL = http://localhost:11434
# Ollama模型名称，当EMBEDDING_TYPE=ollama时使用
OLLAMA_MODEL = bge-large:latest

[CHUNKING]
# 文本分块大小
CHUNK_SIZE = 1000
# 文本分块重叠大小
CHUNK_OVERLAP = 100

[LLM]
# 大语言模型类型：ollama或openai
LLM_TYPE = ollama
# 大语言模型名称：
# - 当LLM_TYPE=ollama时，如：deepseek-r1:32b、qwq、DeepSeek-R1-Distill-Qwen-32B、gemma3:27b、qwen3:30b、deepseek-r1:8b、qwen3:8b等
# - 当LLM_TYPE=openai时，如：deepseek-chat、gpt-3.5-turbo、gpt-4等
MODEL = deepseek-r1:8b
# Ollama基础URL，当LLM_TYPE=ollama时使用 url示例： http://************:11434、http://localhost:11434、http://lanz.hikvision.com/v3/openai/deepseek-v3_1
OLLAMA_BASE_URL = http://localhost:11434
# OpenAI API密钥，当LLM_TYPE=openai时使用 示例：ds：sk-eefa01dd15c149e1a7245c2089294f9a、sk-123456
OPENAI_API_KEY = sk-eefa01dd15c149e1a7245c2089294f9a
# OpenAI API基础URL，当LLM_TYPE=openai时使用，默认为官方API 示例：https://api.deepseek.com、http://*************:8000/v1/、
OPENAI_API_BASE = https://open.bigmodel.cn/api
#OPENAI_API_BASE = https://maas.hikvision.com.cn/v1
# 检索时返回的文档块数量
TARGET_SOURCE_CHUNKS = 5

[OCR]
# OCR 语言设置
# 中文简体+英文: chi_sim+eng
# 中文繁体+英文: chi_tra+eng
# 仅英文: eng
LANGUAGES = chi_sim+eng



[API]
# API基础URL（服务器地址和端口）*************、*************
BASE_URL = https://************
# API路径配置 - 默认路径
DEFAULT_PATH = /emdispatch-web/third/v1/eventApi/app/
# API路径配置 - 仓库接口路径
REPOSITORY_PATH = /ermanage-web/third/v1/resourceApi/resourcePage/
# API路径配置 - 预案接口路径
PLAN_PATH = /emplan-web/third/v1/resourceApi/plan/
# 内容类型
CONTENT_TYPE = application/json
# 访问令牌
TOKEN = ************************************************************************************************************
# 函数调用方法选择: react 或 functioncalling
# react: 使用ReAct模式的本地大模型代理
# functioncalling: 使用Function Calling模式的本地大模型代理
FUNCTION_CALLING_METHOD = functioncalling

[JIUAN_API]
# 久安大模型API基础URL
BASE_URL = http://172.28.48.186:8310
# OAuth认证配置
CLIENT_ID = af51852d19cf43d4884b009f2fb25c93
CLIENT_SECRET = uZw8vqg!
# Token获取接口
TOKEN_PATH = /oauth/token
APP_ID = 
# 获取会话ID接口
CONVERSATION_PATH = /service/api/api/ai_apaas/v1/app/conversation
# deepseek对话接口
CHAT_PATH = /service/api/api/ai_apaas/v1/app/conversation/runs
CHAT_CONTENT_TYPE = application/json
CHAT_TIMEOUT = 120
# 对话接口默认参数
CHAT_DEFAULT_MODEL = deepseek-r1-full
# 图片识别接口
IMAGE_PATH = /service/api/dhxx
IMAGE_CONTENT_TYPE = application/json
IMAGE_TIMEOUT = 120
# 视频识别接口
VIDEO_PATH = /service/api/api/analysis/video
VIDEO_CONTENT_TYPE = application/json
VIDEO_TIMEOUT = 120
# 视频分析结果查询接口
VIDEO_RESULT_PATH = /service/api/api/analysis/returnVideoAnalysis
VIDEO_RESULT_CONTENT_TYPE = application/json
VIDEO_RESULT_TIMEOUT = 120
# 应急行业知识问答接口
EMERGENCY_KNOWLEDGE_PATH = /service/api/rpc/2.0/ai_custom/v1/wenxinworkshop/plugin/{x}
EMERGENCY_KNOWLEDGE_CONTENT_TYPE = application/json
EMERGENCY_KNOWLEDGE_TIMEOUT = 120
# 接口访问令牌
TOKEN = ************************************************************************************************************
# 是否验证SSL证书
VERIFY_SSL = false



[SPEECH_RECOGNITION]
# 语音识别引擎选择: whisper 或 fast-whisper
ENGINE = fast-whisper
# 计算设备选择: cpu 或 cuda
DEVICE = cuda
# 计算精度: int8, int16, float16, float32 (fast-whisper支持)
COMPUTE_TYPE =  float32
# Whisper模型大小: tiny, base, small, medium, large
WHISPER_MODEL_SIZE = large
# 模型路径（可选，如果指定则从本地加载）
WHISPER_MODEL_PATH = Private_GPT\sentence-transformers\sound_model\small.pt
# fast-whisper模型路径（可选，如果指定则从本地加载）
FAST_WHISPER_MODEL_PATH = Private_GPT\sentence-transformers\sound_model\models--Systran--faster-whisper-large-v3\snapshots\edaa852ec7e145841d8ffdb056a99866b5f0a478
# 支持的音频格式
SUPPORTED_AUDIO_FORMATS = wav,mp3,m4a,flac,ogg,webm
# 音频文件大小限制(MB)
MAX_AUDIO_SIZE_MB = 10
# 音频处理超时时间(秒)
AUDIO_TIMEOUT = 30
# 语言设置 (zh=中文, en=英文, auto=自动检测)
LANGUAGE = zh
# 是否启用语音识别功能
ENABLE_SPEECH_RECOGNITION = true
# fast-whisper专用配置
# beam搜索大小
BEAM_SIZE = 5
# VAD过滤开关（Voice Activity Detection）
VAD_FILTER = true
# VAD参数
VAD_PARAMETERS = {"threshold": 0.5, "min_speech_duration_ms": 250, "max_speech_duration_s": 30, "min_silence_duration_ms": 100, "speech_pad_ms": 30}

[YJY_SPEECH_RECOGNITION]
# 应急语音识别服务配置
# 服务器IP地址
HOST = *************
# HTTP端口
HTTP_PORT = 8250
# HTTPS端口
HTTPS_PORT = 443
# 协议类型: http 或 https
PROTOCOL = http
# 请求上下文
CONTEXT_PATH = /xmessagemas-cms
# 访问令牌（可选，如果为空则不使用认证）
TOKEN = SElLIGQyT2dveFFOQ0hBZElRNmk6NXg2UUZ4T3VoMmkzNmNEMzFVR1N0TC9MOFRKSlErNmV2ODFwVDhkV2Z4ST06MTc1MjgyMzY0ODUxNQ==
# ASR API密钥（可选，用于第三方直接调用API接口）
API_KEY = asr-api-key-2025
# 是否启用语音识别功能
ENABLE_ASR = true
# 请求超时时间(秒)
REQUEST_TIMEOUT = 60
# 音频文件大小限制(MB)
MAX_AUDIO_SIZE_MB = 50
# 支持的音频格式（目前只支持pcm）
SUPPORTED_AUDIO_FORMATS = pcm
# WebSocket连接超时时间(秒)
WS_TIMEOUT = 300
# 音频数据发送间隔(毫秒)
AUDIO_SEND_INTERVAL = 400
# 每次发送的音频数据大小(字节)
AUDIO_CHUNK_SIZE = 12800

[TTS]
# TTS模型配置
MODEL_NAME = hexgrad/Kokoro-82M-v1.1-zh
MODEL_PATH = Private_GPT/sentence-transformers/kokoro-tts-v1.1-zh/kokoro-v1_1-zh.pth
VOICES_DIR = Private_GPT/sentence-transformers/kokoro-tts-v1.1-zh/voices

# 音频配置
SAMPLE_RATE = 24000
AUDIO_FORMAT = wav
MAX_TEXT_LENGTH = 1000

# 安全配置
MAX_CONCURRENT_REQUESTS = 10
REQUEST_TIMEOUT = 30

# 临时文件配置
TEMP_DIR = ./temp_files
CLEANUP_INTERVAL = 3600
