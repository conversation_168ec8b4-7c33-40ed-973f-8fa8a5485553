#!/bin/bash
# --------------------------------------------------------------------------------------------
# RAG系统启动脚本
# --------------------------------------------------------------------------------------------

# 获取当前目录
EASY_HOME=$(cd `dirname $0`; pwd)

# 日志相关配置
LOG_DIR="$EASY_HOME/log"
LOG_FILE="$LOG_DIR/uvicorn.log"
MAX_LOG_SIZE=100M  # logrotate使用的大小单位
BACKUP_COUNT=5     # 保留的日志文件数量
LOG_DATE_FORMAT="%Y-%m-%d %H:%M:%S"  # 日志时间格式

# 环境变量配置
export PYTHONPATH=$EASY_HOME:$PYTHONPATH
export RAG_ENV="production"

# 激活Python虚拟环境
source ~/miniconda3/envs/rag_env/bin/activate

# 检查是否为root用户
check_root() {
    if [ $(id -u) -ne 0 ]; then
        echo "需要root权限运行此脚本"
        exit 1
    fi
}

# 创建日志目录和轮转配置
setup_logging() {
    # 创建日志目录
    if ! mkdir -p $LOG_DIR 2>/dev/null; then
        echo "无法创建日志目录 $LOG_DIR"
        return 1
    fi

    # 配置logrotate
    if [ -d "/etc/logrotate.d" ]; then
        if ! cat > /etc/logrotate.d/rag_service << EOF
$LOG_FILE {
    size $MAX_LOG_SIZE
    rotate $BACKUP_COUNT
    copytruncate
    compress
    notifempty
    missingok
    dateext
    dateformat .$LOG_DATE_FORMAT
}
EOF
        then
            echo "无法创建 logrotate 配置文件，日志轮转可能不会正常工作"
            # 继续执行，不返回错误，因为这不是致命错误
        else
            echo "日志轮转配置已设置"
        fi
    else
        echo "未找到 logrotate 配置目录，日志轮转将不可用"
    fi

    # 创建空日志文件（如果不存在）
    if [ ! -f "$LOG_FILE" ]; then
        touch "$LOG_FILE" 2>/dev/null || {
            echo "无法创建日志文件 $LOG_FILE"
            return 1
        }
    fi

    return 0
}

# 检查必要服务
check_dependencies() {
    local errors=0
    local error_msg=""

    echo "检查依赖服务..."

    # 检查Qdrant服务
    if ! check_service_status 7541; then
        error_msg="$error_msg\n- Qdrant服务未运行，请先启动Qdrant"
        errors=$((errors + 1))
    else
        echo "- Qdrant服务正常运行"
    fi

    # 检查deepseek服务
    if ! check_service_status 8080; then
        error_msg="$error_msg\n- deepseek服务未运行，请先启动deepseek"
        errors=$((errors + 1))
    else
        echo "- deepseek服务正常运行"
    fi

    # 检查PostgreSQL服务
    # 从配置文件中读取PostgreSQL端口
    local pg_port=7543
    # 自动获取本机IP地址
    local pg_host=$(hostname -I | awk '{print $1}')
    # 如果无法获取IP，则使用localhost
    if [ -z "$pg_host" ]; then
        pg_host="localhost"
    fi

    if [ -f "$EASY_HOME/config.ini" ]; then
        # 尝试从配置文件中读取端口
        local config_port=$(grep -E "^PORT\s*=" "$EASY_HOME/config.ini" | grep -A5 "\[POSTGRES\]" | head -n1 | cut -d'=' -f2 | tr -d ' ')
        local config_host=$(grep -E "^HOST\s*=" "$EASY_HOME/config.ini" | grep -A5 "\[POSTGRES\]" | head -n1 | cut -d'=' -f2 | tr -d ' ')

        if [ -n "$config_port" ]; then
            pg_port=$config_port
        fi

        if [ -n "$config_host" ]; then
            pg_host=$config_host
        fi
    fi

    if ! check_postgres_connection "$pg_host" "$pg_port"; then
        error_msg="$error_msg\n- PostgreSQL数据库未运行或无法连接，请检查PostgreSQL服务"
        errors=$((errors + 1))
    else
        echo "- PostgreSQL数据库正常运行"
    fi

    # 检查Python环境
    if ! command -v python3 &> /dev/null; then
        error_msg="$error_msg\n- Python3未安装"
        errors=$((errors + 1))
    else
        echo "- Python3环境正常"

        # 检查Python版本
        local py_version=$(python3 --version 2>&1 | cut -d' ' -f2)
        echo "  Python版本: $py_version"
    fi

    # 检查虚拟环境
    if [ ! -f "~/miniconda3/envs/rag_env/bin/activate" ]; then
        echo "- 警告: 找不到指定的Python虚拟环境，可能需要调整配置"
    fi

    # 返回结果
    if [ $errors -gt 0 ]; then
        echo -e "检测到以下问题:$error_msg"
        return 1
    fi

    echo "所有依赖服务检查通过"
    return 0
}

# 检查服务状态
check_service_status() {
    local port=$1
    local host="localhost"

    # 如果提供了第二个参数，则使用它作为主机名
    if [ -n "$2" ]; then
        host="$2"
    fi

    # 首先检查是否存在nc命令
    if command -v nc &> /dev/null; then
        if nc -z $host $port; then
            return 0
        else
            return 1
        fi
    else
        # 如果没有nc，使用/dev/tcp替代
        if (echo > /dev/tcp/$host/$port) >/dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    fi
}

# 检查PostgreSQL连接
check_postgres_connection() {
    local host=$1
    local port=$2

    # 首先检查端口是否开放
    if ! check_service_status "$port" "$host"; then
        return 1
    fi

    # 如果安装了psql，尝试使用psql连接
    if command -v psql &> /dev/null; then
        # 从配置文件中读取用户名和密码
        local pg_user="postgres"
        local pg_password="hik12345+"
        local pg_database="postgres"

        if [ -f "$EASY_HOME/config.ini" ]; then
            local config_user=$(grep -E "^USER\s*=" "$EASY_HOME/config.ini" | grep -A10 "\[POSTGRES\]" | head -n1 | cut -d'=' -f2 | tr -d ' ')
            local config_password=$(grep -E "^PASSWORD\s*=" "$EASY_HOME/config.ini" | grep -A10 "\[POSTGRES\]" | head -n1 | cut -d'=' -f2 | tr -d ' ')
            local config_database=$(grep -E "^DATABASE\s*=" "$EASY_HOME/config.ini" | grep -A10 "\[POSTGRES\]" | head -n1 | cut -d'=' -f2 | tr -d ' ')

            if [ -n "$config_user" ]; then
                pg_user=$config_user
            fi

            if [ -n "$config_password" ]; then
                pg_password=$config_password
            fi

            if [ -n "$config_database" ]; then
                pg_database=$config_database
            fi
        fi

        # 尝试连接PostgreSQL
        PGPASSWORD="$pg_password" psql -h "$host" -p "$port" -U "$pg_user" -d "$pg_database" -c "\q" >/dev/null 2>&1
        return $?
    else
        # 如果没有psql，只能检查端口
        # 端口检查已经通过，返回成功
        return 0
    fi
}

# 检查服务运行状态
check_running() {
    if [ -f "$EASY_HOME/app.pid" ]; then
        local pid=$(cat "$EASY_HOME/app.pid")
        if [ -n "$pid" ] && kill -0 $pid 2>/dev/null; then
            # 进一步验证这是否是我们的uvicorn进程
            if ps -p $pid -o cmd= | grep -q "uvicorn main:app"; then
                return 0
            else
                echo "警告: PID文件存在但进程似乎不是uvicorn服务"
                rm -f "$EASY_HOME/app.pid"
            fi
        else
            echo "警告: PID文件存在但进程不存在，清理PID文件"
            rm -f "$EASY_HOME/app.pid"
        fi
    fi
    return 1
}

# 等待服务启动
wait_for_service() {
    local port=$1
    local timeout=30
    local count=0

    while [ $count -lt $timeout ]; do
        if check_service_status $port; then
            echo "服务在端口 $port 上成功启动"
            return 0
        fi
        echo "等待服务启动，已等待 $count 秒..."
        sleep 1
        count=$((count + 1))
    done
    echo "等待服务启动超时（$timeout 秒）"
    return 1
}

# 启动服务
start() {
    # 检查依赖服务
    if ! check_dependencies; then
        echo "依赖服务检查失败，请确保所有必要服务都已启动"
        return 1
    fi

    # 检查是否已运行
    if check_running; then
        echo "服务已经在运行中"
        return 0
    fi

    # 设置日志
    setup_logging

    # 启动服务
    cd $EASY_HOME
    # 使用环境变量或默认值来设置主机地址
    HOST_IP=${RAG_HOST_IP:-"0.0.0.0"}
    nohup uvicorn main:app --host $HOST_IP --port 8089 > $LOG_FILE 2>&1 & echo $! > app.pid

    # 等待服务启动
    if wait_for_service 8089; then
        echo "RAG服务启动成功"
        return 0
    else
        echo "RAG服务启动失败，请检查日志"
        return 1
    fi
}

# 停止服务
stop() {
    if [ -f "$EASY_HOME/app.pid" ]; then
        local pid=$(cat "$EASY_HOME/app.pid")
        if [ -n "$pid" ]; then
            # 先尝试正常停止
            kill -15 $pid 2>/dev/null

            # 等待进程退出
            local count=0
            while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done

            # 如果进程仍然存在，强制终止
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid
            fi

            rm -f "$EASY_HOME/app.pid"
            echo "服务已停止"
        fi
    else
        echo "找不到PID文件，服务可能未运行"
    fi
}

# 查看服务状态
status() {
    if check_running; then
        local pid=$(cat "$EASY_HOME/app.pid")
        echo -e "RAG服务运行中 (PID: $pid)"

        # 检查依赖服务状态
        if check_service_status 7541; then
            echo -e "Qdrant服务: \e[1;32m运行中\e[0m"
        else
            echo -e "Qdrant服务: \e[1;31m未运行\e[0m"
        fi

        if check_service_status 8080; then
            echo -e "deepseek服务: \e[1;32m运行中\e[0m"
        else
            echo -e "deepseek服务: \e[1;31m未运行\e[0m"
        fi

        # 从配置文件中读取PostgreSQL端口
        local pg_port=7543
        # 自动获取本机IP地址
        local pg_host=$(hostname -I | awk '{print $1}')
        # 如果无法获取IP，则使用localhost
        if [ -z "$pg_host" ]; then
            pg_host="localhost"
        fi

        if [ -f "$EASY_HOME/config.ini" ]; then
            local config_port=$(grep -E "^PORT\s*=" "$EASY_HOME/config.ini" | grep -A5 "\[POSTGRES\]" | head -n1 | cut -d'=' -f2 | tr -d ' ')
            local config_host=$(grep -E "^HOST\s*=" "$EASY_HOME/config.ini" | grep -A5 "\[POSTGRES\]" | head -n1 | cut -d'=' -f2 | tr -d ' ')

            if [ -n "$config_port" ]; then
                pg_port=$config_port
            fi

            if [ -n "$config_host" ]; then
                pg_host=$config_host
            fi
        fi

        if check_postgres_connection "$pg_host" "$pg_port"; then
            echo -e "PostgreSQL数据库: \e[1;32m运行中\e[0m ($pg_host:$pg_port)"
        else
            echo -e "PostgreSQL数据库: \e[1;31m未运行\e[0m ($pg_host:$pg_port)"
        fi
    else
        echo -e "RAG服务未运行"
    fi
}

# 重启服务
restart() {
    stop
    sleep 2
    start
}

# 查看日志
view_logs() {
    local lines=${1:-50}  # 默认显示50行

    if [ ! -f "$LOG_FILE" ]; then
        echo "日志文件 $LOG_FILE 不存在"
        return 1
    fi

    echo "显示最后 $lines 行日志:"
    tail -n $lines "$LOG_FILE"
}

# 主函数
main() {
    # 检查root权限
    check_root

    case "$1" in
        start)
            start
            ;;
        stop)
            stop
            ;;
        status)
            status
            ;;
        restart)
            restart
            ;;
        logs)
            # 如果提供了第二个参数，则使用它作为行数
            view_logs "${2:-50}"
            ;;
        *)
            echo "用法: $0 {start|stop|status|restart|logs [lines]}"
            echo "  start   - 启动RAG服务"
            echo "  stop    - 停止RAG服务"
            echo "  status  - 显示RAG服务状态"
            echo "  restart - 重启RAG服务"
            echo "  logs    - 查看日志文件（可选参数: 行数，默认50行）"
            exit 1
    esac
}

# 执行主函数
main "$@"