#!/usr/bin/env python3
#test2：element by_title分割， test3:basic  test4;element,basic
#"strategy":'hi_res',"mode":"elements","hi_res_model_name":"yolox","chunking_strategy":"by_title","extract_images":False,"OCR_Languages":"chi-sim","infer_table_structure":True,"max_characters":2000,"new_after_n_chars":1800,"combine_text_under_n_chars":500,'post_processors':'[clean_extra_whitespace]'}),
import os
import glob
from typing import List
from multiprocessing import Pool
from tqdm import tqdm
#from unstructured.partition.pdf import partition_pdf
#from unstructured.cleaners.core import clean_extra_whitespace
import logging
import traceback
import sys
import platform
import subprocess
import configparser
import re
import torch
import datetime
#import fitz  # PyMuPDF
from langchain_community.document_loaders import (
    CSVLoader,
    EverNoteLoader,
    PyMuPDFLoader,
    PyPDFLoader,  # 我们将使用这个加载器处理PDF
    TextLoader,
    UnstructuredEmailLoader,
    UnstructuredEPubLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader,
    UnstructuredODTLoader,
    UnstructuredPowerPointLoader,
    UnstructuredWordDocumentLoader,
    # UnstructuredPDFLoader,  # 注释掉这个加载器，因为我们使用PyPDFLoader替代
    Docx2txtLoader,
    UnstructuredExcelLoader,
)
from unstructured.cleaners.core import clean_extra_whitespace
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.embeddings import OllamaEmbeddings
from langchain.docstore.document import Document
#from constants import CHROMA_SETTINGS
#python ingest_MBSE_eng.py
#from chromadb.config import Settings

# 移除 Chroma 设置
# CHROMA_SETTINGS = Settings(
#     anonymized_telemetry=False
# )

# 配置日志
# 确保日志目录存在
log_dir = "log"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, "ingest.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ingest")

# 加载配置文件
config = configparser.ConfigParser()
try:
    # 明确指定 UTF-8 编码
    config.read("config.ini", encoding='utf-8')
    logger.info("成功加载配置文件 config.ini")
except Exception as e:
    logger.error(f"加载配置文件失败: {str(e)}")
    # 尝试使用其他编码


#PERSIST_DIRECTORY = r"Private_GPT\db_DOC_basic_03"

# 从配置文件获取路径
libreoffice_path_config = config.get('PATHS', 'LIBREOFFICE_PATH', fallback=r'C:\Program Files\LibreOffice\program\soffice.exe')
persist_directory = config.get('DATABASE', 'PERSIST_DIRECTORY', fallback='Private_GPT/db_DOC_basic_03')
source_directory = config.get('DOCUMENTS', 'SOURCE_DIRECTORY', fallback='Private_GPT/source_documents')
embeddings_model_name = config.get('EMBEDDINGS', 'MODEL_NAME', fallback='Private_GPT/sentence-transformers/bge-large-zh-v1.5')
chunk_size = config.getint('CHUNKING', 'CHUNK_SIZE', fallback=1000)
chunk_overlap = config.getint('CHUNKING', 'CHUNK_OVERLAP', fallback=100)

# 环境变量优先级高于配置文件
persist_directory = os.environ.get('PERSIST_DIRECTORY', persist_directory)
source_directory = os.environ.get('SOURCE_DIRECTORY', source_directory)
embeddings_model_name = os.environ.get('EMBEDDINGS_MODEL_NAME', embeddings_model_name)

# 在配置文件加载后添加 OCR 配置获取
tesseract_path_config = config.get('PATHS', 'TESSERACT_PATH', fallback='')
ocr_languages = config.get('OCR', 'LANGUAGES', fallback='chi_sim+eng')

# 如果配置了 Tesseract 路径，设置环境变量
if tesseract_path_config and os.path.exists(tesseract_path_config):
    logger.info(f"设置 Tesseract OCR 路径: {tesseract_path_config}")
    # 添加到环境变量
    os.environ["PATH"] = tesseract_path_config + os.pathsep + os.environ.get("PATH", "")
    # 设置 Tesseract 数据路径
    os.environ["TESSDATA_PREFIX"] = os.path.join(tesseract_path_config, "tessdata")

LOADER_MAPPING = {
    ".pdf": (PyPDFLoader, {}),  # 使用PyPDFLoader替代UnstructuredPDFLoader
    ".docx": (UnstructuredWordDocumentLoader, {"mode": "single"}),
    ".xlsx": (UnstructuredExcelLoader, {}),
    ".xls": (UnstructuredExcelLoader, {}),
    ".csv": (CSVLoader, {}),
    ".md": (UnstructuredMarkdownLoader, {}),
    ".ppt": (UnstructuredPowerPointLoader, {}),
    ".pptx": (UnstructuredPowerPointLoader, {}),
    ".txt": (TextLoader, {"encoding": "utf-8", "autodetect_encoding": True}),
    # Add more mappings for other file extensions and loaders as needed
}

# 移除全局OCR检查的代码
try:
    import textract
    HAS_TEXTRACT = True
except ImportError:
    HAS_TEXTRACT = False

def normalize_path(path):
    """标准化路径，确保使用正确的路径分隔符"""
    if path:
        # 将路径中的正斜杠替换为操作系统适合的路径分隔符
        path = path.replace('/', os.sep).replace('\\', os.sep)
        # 转换为绝对路径
        if not os.path.isabs(path):
            # 如果是相对路径，则相对于当前工作目录
            path = os.path.abspath(path)
    return path

def format_datetime_from_parts(timestamp_str, time_part_str):
    """将时间戳和时间部分格式化为易读的日期时间格式

    Args:
        timestamp_str: 日期部分，格式为'YYYYMMDD'，例如'20250422'
        time_part_str: 时间部分，格式为'HHMMSS'，例如'195049'

    Returns:
        格式化的日期时间字符串，例如'2025-04-22 19:50:49'
    """
    try:
        if not timestamp_str or len(timestamp_str) != 8:
            return ""

        if not time_part_str or len(time_part_str) != 6:
            # 如果没有时间部分，只返回日期
            year = timestamp_str[:4]
            month = timestamp_str[4:6]
            day = timestamp_str[6:8]
            return f"{year}-{month}-{day}"

        # 解析日期部分
        year = timestamp_str[:4]
        month = timestamp_str[4:6]
        day = timestamp_str[6:8]

        # 解析时间部分
        hour = time_part_str[:2]
        minute = time_part_str[2:4]
        second = time_part_str[4:6]

        # 组合成格式化的日期时间
        return f"{year}-{month}-{day} {hour}:{minute}:{second}"
    except Exception as e:
        logger.warning(f"格式化日期时间失败: {str(e)}")
        return ""

def check_ocr_dependencies():
    """按需检查OCR依赖"""
    try:
        import pytesseract
        from pdf2image import convert_from_path
        return True
    except ImportError:
        logger.warning("未检测到 OCR 依赖，尝试安装...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pytesseract", "pdf2image", "pillow"])
            logger.info("成功安装 OCR 依赖")

            # 重新尝试导入
            import pytesseract
            from pdf2image import convert_from_path

            # 设置 Tesseract 路径
            if tesseract_path_config and os.path.exists(tesseract_path_config):
                pytesseract.pytesseract.tesseract_cmd = os.path.join(tesseract_path_config, "tesseract.exe")
                logger.info(f"设置 pytesseract.tesseract_cmd: {pytesseract.pytesseract.tesseract_cmd}")
            return True
        except Exception as e:
            logger.error(f"安装 OCR 依赖失败: {str(e)}")
            logger.warning("无法使用 OCR 功能，扫描 PDF 将无法处理")
            return False

def process_scanned_pdf(file_path: str) -> List[Document]:
    """处理扫描版PDF文件"""
    if not check_ocr_dependencies():
        logger.warning(f"OCR依赖不可用，无法处理可能的扫描版PDF: {file_path}")
        # 尝试使用PyMuPDFLoader作为备选
        try:
            logger.info(f"尝试使用PyMuPDFLoader作为备选方法处理PDF: {file_path}")
            loader = PyMuPDFLoader(file_path)
            docs = loader.load()
            if docs and any(doc.page_content.strip() for doc in docs):
                logger.info(f"PyMuPDFLoader成功加载PDF: {file_path}, 页数: {len(docs)}")
                return docs
        except Exception as e:
            logger.warning(f"PyMuPDFLoader备选方法失败: {str(e)}")
        return []

    try:
        import pytesseract
        from pdf2image import convert_from_path

        # 将PDF转换为图片
        try:
            images = convert_from_path(file_path)
            if not images:
                logger.warning(f"PDF转换为图片失败，未获取到图片: {file_path}")
                return []
        except Exception as e:
            logger.error(f"PDF转换为图片失败: {str(e)}")
            # 尝试使用PyMuPDFLoader作为备选
            try:
                logger.info(f"尝试使用PyMuPDFLoader作为备选方法处理PDF: {file_path}")
                loader = PyMuPDFLoader(file_path)
                docs = loader.load()
                if docs and any(doc.page_content.strip() for doc in docs):
                    logger.info(f"PyMuPDFLoader成功加载PDF: {file_path}, 页数: {len(docs)}")
                    return docs
            except Exception as e2:
                logger.warning(f"PyMuPDFLoader备选方法失败: {str(e2)}")
            return []

        text = ""

        # 对每个页面进行OCR
        for i, image in enumerate(images):
            try:
                page_text = pytesseract.image_to_string(image, lang=ocr_languages)
                text += f"\n\n=== Page {i+1} ===\n{page_text}"
            except Exception as e:
                logger.warning(f"处理第{i+1}页OCR失败: {str(e)}")
                # 继续处理其他页面

        if text.strip():
            # 添加基本元数据
            metadata = {
                "source": file_path,
                "file_name": os.path.basename(file_path),
                "file_type": "scanned_pdf"
            }
            return [Document(page_content=text, metadata=metadata)]

        # 如果OCR没有提取到文本，尝试PyMuPDFLoader作为最后的备选
        logger.warning(f"OCR未能提取到文本，尝试PyMuPDFLoader作为备选: {file_path}")
        try:
            loader = PyMuPDFLoader(file_path)
            docs = loader.load()
            if docs and any(doc.page_content.strip() for doc in docs):
                logger.info(f"PyMuPDFLoader成功加载PDF: {file_path}, 页数: {len(docs)}")
                return docs
        except Exception as e:
            logger.warning(f"PyMuPDFLoader备选方法失败: {str(e)}")

        return []
    except Exception as e:
        logger.error(f"处理扫描版PDF失败: {str(e)}")
        return []

def load_single_document(file_path: str) -> List[Document]:
    """Load a single document from a file path."""
    try:
        # 获取文件扩展名
        ext = "." + file_path.rsplit(".", 1)[-1].lower()

        if ext == ".pdf":
            # 使用PyPDFLoader加载 PDF
            try:
                loader_class, loader_args = LOADER_MAPPING[ext]
                loader = loader_class(file_path, **loader_args)
                docs = loader.load()
                if docs and any(doc.page_content.strip() for doc in docs):
                    logger.info(f"PyPDFLoader 成功加载 PDF: {file_path}, 页数: {len(docs)}")
                    return docs
                # 如果提取的文本为空，可能是扫描版PDF，尝试OCR
                logger.info(f"PyPDFLoader 提取的文本为空，尝试使用OCR: {file_path}")
                return process_scanned_pdf(file_path)
            except Exception as e:
                logger.warning(f"PyPDFLoader 处理失败，尝试OCR: {str(e)}")
                return process_scanned_pdf(file_path)
        elif ext == ".docx":
            return process_docx_with_python_docx(file_path)
        elif ext in LOADER_MAPPING:
            loader_class, loader_args = LOADER_MAPPING[ext]
            logger.info(f"使用标准加载器处理文件: {file_path}")
            loader = loader_class(file_path, **loader_args)
            return loader.load()
        else:
            logger.warning(f"未知文件类型: {file_path}")
            return []
    except Exception as e:
        logger.error(f"加载文件 {file_path} 失败: {str(e)}")
        return []

def process_doc_with_antiword(file_path: str) -> List[Document]:
    """使用antiword处理.doc文件"""
    logger.info(f"使用antiword处理.doc文件: {file_path}")
    try:
        # 检查antiword是否可用
        try:
            result = subprocess.run(["antiword", "-v"], capture_output=True, text=True)
            logger.info(f"antiword版本: {result.stderr.strip()}")  # antiword输出版本信息到stderr
        except FileNotFoundError:
            logger.warning("antiword未安装，尝试安装...")
            if platform.system() == "Windows":
                logger.error("Windows系统需要手动安装antiword")
                # 尝试使用备选方法
                return process_doc_with_fallback(file_path)
            elif platform.system() == "Linux":
                try:
                    subprocess.run(["apt-get", "update"], check=True)
                    subprocess.run(["apt-get", "install", "-y", "antiword"], check=True)
                    logger.info("antiword安装成功")
                except Exception as e:
                    logger.error(f"安装antiword失败: {str(e)}")
                    return process_doc_with_fallback(file_path)
            else:
                logger.error(f"不支持的操作系统: {platform.system()}")
                return process_doc_with_fallback(file_path)

        # 使用antiword提取文本
        result = subprocess.run(["antiword", file_path], capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            text = result.stdout
            logger.info(f"antiword成功提取文本，长度: {len(text)}")
            metadata = {
                "source": file_path,
                "file_name": os.path.basename(file_path),
                "file_type": "doc"
            }
            return [Document(page_content=text, metadata=metadata)]
        else:
            logger.warning(f"antiword提取文本失败: {result.stderr}")
            return process_doc_with_fallback(file_path)
    except Exception as e:
        logger.error(f"使用antiword处理文件失败: {str(e)}")
        return process_doc_with_fallback(file_path)

def process_docx_with_python_docx(file_path: str) -> List[Document]:
    """使用python-docx处理.docx文件"""
    logger.info(f"使用python-docx处理.docx文件: {file_path}")
    try:
        # 检查python-docx是否可用
        try:
            import docx
            logger.info("python-docx可用")
        except ImportError:
            logger.warning("python-docx未安装，尝试安装...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "python-docx"], check=True)
                import docx
                logger.info("python-docx安装成功")
            except Exception as e:
                logger.error(f"安装python-docx失败: {str(e)}")
                # 使用标准加载器作为备选
                return process_doc_with_fallback(file_path)

        # 使用python-docx提取文本
        doc = docx.Document(file_path)
        text = "\n".join([para.text for para in doc.paragraphs])

        # 提取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_text = " | ".join([cell.text for cell in row.cells])
                text += f"\n{row_text}"

        logger.info(f"python-docx成功提取文本，长度: {len(text)}")
        metadata = {
            "source": file_path,
            "file_name": os.path.basename(file_path),
            "file_type": "docx"
        }
        return [Document(page_content=text, metadata=metadata)]
    except Exception as e:
        logger.error(f"使用python-docx处理文件失败: {str(e)}")
        # 使用标准加载器作为备选
        return process_doc_with_fallback(file_path)

def process_doc_with_fallback(file_path: str) -> List[Document]:
    """当antiword失败时的备选方法处理文件"""
    logger.info(f"使用备选方法处理文件: {file_path}")
    try:
        # 尝试使用textract
        try:
            import textract
            logger.info(f"尝试使用textract加载 {file_path}")
            text = textract.process(file_path, encoding='utf-8').decode('utf-8')
            if text.strip():
                logger.info(f"textract成功提取文本，长度: {len(text)}")
                metadata = {
                    "source": file_path,
                    "file_name": os.path.basename(file_path),
                    "file_type": "textract_processed"
                }
                return [Document(page_content=text, metadata=metadata)]
        except Exception as e:
            logger.warning(f"使用textract失败: {str(e)}")

        # 尝试使用二进制方式读取文件
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                # 尝试不同的编码
                for encoding in ['utf-8', 'latin1', 'gbk', 'gb2312', 'gb18030']:
                    try:
                        text = content.decode(encoding)
                        if text.strip():
                            logger.info(f"使用{encoding}编码成功读取文本，长度: {len(text)}")
                            metadata = {
                                "source": file_path,
                                "file_name": os.path.basename(file_path),
                                "file_type": "binary_decoded",
                                "encoding": encoding
                            }
                            return [Document(page_content=text, metadata=metadata)]
                    except UnicodeDecodeError:
                        continue
        except Exception as e:
            logger.warning(f"二进制读取文件失败: {str(e)}")

        # 如果所有方法都失败，返回空列表
        logger.warning(f"所有方法都无法提取文本: {file_path}")
        return []
    except Exception as e:
        logger.error(f"备选方法处理文件失败: {str(e)}")
        return []

def load_documents(source_dir: str, ignored_files: List[str] = []) -> List[Document]:
    """
    Loads all documents from the source documents directory, ignoring specified files
    """
    all_files = []
    for ext in LOADER_MAPPING:
        all_files.extend(
            glob.glob(os.path.join(source_dir, f"**/*{ext}"), recursive=True)
        )
        # 添加大写扩展名支持
        all_files.extend(
            glob.glob(os.path.join(source_dir, f"**/*{ext.upper()}"), recursive=True)
        )

    filtered_files = [file_path for file_path in all_files if file_path not in ignored_files]
    logger.info(f"找到 {len(filtered_files)} 个文件需要处理")

    if not filtered_files:
        logger.warning(f"在 {source_dir} 中没有找到支持的文件")
        return []

    try:
        with Pool(processes=os.cpu_count()) as pool:
            results = []
            with tqdm(total=len(filtered_files), desc='Loading new documents', ncols=80) as pbar:
                for i, docs in enumerate(pool.imap_unordered(load_single_document, filtered_files)):
                    results.extend(docs)
                    logger.info(f"已处理 {i+1}/{len(filtered_files)} 个文件")
                    pbar.update()
            return results
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"加载文档时发生异常: {str(e)}\n{error_trace}")
        # 尝试使用单线程方式加载
        logger.info("尝试使用单线程方式加载文档...")
        results = []
        for file_path in filtered_files:
            try:
                docs = load_single_document(file_path)
                results.extend(docs)
            except Exception as e:
                logger.error(f"加载文件 {file_path} 失败: {str(e)}")
        return results

def process_documents(source_directory, persist_directory, embeddings):
    """处理文档并返回文本块列表"""
    # 获取所有待处理的文件
    all_files = []
    for ext in LOADER_MAPPING:
        all_files.extend(
            glob.glob(os.path.join(source_directory, f"**/*{ext}"), recursive=True)
        )
    logger.info(f"找到 {len(all_files)} 个文件需要处理")

    # 从文件夹名称中获取基本信息
    folder_name = os.path.basename(source_directory)
    knowledge_title = ""
    department = ""
    timestamp = ""
    time_part = ""
    folder_id = ""

    # 文件夹名称格式: timestamp_time_folder_id_title_department (例如：20250422_195049_6259764c_111_生产处)
    folder_parts = folder_name.split('_', 4)  # 最多分割4次，保留标题和部门
    if len(folder_parts) >= 5:
        # 提取时间戳、时间部分和文件夹ID
        timestamp = folder_parts[0]
        time_part = folder_parts[1]
        folder_id = folder_parts[2]
        knowledge_title = folder_parts[3]
        department = folder_parts[4]
       
    elif len(folder_parts) >= 4:
        # 旧格式兼容：timestamp_folder_id_title_department
        timestamp = folder_parts[0]
        folder_id = folder_parts[1]

        # 直接使用文件夹名称中的标题和部门信息
        title_dept_part = folder_parts[3]
        # 尝试从最后一部分分离出部门
        title_dept_parts = title_dept_part.rsplit('_', 1)
        if len(title_dept_parts) == 2:
            knowledge_title = title_dept_parts[0]
            department = title_dept_parts[1]
        else:
            knowledge_title = title_dept_part

    logger.info(f"使用的知识标题: {knowledge_title}, 所属部门: {department}")

    # 使用多进程池并行加载文档
    with Pool(processes=os.cpu_count()) as pool:
        results = []
        with tqdm(total=len(all_files), desc="Loading new documents") as pbar:
            for i, docs in enumerate(
                pool.imap_unordered(load_single_document, all_files)
            ):
                # 为每个文档添加文件名和文件夹名到元数据
                for doc in docs:
                    file_path = doc.metadata.get("source", "")
                    file_name = os.path.basename(file_path) if file_path else ""
                    doc.metadata["file_name"] = file_name
                    doc.metadata["knowledge_title"] = knowledge_title
                    doc.metadata["department"] = department
                    doc.metadata["folder_name"] = folder_name


                    # 添加格式化的日期时间
                    formatted_datetime = format_datetime_from_parts(timestamp, time_part)
                    doc.metadata["datetime"] = formatted_datetime

                results.extend(docs)
                pbar.update()
                logger.info(f"已处理 {i+1}/{len(all_files)} 个文件")

    if not results:
        logger.warning(f"No documents found in {source_directory}")
        return []

    # 分割文本
    logger.info(f"分割文本为块...")
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size, chunk_overlap=chunk_overlap
    )
    texts = text_splitter.split_documents(results)
    logger.info(f"分割后得到 {len(texts)} 个文本块")

    # 为每个文本块添加更详细的ID信息
    total_chunks = len(texts)

    # 获取文件夹名称的前三部分作为唯一标识的一部分
    folder_prefix = ""
    folder_parts = folder_name.split('_')
    if len(folder_parts) >= 3:
        folder_prefix = "_".join(folder_parts[:3])  # 取前三部分
    elif len(folder_parts) > 0:
        folder_prefix = "_".join(folder_parts)  # 如果不足三部分，则使用所有可用部分

    for i, text in enumerate(texts):
        # 保留原有元数据
        text.metadata["total_chunks"] = total_chunks
        text.metadata["chunk_index"] = i + 1

        # 构建更唯一的ID，包含文件夹前缀、总块数和当前块索引
        chunk_id = f"{folder_prefix}_chunk_{i+1}_of_{total_chunks}"
        text.metadata["chunk_id"] = chunk_id

    return texts
    # print(f"Loading documents from {source_directory}")
    # documents = load_documents(source_directory, ignored_files)
    # if not documents:
    #     print("No new documents to load")
    #     exit(0)
    # print(f"Loaded {len(documents)} new documents from {source_directory}")
    # text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    # texts = text_splitter.split_documents(documents)
    # print(f"Split into {len(texts)} chunks of text (max. {chunk_size} tokens each)")
    # return texts

def does_vectorstore_exist(persist_directory: str) -> bool:
    """
    Checks if vectorstore exists
    """
    # 仅检查目录是否存在，不再检查Chroma特定文件
    return os.path.exists(persist_directory) and len(os.listdir(persist_directory)) > 0

def main():
    """Main function to run the ingest process."""
    try:
        # 加载配置文件
        config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.ini")
        try:
            config.read(config_path, encoding='utf-8')
            logger.info(f"成功加载配置文件: {config_path}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            try:
                # 尝试加载当前目录下的配置文件
                config.read("config.ini", encoding='utf-8')
                logger.info("成功加载配置文件 config.ini")
            except Exception as e2:
                logger.error(f"加载配置文件失败: {str(e2)}")

        # 获取数据库路径和源文档目录
        persist_directory = config.get("DATABASE", "PERSIST_DIRECTORY", fallback="Private_GPT/db_DOC_basic_03")
        source_directory = os.environ.get("SOURCE_DIRECTORY", config.get("DOCUMENTS", "SOURCE_DIRECTORY", fallback="Private_GPT/source_documents"))
        vector_db_type = "qdrant"  # 默认使用qdrant

        # 获取embedding配置
        embeddings_model_name = config.get("EMBEDDINGS", "MODEL_NAME", fallback="Private_GPT/sentence-transformers/bge-large-zh-v1.5")
        embedding_type = config.get("EMBEDDINGS", "EMBEDDING_TYPE", fallback="ollama")
        base_url = config.get("EMBEDDINGS", "BASE_URL", fallback="http://localhost:11434")
        ollama_model = config.get("EMBEDDINGS", "OLLAMA_MODEL", fallback="dztech/bge-large-zh:v1.5")

        # 标准化路径
        persist_directory = normalize_path(persist_directory)
        source_directory = normalize_path(source_directory)

        logger.info(f"使用向量数据库类型: {vector_db_type}")
        logger.info(f"使用源文档目录: {source_directory}")

        # 创建embeddings
        logger.info("开始创建embeddings")
        if embedding_type.lower() == "ollama":
            logger.info(f"使用Ollama Embeddings模型: {ollama_model}, 基础URL: {base_url}")
            embeddings = OllamaEmbeddings(model=ollama_model, base_url=base_url)
        else:
            logger.info(f"使用HuggingFace Embeddings模型: {embeddings_model_name}")
            embeddings = HuggingFaceEmbeddings(model_name=embeddings_model_name)

        # 处理文档
        texts = process_documents(source_directory, persist_directory, embeddings)

        # 检查是否有有效的文本块
        if not texts:
            logger.warning("没有找到有效的文本块，无法创建向量数据库")
            # 创建处理完成标记文件，但标记为空结果
            if os.path.exists(source_directory) and os.path.isdir(source_directory):
                with open(os.path.join(source_directory, '.processing_complete'), 'w') as f:
                    f.write('completed_no_content')
            return

        # 根据配置选择向量数据库
        # 使用 Qdrant
        from langchain_community.vectorstores import Qdrant
        from qdrant_client import QdrantClient

        qdrant_host = config.get("QDRANT", "HOST", fallback="localhost")
        qdrant_port = config.getint("QDRANT", "PORT", fallback=7541)

        logger.info(f"连接 Qdrant 数据库: {qdrant_host}:{qdrant_port}")
        client = QdrantClient(host=qdrant_host, port=qdrant_port)

        # 获取存储集合名称
        collection_name_store = config.get("QDRANT", "COLLECTION_NAME_STORE", fallback="")
        if collection_name_store:
            collection_name = collection_name_store
        else:
            # 使用默认集合名称
            collection_name = "documents"

        logger.info(f"使用 Qdrant 集合名称: {collection_name}")

        # 检查集合是否存在
        collections = client.get_collections().collections
        collection_names = [collection.name for collection in collections]

        if collection_name in collection_names:
            # 更新现有集合
            logger.info(f"向现有 Qdrant 集合添加文档: {collection_name}")
            db = Qdrant(client=client, collection_name=collection_name, embeddings=embeddings)
            db.add_documents(texts)
        else:
            # 创建新集合
            logger.info(f"创建新的 Qdrant 集合: {collection_name}")
            db = Qdrant.from_documents(
                texts,
                embeddings,
                url=f"http://{qdrant_host}:{qdrant_port}",
                collection_name=collection_name
            )

        logger.info(f"向量数据库已成功创建/更新")

        # 创建处理完成标记文件
        if os.path.exists(source_directory) and os.path.isdir(source_directory):
            with open(os.path.join(source_directory, '.processing_complete'), 'w') as f:
                f.write('completed')

        logger.info(f"摄取完成！")
    except Exception as e:
        error_trace = traceback.format_exc()
        logger.error(f"处理过程中发生错误: {str(e)}\n{error_trace}")

        # 创建处理失败标记文件
        if os.path.exists(source_directory) and os.path.isdir(source_directory):
            with open(os.path.join(source_directory, '.processing_failed'), 'w') as f:
                f.write(f'处理失败: {str(e)}')

        # 确保脚本以非零状态退出，以便调用者知道发生了错误
        sys.exit(1)


def extract_text_from_file(file_path):
    """从文件中提取文本的通用方法"""
    logger.info(f"尝试从文件中提取文本: {file_path}")
    try:
        # 尝试以不同的编码读取文件
        encodings = ['utf-8', 'latin1', 'gbk', 'gb2312', 'gb18030']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    text = f.read()
                    if text.strip():
                        logger.info(f"使用{encoding}编码成功读取文本，长度: {len(text)}")
                        return text
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.warning(f"使用{encoding}编码读取文件失败: {str(e)}")

        # 如果文本读取失败，尝试二进制读取
        with open(file_path, 'rb') as f:
            content = f.read()
            # 尝试检测编码
            try:
                import chardet
                detected = chardet.detect(content)
                if detected['confidence'] > 0.5:
                    encoding = detected['encoding']
                    text = content.decode(encoding)
                    logger.info(f"使用检测到的编码{encoding}成功读取文本，长度: {len(text)}")
                    return text
            except ImportError:
                logger.warning("chardet库未安装，无法检测编码")
            except Exception as e:
                logger.warning(f"使用检测到的编码读取失败: {str(e)}")

        logger.warning(f"无法从文件中提取文本: {file_path}")
        return ""
    except Exception as e:
        logger.error(f"提取文本失败: {str(e)}")
        return ""

if __name__ == "__main__":
    main()
