/* 管理员反馈页面样式 */
:root {
    --primary-color: #1890ff;
    --secondary-color: #52c41a;
    --danger-color: #f5222d;
    --warning-color: #faad14;
    --text-color: #333;
    --light-text: #666;
    --border-color: #e8e8e8;
    --bg-color: #f5f5f5;
    --card-bg: #fff;
    --hover-color: #f0f0f0;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 基础布局 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: var(--bg-color);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--card-bg);
    border-radius: 5px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.logo h1 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--text-color);
}

.admin-actions {
    display: flex;
    gap: 10px;
}

.admin-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.admin-footer {
    text-align: center;
    padding: 15px;
    margin-top: 20px;
    color: var(--light-text);
    font-size: 0.9rem;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #40a9ff;
}

.btn-secondary {
    background-color: #f0f0f0;
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #d9d9d9;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #ff4d4f;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 筛选区域 */
.filter-section {
    background-color: var(--card-bg);
    border-radius: 5px;
    padding: 15px 20px;
    box-shadow: var(--shadow);
}

.filter-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
    color: var(--text-color);
}

.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: white;
    min-width: 120px;
}

.filter-checkbox {
    width: 16px;
    height: 16px;
}

/* 数据区域 */
.data-section {
    background-color: var(--card-bg);
    border-radius: 5px;
    padding: 15px 20px;
    box-shadow: var(--shadow);
    flex: 1;
}

.data-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.data-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: var(--light-text);
}

.page-size-select {
    padding: 5px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    margin-left: 5px;
}

/* 反馈列表 */
.feedback-list {
    min-height: 400px;
    position: relative;
}

.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--light-text);
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.feedback-item {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: white;
    transition: all 0.3s;
}

.feedback-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feedback-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.feedback-meta {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.feedback-id {
    font-size: 0.8rem;
    color: var(--light-text);
}

.feedback-timestamp {
    font-size: 0.8rem;
    color: var(--light-text);
}

.feedback-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.rating-badge {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: bold;
}

.rating-positive {
    background-color: #f6ffed;
    color: var(--secondary-color);
    border: 1px solid #b7eb8f;
}

.rating-negative {
    background-color: #fff2f0;
    color: var(--danger-color);
    border: 1px solid #ffccc7;
}

.feedback-content {
    margin-bottom: 10px;
}

.feedback-question {
    font-weight: bold;
    margin-bottom: 5px;
}

.feedback-answer {
    color: var(--light-text);
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.feedback-comment {
    background-color: #f9f9f9;
    padding: 8px;
    border-radius: 4px;
    font-style: italic;
    color: var(--light-text);
    margin-top: 10px;
}

.feedback-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
}

.deleted-item {
    opacity: 0.6;
    background-color: #fafafa;
}

.deleted-badge {
    background-color: #f5f5f5;
    color: #999;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    margin-left: 10px;
}

/* 分页控制 */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
}

.page-info {
    font-size: 0.9rem;
    color: var(--light-text);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 80%;
    max-width: 900px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.2rem;
}

.close {
    color: var(--light-text);
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
}

.feedback-detail {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.detail-group {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    overflow: hidden;
}

.detail-group h3 {
    margin: 0;
    padding: 10px 15px;
    background-color: #f5f5f5;
    font-size: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.detail-content {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
    background-color: white;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.timestamp {
    font-size: 0.8rem;
    color: var(--light-text);
}

.comment-content {
    background-color: #f9f9f9;
    padding: 10px;
    border-radius: 4px;
    font-style: italic;
}

.session-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.session-info p {
    margin: 0;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .admin-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .filter-controls {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .data-controls {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px auto;
    }
}
