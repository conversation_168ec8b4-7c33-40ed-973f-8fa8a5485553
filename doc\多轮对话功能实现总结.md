# 多轮对话功能实现总结

## 概述

本次修改实现了项目的多轮对话功能，支持RAG、工具调用和普通对话三种场景。采用前端传递历史消息的方案，保持后端无状态设计。

## 修改文件列表

### 1. 前端修改
- **static/js/main.js**
  - 修改 `sendMessage()` 函数
  - 在请求中包含历史消息数组
  - 将 `localChatHistory` 转换为标准格式传递给后端

### 2. 后端接口修改
- **main.py**
  - 修改 `run_workflow()` 函数
  - 解析请求中的 `history` 参数
  - 将历史消息传递给处理函数

### 3. RAG处理修改
- **src/privateGPT_res.py**
  - 修改 `search_local_information_stream()` 函数签名，添加 `history` 参数
  - 创建包含历史对话的增强提示词模板
  - 在QA链调用时传递历史信息

### 4. 普通对话修改
- **src/privateGPT_res.py**
  - 修改 `stream_chat()` 函数签名，添加 `history` 参数
  - 根据是否有历史消息创建不同的提示词模板
  - 构建包含历史对话的上下文

### 5. 工具调用代理修改

#### 久安代理
- **src/jiuan_agent/agent_service.py**
  - 修改 `function_calling_agent()` 函数签名，添加 `history` 参数
  - 在创建LLM代理时传递历史信息

- **src/jiuan_agent/agent_core.py**
  - 修改 `create_function_calling_agent()` 函数，支持历史参数
  - 修改 `JiuanFunctionCallingAgent.invoke_async()` 方法
  - 修改 `_create_function_calling_prompt()` 方法，包含历史对话上下文

#### LangChain代理
- **src/agent/agent_service.py**
  - 修改 `function_calling_agent()` 函数签名，添加 `history` 参数
  - 在代理调用时构建包含历史对话的输入
  - 修改动态工具处理函数

- **src/agent/agent_core.py**
  - 修改 `CustomFunctionCallingAgent.invoke()` 方法
  - 修改 `_create_function_calling_prompt()` 方法，支持历史对话

- **src/agent/dynamic_agent_core.py**
  - 修改 `DynamicCustomFunctionCallingAgent.invoke()` 方法
  - 修改 `_create_function_calling_prompt()` 方法，支持历史对话

## 数据格式

### 前端请求格式
```javascript
{
  "query": "当前用户问题",
  "enable_rag": true,
  "history": [
    {"role": "user", "content": "历史用户消息1"},
    {"role": "assistant", "content": "历史助手回答1"},
    {"role": "user", "content": "历史用户消息2"},
    {"role": "assistant", "content": "历史助手回答2"}
  ]
}
```

### 历史消息格式
- 每条消息包含 `role` 和 `content` 字段
- `role` 可以是 "user" 或 "assistant"
- `content` 包含消息的具体内容

## 功能特点

### 1. 兼容性
- 保持向后兼容，支持原有的单轮对话
- 如果没有历史消息，使用原有的提示词模板

### 2. 三种场景支持
- **RAG模式**: 结合历史对话和检索文档生成回答
- **普通对话**: 基于历史对话上下文进行闲聊
- **工具调用**: 在LLM代理阶段考虑历史对话，帮助理解上下文

### 3. 工具调用策略
- 直接参数提取部分不考虑历史对话（按要求）
- 只在创建LLM代理时传递历史对话上下文
- 支持久安代理和LangChain代理两种模式

### 4. 无状态设计
- 后端不存储会话状态
- 前端完全控制历史消息的传递
- 便于扩展和维护

## 测试

创建了 `test_multi_turn_chat.py` 测试脚本，包含：
- 请求格式验证
- 单轮对话测试
- 多轮普通对话测试
- 多轮RAG对话测试（可选）

## 使用方法

1. 前端在发送请求时包含历史消息数组
2. 后端自动识别并处理历史对话
3. 根据场景选择相应的处理方式
4. 返回考虑历史上下文的回答

## 注意事项

1. 历史消息会增加提示词长度，需要注意模型的上下文限制
2. 前端可以控制历史消息的数量，建议保留最近的10-20轮对话
3. 工具调用中的直接参数提取不受历史对话影响，保持原有逻辑
4. 所有修改都保持了原有代码的结构和风格


{
  query: "用户问题",
  enable_rag: true,
  history: [
    { role: "user", content: "之前的用户问题1" },
    { role: "assistant", content: "之前的助手回答1" },
    { role: "user", content: "之前的用户问题2" },
    { role: "assistant", content: "之前的助手回答2" }
  ]
}


// 修改sendMessage函数
async function sendMessage() {
  // ... 现有代码 ...
  
  // 构建请求数据，包含历史消息
  const requestData = {
    query: message,
    enable_rag: true, // 或根据UI设置
    history: localChatHistory.map(item => [
      { role: "user", content: item.user },
      { role: "assistant", content: item.assistant }
    ]).flat()
  };

  const response = await fetch('/run_workflow/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'session-id': sessionId,
      'userId': 'admin'
    },
    body: JSON.stringify(requestData),
  });
  
  // ... 其余代码保持不变 ...
}

注：
限制一下5轮对话
思考的内容和引用的信息不要加进来
久安不传历史会话