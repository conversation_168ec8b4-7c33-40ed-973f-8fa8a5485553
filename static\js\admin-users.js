// 全局变量
let currentAction = null;
let currentUserId = null;
let currentTab = 'pending';

// 通用的401错误处理函数
function handleUnauthorized() {
    localStorage.removeItem('user_token');
    localStorage.removeItem('user_info');
    window.location.href = '/login';
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuth();
    loadPendingUsers();
});

// 检查管理员权限
function checkAdminAuth() {
    const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
    const token = localStorage.getItem('user_token');
    
    if (!token || !userInfo.role || userInfo.role !== 'admin') {
        alert('您没有权限访问此页面');
        window.location.href = '/login';
        return;
    }
}

// 切换标签页
function switchTab(tab) {
    // 更新按钮状态
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[onclick="switchTab('${tab}')"]`).classList.add('active');
    
    // 更新内容显示
    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
    document.getElementById(`${tab}-tab`).classList.add('active');
    
    currentTab = tab;
    
    // 加载对应数据
    if (tab === 'pending') {
        loadPendingUsers();
    } else if (tab === 'all') {
        loadAllUsers();
    }
}

// 加载待审核用户
async function loadPendingUsers() {
    const loading = document.getElementById('pending-loading');
    const content = document.getElementById('pending-content');
    const tbody = document.getElementById('pending-tbody');
    const empty = document.getElementById('pending-empty');
    
    loading.style.display = 'block';
    content.style.display = 'none';
    
    try {
        const response = await fetch('/api/admin/pending-users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('user_token')}`
            }
        });

        if (response.status === 401) {
            handleUnauthorized();
            return;
        }

        const data = await response.json();
        
        if (data.success) {
            tbody.innerHTML = '';
            
            if (data.users.length === 0) {
                empty.style.display = 'block';
                document.getElementById('pending-table').style.display = 'none';
            } else {
                empty.style.display = 'none';
                document.getElementById('pending-table').style.display = 'table';
                
                data.users.forEach(user => {
                    const row = createPendingUserRow(user);
                    tbody.appendChild(row);
                });
            }
        } else {
            throw new Error(data.message || '加载失败');
        }
    } catch (error) {
        console.error('加载待审核用户失败:', error);
        alert('加载待审核用户失败: ' + error.message);
    } finally {
        loading.style.display = 'none';
        content.style.display = 'block';
    }
}

// 加载所有用户
async function loadAllUsers() {
    const loading = document.getElementById('all-loading');
    const content = document.getElementById('all-content');
    const tbody = document.getElementById('all-tbody');
    const empty = document.getElementById('all-empty');
    
    loading.style.display = 'block';
    content.style.display = 'none';
    
    try {
        const response = await fetch('/api/admin/all-users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('user_token')}`
            }
        });

        if (response.status === 401) {
            handleUnauthorized();
            return;
        }

        const data = await response.json();
        
        if (data.success) {
            tbody.innerHTML = '';
            
            if (data.users.length === 0) {
                empty.style.display = 'block';
                document.getElementById('all-table').style.display = 'none';
            } else {
                empty.style.display = 'none';
                document.getElementById('all-table').style.display = 'table';
                
                data.users.forEach(user => {
                    const row = createAllUserRow(user);
                    tbody.appendChild(row);
                });
            }
        } else {
            throw new Error(data.message || '加载失败');
        }
    } catch (error) {
        console.error('加载所有用户失败:', error);
        alert('加载所有用户失败: ' + error.message);
    } finally {
        loading.style.display = 'none';
        content.style.display = 'block';
    }
}

// 创建待审核用户行
function createPendingUserRow(user) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${user.username}</td>
        <td>${formatDate(user.created_at)}</td>
        <td><span class="status-badge status-${user.status}">${getStatusText(user.status)}</span></td>
        <td>
            <div class="actions">
                <button class="btn btn-success" onclick="approveUser('${user.user_id}', '${user.username}')">
                    <i class="fas fa-check"></i> 通过
                </button>
                <button class="btn btn-danger" onclick="rejectUser('${user.user_id}', '${user.username}')">
                    <i class="fas fa-times"></i> 拒绝
                </button>
            </div>
        </td>
    `;
    return row;
}

// 创建所有用户行
function createAllUserRow(user) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${user.username}</td>
        <td><span class="role-badge role-${user.role}">${getRoleText(user.role)}</span></td>
        <td><span class="status-badge status-${user.status}">${getStatusText(user.status)}</span></td>
        <td>${formatDate(user.created_at)}</td>
        <td>${user.approved_at ? formatDate(user.approved_at) : '-'}</td>
        <td>${user.approved_by || '-'}</td>
    `;
    return row;
}

// 审核通过用户
function approveUser(userId, username) {
    currentAction = 'approve';
    currentUserId = userId;
    document.getElementById('modal-title').textContent = `审核通过用户: ${username}`;
    document.getElementById('confirm-btn').className = 'btn btn-success';
    document.getElementById('confirm-btn').innerHTML = '<i class="fas fa-check"></i> 确认通过';
    document.getElementById('approval-modal').style.display = 'block';
}

// 拒绝用户
function rejectUser(userId, username) {
    currentAction = 'reject';
    currentUserId = userId;
    document.getElementById('modal-title').textContent = `拒绝用户: ${username}`;
    document.getElementById('confirm-btn').className = 'btn btn-danger';
    document.getElementById('confirm-btn').innerHTML = '<i class="fas fa-times"></i> 确认拒绝';
    document.getElementById('approval-modal').style.display = 'block';
}

// 确认操作
async function confirmAction() {
    if (!currentAction || !currentUserId) return;
    
    const reason = document.getElementById('approval-reason').value;
    const confirmBtn = document.getElementById('confirm-btn');
    const originalText = confirmBtn.innerHTML;
    
    confirmBtn.disabled = true;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    
    try {
        const response = await fetch(`/api/admin/${currentAction}-user`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('user_token')}`
            },
            body: JSON.stringify({
                user_id: currentUserId,
                reason: reason
            })
        });

        if (response.status === 401) {
            handleUnauthorized();
            return;
        }

        const data = await response.json();

        if (data.success) {
            alert(`用户${currentAction === 'approve' ? '审核通过' : '拒绝'}成功`);
            closeModal();
            // 重新加载当前标签页数据
            if (currentTab === 'pending') {
                loadPendingUsers();
            } else {
                loadAllUsers();
            }
        } else {
            throw new Error(data.message || '操作失败');
        }
    } catch (error) {
        console.error('操作失败:', error);
        alert('操作失败: ' + error.message);
    } finally {
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = originalText;
    }
}

// 关闭模态框
function closeModal() {
    document.getElementById('approval-modal').style.display = 'none';
    document.getElementById('approval-reason').value = '';
    currentAction = null;
    currentUserId = null;
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('user_token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function getStatusText(status) {
    const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝'
    };
    return statusMap[status] || status;
}

function getRoleText(role) {
    const roleMap = {
        'admin': '管理员',
        'user': '普通用户'
    };
    return roleMap[role] || role;
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('approval-modal');
    if (event.target === modal) {
        closeModal();
    }
}
