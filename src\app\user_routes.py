"""
用户管理相关的API路由
"""
from fastapi import APIRouter, Request, HTTPException, Depends
from fastapi.responses import JSONResponse
from fastapi.security import HTTPAuthorizationCredentials
from .auth import AuthService, security, verify_token
import logging

logger = logging.getLogger(__name__)

def create_user_router(db_manager):
    """创建用户路由"""
    router = APIRouter()
    auth_service = AuthService(db_manager)
    
    # 创建依赖注入函数
    def get_current_user_with_db(credentials: HTTPAuthorizationCredentials = Depends(security)):
        """获取当前用户（带数据库连接）"""
        try:
            username = verify_token(credentials)
            user = db_manager.get_user_by_username(username)
            if user is None:
                raise HTTPException(status_code=401, detail="用户不存在")
            if user['status'] != 'approved':
                raise HTTPException(status_code=401, detail="用户未通过审核")
            return user
        except Exception as e:
            raise HTTPException(status_code=401, detail="认证失败")

    def get_current_admin_with_db(current_user: dict = Depends(get_current_user_with_db)):
        """获取当前管理员用户（带数据库连接）"""
        if current_user['role'] != 'admin':
            raise HTTPException(status_code=403, detail="需要管理员权限")
        return current_user
    
    @router.post("/api/register")
    async def register_user(request: Request):
        """用户注册"""
        try:
            data = await request.json()
            username = data.get("username", "").strip()
            password = data.get("password", "").strip()
            
            result = auth_service.register_user(username, password)
            
            if result["success"]:
                return JSONResponse(content=result)
            else:
                return JSONResponse(
                    status_code=400,
                    content=result
                )
                
        except Exception as e:
            logger.error(f"用户注册API失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "注册失败，请稍后重试"}
            )
    
    @router.post("/api/login")
    async def login_user(request: Request):
        """用户登录"""
        try:
            data = await request.json()
            username = data.get("username", "").strip()
            password = data.get("password", "").strip()

            result = auth_service.login_user(username, password)

            if result["success"]:
                return JSONResponse(content=result)
            else:
                return JSONResponse(
                    status_code=401,
                    content=result
                )

        except Exception as e:
            logger.error(f"用户登录API失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "登录失败，请稍后重试"}
            )

    @router.get("/api/verify-token")
    async def verify_token_endpoint(current_user: dict = Depends(get_current_user_with_db)):
        """验证token有效性"""
        try:
            return JSONResponse(content={
                "success": True,
                "user": {
                    "user_id": current_user['user_id'],
                    "username": current_user['username'],
                    "role": current_user['role'],
                    "status": current_user['status']
                }
            })
        except Exception as e:
            logger.error(f"Token验证失败: {str(e)}")
            return JSONResponse(
                status_code=401,
                content={"success": False, "message": "Token无效"}
            )
    
    @router.get("/api/admin/pending-users")
    async def get_pending_users(current_admin: dict = Depends(get_current_admin_with_db)):
        """获取待审核用户列表"""
        try:
            users = db_manager.get_pending_users()
            return JSONResponse(
                content={"success": True, "users": users}
            )
        except Exception as e:
            logger.error(f"获取待审核用户失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "获取用户列表失败"}
            )
    
    @router.get("/api/admin/all-users")
    async def get_all_users(current_admin: dict = Depends(get_current_admin_with_db)):
        """获取所有用户列表"""
        try:
            users = db_manager.get_all_users()
            return JSONResponse(
                content={"success": True, "users": users}
            )
        except Exception as e:
            logger.error(f"获取所有用户失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "获取用户列表失败"}
            )
    
    @router.post("/api/admin/approve-user")
    async def approve_user(request: Request, current_admin: dict = Depends(get_current_admin_with_db)):
        """审核通过用户"""
        try:
            data = await request.json()
            user_id = data.get("user_id")
            reason = data.get("reason", "")
            
            if not user_id:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": "用户ID不能为空"}
                )
            
            success = db_manager.approve_user(user_id, current_admin['user_id'], reason)
            
            if success:
                logger.info(f"管理员 {current_admin['username']} 审核通过用户: {user_id}")
                return JSONResponse(
                    content={"success": True, "message": "用户审核通过"}
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": "审核失败"}
                )
                
        except Exception as e:
            logger.error(f"审核用户失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "审核失败，请稍后重试"}
            )
    
    @router.post("/api/admin/reject-user")
    async def reject_user(request: Request, current_admin: dict = Depends(get_current_admin_with_db)):
        """拒绝用户"""
        try:
            data = await request.json()
            user_id = data.get("user_id")
            reason = data.get("reason", "")
            
            if not user_id:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": "用户ID不能为空"}
                )
            
            success = db_manager.reject_user(user_id, current_admin['user_id'], reason)
            
            if success:
                logger.info(f"管理员 {current_admin['username']} 拒绝用户: {user_id}")
                return JSONResponse(
                    content={"success": True, "message": "用户已拒绝"}
                )
            else:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": "拒绝失败"}
                )
                
        except Exception as e:
            logger.error(f"拒绝用户失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "拒绝失败，请稍后重试"}
            )
    
    return router
