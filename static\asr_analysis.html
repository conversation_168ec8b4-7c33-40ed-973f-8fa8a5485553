<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>应急语音识别分析 - 应急指挥智能体</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <link rel="stylesheet" href="/static/css/feedback.css">
  <link rel="stylesheet" href="/static/css/input-fix.css">
  <link rel="stylesheet" href="/static/css/modal.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* ASR专用样式 */
    .asr-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .asr-header {
      text-align: center;
      margin-bottom: 30px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 10px;
    }

    .asr-header h1 {
      margin: 0 0 10px 0;
      font-size: 2.5em;
    }

    .asr-header p {
      margin: 0;
      font-size: 1.1em;
      opacity: 0.9;
    }

    .asr-tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 2px solid #e0e0e0;
    }

    .tab-button {
      padding: 12px 24px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 16px;
      border-bottom: 2px solid transparent;
      transition: all 0.3s;
    }

    .tab-button.active {
      color: #667eea;
      border-bottom-color: #667eea;
      font-weight: bold;
    }

    .tab-content {
      display: none;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .tab-content.active {
      display: block;
    }

    .upload-area {
      border: 2px dashed #ccc;
      border-radius: 8px;
      padding: 40px;
      text-align: center;
      margin-bottom: 20px;
      transition: border-color 0.3s;
    }

    .upload-area:hover {
      border-color: #667eea;
    }

    .upload-area.dragover {
      border-color: #667eea;
      background-color: #f8f9ff;
    }

    .upload-icon {
      font-size: 48px;
      color: #ccc;
      margin-bottom: 10px;
    }

    .realtime-controls {
      display: flex;
      gap: 15px;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .mode-selector {
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .mode-button {
      padding: 8px 16px;
      border: 2px solid #667eea;
      background: white;
      color: #667eea;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s;
    }

    .mode-button.active {
      background: #667eea;
      color: white;
    }

    .realtime-button {
      padding: 12px 24px;
      border: none;
      border-radius: 25px;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
      transition: all 0.3s;
      min-width: 120px;
    }

    .start-button {
      background: #28a745;
      color: white;
    }

    .start-button:hover {
      background: #218838;
    }

    .stop-button {
      background: #dc3545;
      color: white;
    }

    .stop-button:hover {
      background: #c82333;
    }

    .recording-indicator {
      color: #dc3545;
      font-weight: bold;
      animation: pulse 1s infinite;
    }

    .transcript-area {
      background: #f8f9fa;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 20px;
      min-height: 200px;
      font-family: monospace;
      white-space: pre-wrap;
      overflow-y: auto;
    }

    .transcript-item {
      margin-bottom: 10px;
      padding: 8px;
      background: white;
      border-radius: 4px;
      border-left: 4px solid #667eea;
    }

    .transcript-meta {
      font-size: 12px;
      color: #666;
      margin-bottom: 5px;
    }

    .status-panel {
      background: #e8f5e8;
      border: 1px solid #28a745;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .status-panel.error {
      background: #f8d7da;
      border-color: #dc3545;
      color: #721c24;
    }

    .status-panel.warning {
      background: #fff3cd;
      border-color: #ffc107;
      color: #856404;
    }

    .task-list {
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      max-height: 300px;
      overflow-y: auto;
    }

    .task-item {
      padding: 12px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .task-item:last-child {
      border-bottom: none;
    }

    .task-info {
      flex: 1;
    }

    .task-actions {
      display: flex;
      gap: 8px;
    }

    .action-button {
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }

    .end-task-button {
      background: #dc3545;
      color: white;
    }

    .info-button {
      background: #17a2b8;
      color: white;
    }

    .speaker-input {
      margin-bottom: 15px;
    }

    .speaker-input label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }

    .speaker-input input {
      width: 200px;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .file-info {
      background: #e8f5e8;
      border: 1px solid #28a745;
      border-radius: 4px;
      padding: 10px;
      margin-top: 10px;
      display: none;
    }

    .progress-bar {
      width: 100%;
      height: 20px;
      background-color: #f0f0f0;
      border-radius: 10px;
      overflow: hidden;
      margin: 10px 0;
    }

    .progress-fill {
      height: 100%;
      background-color: #28a745;
      width: 0%;
      transition: width 0.3s ease;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .navigation-buttons {
      text-align: center;
      margin-top: 30px;
    }

    .nav-button {
      padding: 12px 24px;
      margin: 0 10px;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      transition: background 0.3s;
    }

    .nav-button:hover {
      background: #5a67d8;
    }
  </style>
</head>
<body>
  <div class="asr-container">
    <!-- 页面头部 -->
    <div class="asr-header">
      <h1><i class="fas fa-microphone"></i> 应急语音识别分析</h1>
      <p>支持PCM文件转写和实时语音识别</p>
    </div>

    <!-- 服务状态面板 -->
    <div id="status-panel" class="status-panel">
      <i class="fas fa-spinner fa-spin"></i> 正在检查服务状态...
    </div>

    <!-- 功能选项卡 -->
    <div class="asr-tabs">
      <button class="tab-button active" onclick="switchTab('file-transfer')">
        <i class="fas fa-file-audio"></i> 文件转写
      </button>
      <button class="tab-button" onclick="switchTab('realtime-single')">
        <i class="fas fa-microphone"></i> 实时转写(单路)
      </button>
      <button class="tab-button" onclick="switchTab('realtime-meeting')">
        <i class="fas fa-users"></i> 实时转写(会议)
      </button>
      <button class="tab-button" onclick="switchTab('task-management')">
        <i class="fas fa-tasks"></i> 任务管理
      </button>
    </div>

    <!-- 文件转写选项卡 -->
    <div id="file-transfer" class="tab-content active">
      <h3><i class="fas fa-upload"></i> PCM文件转写</h3>
      <div class="upload-area" id="upload-area">
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <div>
          <p>将PCM音频文件拖放到此处或点击选择文件</p>
          <p style="font-size: 14px; color: #666;">支持格式：.pcm</p>
        </div>
        <input type="file" id="file-input" accept=".pcm" style="display: none;">
      </div>
      
      <div id="file-info" class="file-info">
        <div id="file-details"></div>
        <div class="progress-bar">
          <div id="upload-progress" class="progress-fill"></div>
        </div>
      </div>

      <div id="file-result" class="transcript-area" style="display: none;">
        <h4>转写结果：</h4>
        <div id="file-transcript"></div>
      </div>
    </div>

    <!-- 实时转写(单路)选项卡 -->
    <div id="realtime-single" class="tab-content">
      <h3><i class="fas fa-microphone"></i> 实时语音转写 (单路模式)</h3>
      
      <div class="speaker-input">
        <label for="single-speaker">说话人标识：</label>
        <input type="text" id="single-speaker" value="用户" placeholder="输入说话人名称">
      </div>

      <div class="realtime-controls">
        <button id="single-start-btn" class="realtime-button start-button">
          <i class="fas fa-play"></i> 开始转写
        </button>
        <button id="single-stop-btn" class="realtime-button stop-button" disabled>
          <i class="fas fa-stop"></i> 停止转写
        </button>
        <div id="single-status" class="status-text"></div>
      </div>

      <div id="single-transcript" class="transcript-area">
        <p style="color: #666;">点击"开始转写"开始实时语音识别...</p>
      </div>
    </div>

    <!-- 实时转写(会议)选项卡 -->
    <div id="realtime-meeting" class="tab-content">
      <h3><i class="fas fa-users"></i> 实时语音转写 (会议模式)</h3>
      
      <div class="speaker-input">
        <label for="meeting-creator">会议创建人：</label>
        <input type="text" id="meeting-creator" value="管理员" placeholder="输入创建人名称">
      </div>

      <div class="realtime-controls">
        <button id="meeting-start-btn" class="realtime-button start-button">
          <i class="fas fa-play"></i> 开始会议转写
        </button>
        <button id="meeting-stop-btn" class="realtime-button stop-button" disabled>
          <i class="fas fa-stop"></i> 停止会议转写
        </button>
        <div id="meeting-status" class="status-text"></div>
      </div>

      <div id="meeting-transcript" class="transcript-area">
        <p style="color: #666;">点击"开始会议转写"开始多人语音识别...</p>
      </div>
    </div>

    <!-- 任务管理选项卡 -->
    <div id="task-management" class="tab-content">
      <h3><i class="fas fa-tasks"></i> 任务管理</h3>
      
      <div style="margin-bottom: 20px;">
        <button id="refresh-tasks-btn" class="realtime-button start-button">
          <i class="fas fa-sync"></i> 刷新任务列表
        </button>
      </div>

      <div id="task-list" class="task-list">
        <div style="padding: 20px; text-align: center; color: #666;">
          暂无活跃任务
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation-buttons">
      <a href="/" class="nav-button">
        <i class="fas fa-home"></i> 返回主页
      </a>
      <a href="/voice_chat" class="nav-button">
        <i class="fas fa-comments"></i> 语音对话
      </a>
    </div>
  </div>

  <script>
    // 全局变量
    let currentTab = 'file-transfer';
    let asrService = null;
    let currentWebSocket = null;
    let currentTaskId = null;
    let isRealTimeActive = false;
    let mediaStream = null;
    let audioContext = null;
    let processor = null;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      checkLoginStatus();
      initializeASR();
      setupEventListeners();
      checkServiceStatus();
    });

    // 检查登录状态
    function checkLoginStatus() {
      const token = localStorage.getItem('user_token');
      const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');

      if (!token || !userInfo.username) {
        // 未登录，跳转到登录页面
        window.location.href = '/login';
        return false;
      }
      return true;
    }

    // 初始化ASR服务
    function initializeASR() {
      console.log('ASR服务初始化中...');
    }

    // 设置事件监听器
    function setupEventListeners() {
      // 文件上传相关
      const uploadArea = document.getElementById('upload-area');
      const fileInput = document.getElementById('file-input');

      uploadArea.addEventListener('click', () => fileInput.click());
      uploadArea.addEventListener('dragover', handleDragOver);
      uploadArea.addEventListener('dragleave', handleDragLeave);
      uploadArea.addEventListener('drop', handleDrop);
      fileInput.addEventListener('change', handleFileSelect);

      // 实时转写按钮
      document.getElementById('single-start-btn').addEventListener('click', () => startRealtimeASR('single'));
      document.getElementById('single-stop-btn').addEventListener('click', () => stopRealtimeASR('single'));
      document.getElementById('meeting-start-btn').addEventListener('click', () => startRealtimeASR('meeting'));
      document.getElementById('meeting-stop-btn').addEventListener('click', () => stopRealtimeASR('meeting'));

      // 任务管理
      document.getElementById('refresh-tasks-btn').addEventListener('click', refreshTaskList);
    }

    // 切换选项卡
    function switchTab(tabName) {
      // 隐藏所有选项卡内容
      document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
      });
      
      // 移除所有按钮的激活状态
      document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
      });

      // 显示选中的选项卡
      document.getElementById(tabName).classList.add('active');
      event.target.classList.add('active');
      
      currentTab = tabName;
    }

         // 检查服务状态
     async function checkServiceStatus() {
       try {
         const token = localStorage.getItem('user_token');
         if (!token) {
           showStatusPanel('请先登录', 'error');
           return;
         }

        const response = await fetch('/api/asr/status', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const result = await response.json();
        
        if (result.code === '0') {
          const data = result.data;
          if (data.enabled && data.available) {
            showStatusPanel(`服务正常运行 (${data.base_url})`, 'success');
          } else if (data.enabled && !data.available) {
            showStatusPanel('服务已启用但无法连接', 'warning');
          } else {
            showStatusPanel('ASR服务未启用', 'warning');
          }
        } else {
          showStatusPanel('无法获取服务状态', 'error');
        }
      } catch (error) {
        console.error('检查服务状态失败:', error);
        showStatusPanel('服务状态检查失败', 'error');
      }
    }

    // 显示状态面板
    function showStatusPanel(message, type = 'info') {
      const panel = document.getElementById('status-panel');
      panel.className = `status-panel ${type}`;
      
      let icon = 'fas fa-info-circle';
      if (type === 'success') icon = 'fas fa-check-circle';
      else if (type === 'error') icon = 'fas fa-exclamation-triangle';
      else if (type === 'warning') icon = 'fas fa-exclamation-circle';
      
      panel.innerHTML = `<i class="${icon}"></i> ${message}`;
    }

    // 文件拖拽处理
    function handleDragOver(e) {
      e.preventDefault();
      e.stopPropagation();
      e.currentTarget.classList.add('dragover');
    }

    function handleDragLeave(e) {
      e.preventDefault();
      e.stopPropagation();
      e.currentTarget.classList.remove('dragover');
    }

    function handleDrop(e) {
      e.preventDefault();
      e.stopPropagation();
      e.currentTarget.classList.remove('dragover');
      
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFileUpload(files[0]);
      }
    }

    function handleFileSelect(e) {
      const file = e.target.files[0];
      if (file) {
        handleFileUpload(file);
      }
    }

    // 处理文件上传
    async function handleFileUpload(file) {
      // 检查文件类型
      if (!file.name.toLowerCase().endsWith('.pcm')) {
        alert('只支持PCM格式的音频文件');
        return;
      }

      const fileInfo = document.getElementById('file-info');
      const fileDetails = document.getElementById('file-details');
      const progressBar = document.getElementById('upload-progress');
      const resultDiv = document.getElementById('file-result');
      const transcriptDiv = document.getElementById('file-transcript');

      // 显示文件信息
      fileDetails.innerHTML = `
        <strong>文件名：</strong>${file.name}<br>
        <strong>大小：</strong>${(file.size / 1024 / 1024).toFixed(2)} MB
      `;
      fileInfo.style.display = 'block';
      resultDiv.style.display = 'none';

      try {
        const token = localStorage.getItem('user_token');
        if (!token) {
          alert('请先登录');
          return;
        }

        const formData = new FormData();
        formData.append('audio_file', file);

        // 模拟进度
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += 5;
          if (progress >= 90) {
            clearInterval(progressInterval);
          }
          progressBar.style.width = progress + '%';
        }, 100);

        const response = await fetch('/api/asr/file/transfer', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          },
          body: formData
        });

        clearInterval(progressInterval);
        progressBar.style.width = '100%';

        const result = await response.json();

        if (result.code === '0') {
          // 显示转写结果
          const transcripts = result.data.list || [];
          transcriptDiv.innerHTML = transcripts.map((text, index) => 
            `<div class="transcript-item">
              <div class="transcript-meta">片段 ${index + 1}</div>
              <div>${text}</div>
            </div>`
          ).join('');
          resultDiv.style.display = 'block';
        } else {
          alert('转写失败: ' + result.msg);
        }

      } catch (error) {
        console.error('文件上传失败:', error);
        alert('文件上传失败: ' + error.message);
      }
    }

    // 开始实时ASR
    async function startRealtimeASR(mode) {
      try {
        const token = localStorage.getItem('user_token');
        if (!token) {
          alert('请先登录');
          return;
        }

        // 修复：为所有模式都设置creator和speaker参数
        let params;
        if (mode === 'single') {
          const speaker = document.getElementById('single-speaker').value || '用户';
          params = new URLSearchParams({
            mode: mode,
            creator: speaker, // 单路模式：creator和speaker相同
            speaker: speaker
          });
        } else { // meeting
          const creator = document.getElementById('meeting-creator').value || '管理员';
          params = new URLSearchParams({
            mode: mode,
            creator: creator
          });
        }

        console.log('请求参数:', params.toString());

        const response = await fetch(`/api/asr/websocket/url?${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const result = await response.json();
        console.log('WebSocket URL响应:', result);

        if (result.code === '0') {
          const wsUrl = result.data.url;
          console.log('连接WebSocket:', wsUrl);
          
          // 修复：先获取音频权限，再连接WebSocket
          await initAudioCapture();
          connectWebSocket(wsUrl, mode);
        } else {
          alert('获取连接失败: ' + result.msg);
        }

      } catch (error) {
        console.error('启动实时ASR失败:', error);
        alert('启动失败: ' + error.message);
      }
    }

    // 添加音频采集功能
    async function initAudioCapture() {
      try {
        console.log('正在获取麦克风权限...');
        
        // 获取麦克风权限
        mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16000, // 16kHz采样率
            channelCount: 1,   // 单声道
            sampleSize: 16     // 16位
          }
        });

        console.log('麦克风权限获取成功');

        // 创建音频上下文
        audioContext = new (window.AudioContext || window.webkitAudioContext)({
          sampleRate: 16000
        });
        
        const source = audioContext.createMediaStreamSource(mediaStream);

        // 创建音频处理器
        processor = audioContext.createScriptProcessor(4096, 1, 1); // 增大buffer size

        processor.onaudioprocess = function(e) {
          if (currentWebSocket && currentWebSocket.readyState === WebSocket.OPEN && isRealTimeActive) {
            // 获取PCM数据并发送
            const inputBuffer = e.inputBuffer.getChannelData(0);
            const pcmData = floatTo16BitPCM(inputBuffer);
            
            console.log('发送音频数据，大小:', pcmData.length);
            currentWebSocket.send(pcmData);
          }
        };

        source.connect(processor);
        processor.connect(audioContext.destination);
        
        console.log('音频采集初始化完成');
        
      } catch (error) {
        console.error('音频采集初始化失败:', error);
        alert('无法访问麦克风，请检查权限设置');
        throw error;
      }
    }

    // 添加音频格式转换函数
    function floatTo16BitPCM(input) {
      const buffer = new ArrayBuffer(input.length * 2);
      const view = new DataView(buffer);
      let pos = 0;
      
      for (let i = 0; i < input.length; i++) {
        let sample = Math.max(-1, Math.min(1, input[i]));
        sample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        view.setInt16(pos, sample, true); // little endian
        pos += 2;
      }
      
      return buffer;
    }

    // 连接WebSocket
    function connectWebSocket(url, mode) {
      console.log('建立WebSocket连接:', url);
      
      currentWebSocket = new WebSocket(url);
      
      currentWebSocket.onopen = function(event) {
        console.log('WebSocket连接已建立');
        isRealTimeActive = true;
        updateRealtimeUI(mode, true);
        
        // 恢复音频上下文（某些浏览器需要用户交互后才能启动）
        if (audioContext && audioContext.state === 'suspended') {
          audioContext.resume();
        }
      };

      currentWebSocket.onmessage = function(event) {
        try {
          console.log('收到WebSocket消息:', event.data);
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data, mode);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error, '原始数据:', event.data);
        }
      };

      currentWebSocket.onclose = function(event) {
        console.log('WebSocket连接已关闭, 代码:', event.code, '原因:', event.reason);
        isRealTimeActive = false;
        updateRealtimeUI(mode, false);
        
        // 清理音频资源
        stopAudioCapture();
      };

      currentWebSocket.onerror = function(error) {
        console.error('WebSocket错误:', error);
        alert('WebSocket连接错误');
      };
    }

    // 处理WebSocket消息
    function handleWebSocketMessage(data, mode) {
      console.log('处理WebSocket消息:', data);
      
      const transcriptDiv = document.getElementById(mode + '-transcript');
      
      if (data.code === '0') {
        switch (data.data.type) {
          case 'NOTIFY':
            currentTaskId = data.data.taskId;
            console.log('任务创建成功, ID:', currentTaskId);
            transcriptDiv.innerHTML += `<div class="transcript-item"><div class="transcript-meta">系统</div><div>任务创建成功: ${currentTaskId}</div></div>`;
            break;
            
          case 'TEXT':
            console.log('收到转写文本:', data.data);
            const existingItem = transcriptDiv.querySelector(`[data-text-seq="${data.data.textSeq}"]`);
            if (existingItem) {
              // 更新现有句子
              existingItem.querySelector('.transcript-text').textContent = data.data.text;
              existingItem.querySelector('.transcript-status').textContent = data.data.status;
            } else {
              // 添加新句子
              const transcriptItem = document.createElement('div');
              transcriptItem.className = 'transcript-item';
              transcriptItem.setAttribute('data-text-seq', data.data.textSeq);
              transcriptItem.innerHTML = `
                <div class="transcript-meta">
                  ${data.data.speaker} - 序号: ${data.data.textSeq} - 状态: <span class="transcript-status">${data.data.status}</span>
                </div>
                <div class="transcript-text">${data.data.text}</div>
              `;
              transcriptDiv.appendChild(transcriptItem);
            }
            break;
            
          case 'CLOSE':
            console.log('收到任务关闭通知');
            transcriptDiv.innerHTML += `<div class="transcript-item"><div class="transcript-meta">系统</div><div>转写任务已结束</div></div>`;
            stopRealtimeASR(mode);
            break;
        }
        
        // 滚动到底部
        transcriptDiv.scrollTop = transcriptDiv.scrollHeight;
      } else {
        console.error('WebSocket消息错误:', data);
        transcriptDiv.innerHTML += `<div class="transcript-item">
          <div class="transcript-meta">系统错误</div>
          <div>错误: ${data.msg}</div>
        </div>`;
      }
    }

    // 停止实时ASR
    async function stopRealtimeASR(mode) {
      try {
        console.log('停止实时转写, 模式:', mode);
        
        // 先停止音频采集
        stopAudioCapture();
        
        // 调用后端结束任务API
        if (currentTaskId) {
          const token = localStorage.getItem('user_token');
          if (token) {
            console.log('调用结束任务API, taskId:', currentTaskId);
            const response = await fetch('/api/asr/end/task', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({ taskId: currentTaskId })
            });
            
            const result = await response.json();
            console.log('结束任务API响应:', result);
          }
        }

        // 关闭WebSocket连接
        if (currentWebSocket) {
          currentWebSocket.close();
          currentWebSocket = null;
        }

        isRealTimeActive = false;
        currentTaskId = null;
        updateRealtimeUI(mode, false);

      } catch (error) {
        console.error('停止实时ASR失败:', error);
      }
    }

    // 停止音频采集
    function stopAudioCapture() {
      console.log('停止音频采集');
      
      if (processor) {
        processor.disconnect();
        processor = null;
      }
      
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
        mediaStream = null;
      }
    }

    // 更新实时转写UI
    function updateRealtimeUI(mode, isActive) {
      const startBtn = document.getElementById(mode + '-start-btn');
      const stopBtn = document.getElementById(mode + '-stop-btn');
      const status = document.getElementById(mode + '-status');

      if (isActive) {
        startBtn.disabled = true;
        stopBtn.disabled = false;
        status.innerHTML = '<span class="recording-indicator"><i class="fas fa-circle"></i> 正在转写中...</span>';
      } else {
        startBtn.disabled = false;
        stopBtn.disabled = true;
        status.innerHTML = '';
      }
    }

    // 刷新任务列表
    async function refreshTaskList() {
      try {
        const token = localStorage.getItem('user_token');
        if (!token) {
          alert('请先登录');
          return;
        }

        const response = await fetch('/api/asr/tasks', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const result = await response.json();
        const taskList = document.getElementById('task-list');

        if (result.code === '0' && result.data.list.length > 0) {
          taskList.innerHTML = result.data.list.map(taskId => `
            <div class="task-item">
              <div class="task-info">
                <strong>任务ID:</strong> ${taskId}
              </div>
              <div class="task-actions">
                <button class="action-button info-button" onclick="getTaskInfo('${taskId}')">
                  <i class="fas fa-info"></i> 详情
                </button>
                <button class="action-button end-task-button" onclick="endTask('${taskId}')">
                  <i class="fas fa-stop"></i> 结束
                </button>
              </div>
            </div>
          `).join('');
        } else {
          taskList.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">暂无活跃任务</div>';
        }

      } catch (error) {
        console.error('刷新任务列表失败:', error);
        alert('刷新失败: ' + error.message);
      }
    }

    // 获取任务详情
    async function getTaskInfo(taskId) {
      try {
        const token = localStorage.getItem('user_token');
        const response = await fetch(`/api/asr/task/${taskId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const result = await response.json();
        if (result.code === '0') {
          alert(`任务详情:\n${JSON.stringify(result.data, null, 2)}`);
        } else {
          alert('获取任务详情失败: ' + result.msg);
        }
      } catch (error) {
        console.error('获取任务详情失败:', error);
        alert('获取失败: ' + error.message);
      }
    }

    // 结束任务
    async function endTask(taskId) {
      if (!confirm(`确定要结束任务 ${taskId} 吗？`)) {
        return;
      }

      try {
        const token = localStorage.getItem('user_token');
        const response = await fetch('/api/asr/end/task', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ taskId })
        });

        const result = await response.json();
        if (result.code === '0') {
          alert('任务已结束');
          refreshTaskList();
        } else {
          alert('结束任务失败: ' + result.msg);
        }
      } catch (error) {
        console.error('结束任务失败:', error);
        alert('结束失败: ' + error.message);
      }
    }
  </script>
</body>
</html> 