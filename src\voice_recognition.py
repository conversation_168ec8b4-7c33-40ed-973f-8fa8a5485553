import os
import logging
import tempfile
import uuid
from typing import Optional, Dict, Any
import configparser
import traceback
import torch
import json

logger = logging.getLogger("speech_recognition")

class SpeechRecognizer:
    def __init__(self, config: configparser.ConfigParser):
        self.config = config
        self.model = None
        self.engine = config.get("SPEECH_RECOGNITION", "ENGINE", fallback="whisper").lower()
        self.device = config.get("SPEECH_RECOGNITION", "DEVICE", fallback="cpu").lower()
        self.is_enabled = config.getboolean("SPEECH_RECOGNITION", "ENABLE_SPEECH_RECOGNITION", fallback=False)

        if self.is_enabled:
            self.load_model()

    def load_model(self):
        """加载语音识别模型"""
        try:
            if self.engine == "fast-whisper":
                self._load_fast_whisper_model()
            else:
                self._load_whisper_model()
        except Exception as e:
            logger.error(f"语音识别模型加载失败: {str(e)}")
            self.is_enabled = False
            raise

    def _load_whisper_model(self):
        """加载原版Whisper模型"""
        try:
            import whisper
            model_size = self.config.get("SPEECH_RECOGNITION", "WHISPER_MODEL_SIZE", fallback="base")
            model_path = self.config.get("SPEECH_RECOGNITION", "WHISPER_MODEL_PATH", fallback="").strip()

            if model_path and os.path.exists(model_path):
                self.model = whisper.load_model(model_path, device=self.device)
                logger.info(f"从本地路径加载Whisper模型: {model_path}, 设备: {self.device}")
            else:
                self.model = whisper.load_model(model_size, device=self.device)
                logger.info(f"加载Whisper模型: {model_size}, 设备: {self.device}")

        except ImportError:
            logger.error("Whisper库未安装，请运行: pip install openai-whisper")
            self.is_enabled = False
            raise ImportError("Whisper库未安装")

    def _load_fast_whisper_model(self):
        """加载faster-whisper模型"""
        try:
            from faster_whisper import WhisperModel
            
            model_size = self.config.get("SPEECH_RECOGNITION", "WHISPER_MODEL_SIZE", fallback="base")
            model_path = self.config.get("SPEECH_RECOGNITION", "FAST_WHISPER_MODEL_PATH", fallback="").strip()
            compute_type = self.config.get("SPEECH_RECOGNITION", "COMPUTE_TYPE", fallback="int8")
            
            # 验证设备
            if self.device == "cuda" and not torch.cuda.is_available():
                logger.warning("CUDA不可用，回退到CPU")
                self.device = "cpu"

            if model_path and os.path.exists(model_path):
                self.model = WhisperModel(
                    model_path, 
                    device=self.device, 
                    compute_type=compute_type
                )
                logger.info(f"从本地路径加载faster-whisper模型: {model_path}, 设备: {self.device}, 精度: {compute_type}")
            else:
                self.model = WhisperModel(
                    model_size, 
                    device=self.device, 
                    compute_type=compute_type
                )
                logger.info(f"加载faster-whisper模型: {model_size}, 设备: {self.device}, 精度: {compute_type}")

        except ImportError:
            logger.error("faster-whisper库未安装，请运行: pip install faster-whisper")
            self.is_enabled = False
            raise ImportError("faster-whisper库未安装")

    def recognize_audio(self, audio_file_path: str, language: Optional[str] = None) -> Dict[str, Any]:
        """识别音频文件中的语音"""
        if not self.is_enabled or not self.model:
            return {
                "status": "error",
                "message": "语音识别功能未启用或模型未加载"
            }

        try:
            logger.info(f"开始语音识别 ({self.engine}): {audio_file_path}")

            # 检查音频文件是否存在
            if not os.path.exists(audio_file_path):
                logger.error(f"音频文件不存在: {audio_file_path}")
                return {
                    "status": "error",
                    "message": f"音频文件不存在: {audio_file_path}"
                }

            # 检查文件大小
            file_size = os.path.getsize(audio_file_path)
            logger.info(f"音频文件大小: {file_size} 字节")

            if file_size == 0:
                logger.error("音频文件为空")
                return {
                    "status": "error",
                    "message": "音频文件为空"
                }

            # 如果没有指定语言，使用配置文件中的默认语言
            if not language:
                language = self.config.get("SPEECH_RECOGNITION", "LANGUAGE", fallback="zh")

            logger.info(f"识别语言设置: {language}")

            # 预处理音频
            logger.info("开始音频预处理...")
            processed_audio_path = self._preprocess_audio(audio_file_path)
            logger.info(f"预处理完成，使用文件: {processed_audio_path}")

            # 根据引擎类型进行识别
            if self.engine == "fast-whisper":
                result = self._recognize_with_fast_whisper(processed_audio_path, language)
            else:
                result = self._recognize_with_whisper(processed_audio_path, language)

            # 清理临时文件
            if processed_audio_path != audio_file_path:
                try:
                    os.remove(processed_audio_path)
                    logger.info(f"已删除临时转换文件: {processed_audio_path}")
                except Exception as e:
                    logger.warning(f"删除临时转换文件失败: {str(e)}")

            return result

        except Exception as e:
            error_trace = traceback.format_exc()
            logger.error(f"语音识别失败: {str(e)}")
            logger.error(f"详细错误信息:\n{error_trace}")
            return {
                "status": "error",
                "message": f"语音识别失败: {str(e)}"
            }

    def _recognize_with_whisper(self, audio_file_path: str, language: str) -> Dict[str, Any]:
        """使用原版Whisper进行识别"""
        # 设置识别参数
        transcribe_options = {
            "task": "transcribe",
            "fp16": False,
            "verbose": False
        }

        # 如果语言不是auto，则指定语言
        if language != "auto":
            transcribe_options["language"] = language

        logger.info(f"Whisper参数: {transcribe_options}")

        # 使用Whisper进行识别
        logger.info("开始调用Whisper模型进行识别...")
        result = self.model.transcribe(audio_file_path, **transcribe_options, initial_prompt="以下是普通话简体中文的句子。")
        logger.info("Whisper识别完成")

        recognized_text = result["text"].strip()
        logger.info(f"识别结果: {recognized_text[:100]}..." if len(recognized_text) > 100 else f"识别结果: {recognized_text}")

        return {
            "status": "success",
            "text": recognized_text,
            "language": result.get("language", language),
            "segments": result.get("segments", []),
            "confidence": self._calculate_confidence(result.get("segments", []))
        }

    def _recognize_with_fast_whisper(self, audio_file_path: str, language: str) -> Dict[str, Any]:
        """使用faster-whisper进行识别"""
        # 获取配置参数
        beam_size = self.config.getint("SPEECH_RECOGNITION", "BEAM_SIZE", fallback=5)
        vad_filter = self.config.getboolean("SPEECH_RECOGNITION", "VAD_FILTER", fallback=True)
        
        # 解析VAD参数
        vad_parameters = {}
        try:
            vad_params_str = self.config.get("SPEECH_RECOGNITION", "VAD_PARAMETERS", fallback="{}")
            vad_parameters = json.loads(vad_params_str)
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"VAD参数解析失败，使用默认值: {str(e)}")
            vad_parameters = {
                "threshold": 0.5,
                "min_speech_duration_ms": 250,
                "max_speech_duration_s": 30,
                "min_silence_duration_ms": 100,
                "speech_pad_ms": 30
            }

        # 设置识别参数
        transcribe_options = {
            "beam_size": beam_size,
            "vad_filter": vad_filter,
            "vad_parameters": vad_parameters if vad_filter else None,
            "initial_prompt": "以下是普通话简体中文的句子。"
        }

        # 如果语言不是auto，则指定语言
        if language != "auto":
            transcribe_options["language"] = language

        logger.info(f"faster-whisper参数: {transcribe_options}")

        # 使用faster-whisper进行识别
        logger.info("开始调用faster-whisper模型进行识别...")
        segments, info = self.model.transcribe(audio_file_path, **transcribe_options)
        logger.info("faster-whisper识别完成")

        # 处理结果
        segments_list = []
        recognized_text = ""
        
        for segment in segments:
            segment_dict = {
                "id": segment.id,
                "start": segment.start,
                "end": segment.end,
                "text": segment.text,
                "avg_logprob": segment.avg_logprob
            }
            segments_list.append(segment_dict)
            recognized_text += segment.text

        recognized_text = recognized_text.strip()
        logger.info(f"识别结果: {recognized_text[:100]}..." if len(recognized_text) > 100 else f"识别结果: {recognized_text}")

        return {
            "status": "success",
            "text": recognized_text,
            "language": info.language,
            "segments": segments_list,
            "confidence": self._calculate_confidence(segments_list)
        }

    def _preprocess_audio(self, audio_file_path: str) -> str:
        """预处理音频文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(audio_file_path):
                raise FileNotFoundError(f"音频文件不存在: {audio_file_path}")

            # 检查文件格式
            file_extension = os.path.splitext(audio_file_path)[1].lower()

            # 对于webm格式，尝试多种方法处理以避免FFmpeg依赖
            if file_extension == '.webm':
                logger.info("检测到webm格式，尝试多种方法处理...")

                # 方法1：尝试使用ffmpeg-python处理webm
                try:
                    import ffmpeg
                    import tempfile
                    import uuid
                    logger.info("尝试使用ffmpeg-python处理webm...")

                    # 创建临时wav文件
                    temp_wav_path = os.path.join(
                        tempfile.gettempdir(),
                        f"temp_audio_{uuid.uuid4()}.wav"
                    )

                    # 使用ffmpeg转换
                    (
                        ffmpeg
                        .input(audio_file_path)
                        .output(temp_wav_path, acodec='pcm_s16le', ac=1, ar=16000)
                        .overwrite_output()
                        .run(quiet=True)
                    )

                    logger.info(f"ffmpeg-python处理webm成功: {temp_wav_path}")
                    return temp_wav_path

                except ImportError:
                    logger.warning("ffmpeg-python未安装")
                except Exception as e:
                    logger.warning(f"ffmpeg-python处理webm失败: {str(e)}")

                # 方法2：尝试使用pydub处理webm
                try:
                    from pydub import AudioSegment
                    logger.info("尝试使用pydub处理webm...")

                    # 读取webm文件
                    audio = AudioSegment.from_file(audio_file_path, format="webm")

                    # 转换为16kHz单声道
                    audio = audio.set_frame_rate(16000).set_channels(1)

                    # 创建临时wav文件
                    import tempfile
                    import uuid
                    temp_wav_path = os.path.join(
                        tempfile.gettempdir(),
                        f"temp_audio_{uuid.uuid4()}.wav"
                    )

                    # 导出为wav
                    audio.export(temp_wav_path, format="wav")
                    logger.info(f"pydub处理webm成功: {temp_wav_path}")

                    return temp_wav_path

                except Exception as e:
                    logger.warning(f"pydub处理webm失败: {str(e)}")

                # 方法3：尝试使用librosa处理
                try:
                    import librosa
                    import soundfile as sf
                    logger.info("尝试使用librosa处理webm...")

                    # 使用librosa加载音频
                    audio_data, sample_rate = librosa.load(audio_file_path, sr=16000, mono=True)

                    # 创建临时wav文件
                    import tempfile
                    import uuid
                    temp_wav_path = os.path.join(
                        tempfile.gettempdir(),
                        f"temp_audio_{uuid.uuid4()}.wav"
                    )

                    # 保存为wav
                    sf.write(temp_wav_path, audio_data, sample_rate)
                    logger.info(f"librosa处理webm成功: {temp_wav_path}")

                    return temp_wav_path

                except Exception as e:
                    logger.warning(f"librosa处理webm失败: {str(e)}")

                # 方法4：尝试使用subprocess调用系统ffmpeg
                try:
                    import subprocess
                    import tempfile
                    import uuid
                    logger.info("尝试使用系统ffmpeg处理webm...")

                    # 创建临时wav文件
                    temp_wav_path = os.path.join(
                        tempfile.gettempdir(),
                        f"temp_audio_{uuid.uuid4()}.wav"
                    )

                    # 尝试调用系统ffmpeg
                    cmd = [
                        'ffmpeg', '-i', audio_file_path,
                        '-acodec', 'pcm_s16le',
                        '-ac', '1',
                        '-ar', '16000',
                        '-y',  # 覆盖输出文件
                        temp_wav_path
                    ]

                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                    if result.returncode == 0 and os.path.exists(temp_wav_path):
                        logger.info(f"系统ffmpeg处理webm成功: {temp_wav_path}")
                        return temp_wav_path
                    else:
                        logger.warning(f"系统ffmpeg处理失败: {result.stderr}")

                except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.CalledProcessError) as e:
                    logger.warning(f"系统ffmpeg不可用: {str(e)}")
                except Exception as e:
                    logger.warning(f"系统ffmpeg处理webm失败: {str(e)}")

                # 方法5：最后的尝试 - 直接返回原文件让模型处理
                logger.warning("所有webm处理方法都失败，直接使用原文件")
                logger.info("提示：如需完美支持webm格式，请安装FFmpeg到系统PATH")
                return audio_file_path

            # 支持的其他格式：wav, mp3, m4a, flac, ogg等
            supported_formats = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']

            if file_extension in supported_formats:
                logger.info(f"音频格式 {file_extension} 直接支持，无需转换")
                return audio_file_path

            # 只有在不支持的格式时才尝试转换
            logger.info(f"音频格式 {file_extension} 需要转换")

            # 尝试转换格式时，动态导入pydub
            try:
                from pydub import AudioSegment
                logger.info("尝试使用pydub进行音频格式转换...")
            except ImportError:
                logger.warning("pydub未安装，跳过音频格式转换，直接使用原始文件")
                return audio_file_path

            # 转换为wav格式
            try:
                audio = AudioSegment.from_file(audio_file_path)

                # 设置为16kHz采样率，单声道
                audio = audio.set_frame_rate(16000).set_channels(1)

                # 创建临时wav文件
                temp_wav_path = os.path.join(
                    tempfile.gettempdir(),
                    f"temp_audio_{uuid.uuid4()}.wav"
                )

                audio.export(temp_wav_path, format="wav")
                logger.info(f"音频转换完成: {file_extension} -> wav")

                return temp_wav_path

            except Exception as e:
                logger.warning(f"音频格式转换失败，直接使用原始文件: {str(e)}")
                return audio_file_path

        except Exception as e:
            logger.error(f"音频预处理失败: {str(e)}")
            return audio_file_path

    def _calculate_confidence(self, segments: list) -> float:
        """计算识别置信度"""
        if not segments:
            return 0.0

        try:
            # 基于段的平均概率计算置信度
            total_prob = 0.0
            total_length = 0.0

            for segment in segments:
                if 'avg_logprob' in segment and 'end' in segment and 'start' in segment:
                    duration = segment['end'] - segment['start']
                    total_prob += segment['avg_logprob'] * duration
                    total_length += duration

            if total_length > 0:
                avg_logprob = total_prob / total_length
                # 将log概率转换为0-1的置信度
                confidence = max(0.0, min(1.0, (avg_logprob + 1.0)))
                return round(confidence, 3)
        except:
            pass

        return 0.5  # 默认置信度

    def get_supported_formats(self) -> list:
        """获取支持的音频格式列表"""
        formats_str = self.config.get("SPEECH_RECOGNITION", "SUPPORTED_AUDIO_FORMATS", fallback="wav,mp3,m4a,flac,ogg,webm")
        return [fmt.strip() for fmt in formats_str.split(',')]

    def get_max_file_size_mb(self) -> int:
        """获取最大文件大小限制(MB)"""
        return self.config.getint("SPEECH_RECOGNITION", "MAX_AUDIO_SIZE_MB", fallback=10)