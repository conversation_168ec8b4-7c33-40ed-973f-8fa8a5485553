/* 全局样式 */
:root {
  --primary-color: #1890ff;
  --hikvision-red: #e60012;
  --hikvision-gray: #666;
  --border-color: #e0e0e0;
  --background-color: #f5f5f5;
  --text-color: #333;
  --light-text-color: #666;
  --hover-color: #f0f0f0;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: var(--text-color);
  background-color: var(--background-color);
  line-height: 1.6;
}

/* 布局 */
.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* 侧边栏 */
.sidebar {
  width: 250px;
  background-color: #fff;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}


.sidebar-header {
  padding: 16px;
  /*border-bottom: 0.5px solid var(--border-color);*/
  border-bottom: none;
  display: flex;
  align-items: center;
  position: relative; /* 添加相对定位 */
}

/* 添加伪元素来创建下移的边框 */
.sidebar-header::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -3.5px; /* 将边框下移3.5px，可以根据需要调整这个值 */
  height: 1px;
  background-color: var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.logo-title {
  color: var(--text-color);
  font-size: 18px;
  font-weight: bold;
}

.logo-hik {
  color: var(--hikvision-red);
  font-style: italic;
}

.logo-vision {
  color: var(--hikvision-gray);
  font-style: italic;
}

.logo-space {
  width: 10px;
}

.new-chat-btn {
  margin: 16px;
  padding: 8px 16px;
  background-color: #4e6ef2;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-chat-btn:hover {
  background-color: #3a5ae8;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  margin-top: 16px;
}

.history-title {
  font-size: 14px;
  color: var(--light-text-color);
}

.toggle-history-btn {
  background: none;
  border: none;
  color: var(--light-text-color);
  cursor: pointer;
  padding: 4px;
  transition: transform 0.3s;
}

.toggle-history-btn.collapsed {
  transform: rotate(-90deg);
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  transition: max-height 0.3s ease-in-out;
}

.history-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s;
  font-size: 14px;
}

.history-item:hover {
  background-color: var(--hover-color);
}

.history-item.active {
  background-color: var(--hover-color);
}

/* 主内容区 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text {
  font-size: 14px;
}

.upload-btn, .event-btn, .image-btn, .scene-btn, .jiuan-btn {
  padding: 6px 12px;
  background-color: #4e6ef2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-left: 8px;
}

.upload-btn:hover, .event-btn:hover, .image-btn:hover, .scene-btn:hover, .jiuan-btn:hover {
  background-color: #3a5ae8;
}

.chat-history {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: #fff;
  position: relative;
}

.message {
  position: relative;
  z-index: 1;
  margin-bottom: 24px;
  display: flex;
  align-items: flex-start;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background-color: #fff;
  border: 2px solid var(--primary-color);
  overflow: hidden;
}

.message-avatar img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.user-avatar {
  background-color: #e6f7ff;
  color: var(--primary-color);
}

.assistant-avatar {
  background-color: #f5f5f5;
  color: var(--light-text-color);
}

.message-content {
  flex: 1;
  padding: 12px 16px;
  border-radius: 4px;
  background-color: #f5f5f5;
  position: relative;
}

.user-message .message-content {
  background-color: #e6f7ff;
}

.assistant-message .message-content {
  background-color: #f5f5f5;
}

.message-content::before {
  content: '';
  position: absolute;
  top: 12px;
  left: -6px;
  width: 12px;
  height: 12px;
  background-color: inherit;
  transform: rotate(45deg);
}

.input-container {
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.rag-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-label {
  font-size: 14px;
  color: var(--text-color);
  user-select: none;
}

/* 开关按钮样式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.input-row {
  display: flex;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  outline: none;
  resize: none;
  min-height: 24px;
  max-height: 120px;
}

.send-btn {
  margin-left: 12px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: #fff;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 推荐问题区域 */
.recommended-questions {
  width: 300px;
  background-color: #fff;
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
  padding: 16px;
}

.recommended-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.question-category {
  margin-bottom: 20px;
}

.category-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #555;
  display: flex;
  align-items: center;
  gap: 6px;
}

.category-title i {
  color: var(--primary-color);
}

.question-item {
  padding: 12px;
  margin-bottom: 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 13px;
}

.question-item:hover {
  background-color: var(--hover-color);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.knowledge-question {
  border-left: 3px solid #4285f4;
}

.agent-question {
  border-left: 3px solid #4285f4;
}

/* 上传对话框 */
.dialog-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  align-items: center;
  justify-content: center;
}

.dialog-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
}

.dialog-header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.view-docs-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
}

.view-docs-btn:hover {
  background-color: #e0e0e0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input[type="text"] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

/* 自定义下拉框容器 */
.custom-select-container {
  position: relative;
  width: 100%;
}

.custom-select-container input[list] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.custom-select-container input[list]:focus {
  border-color: var(--primary-color);
  outline: none;
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.file-upload-area:hover {
  border-color: #999;
}

.file-upload-area.dragover {
  border-color: #666;
  background-color: #f9f9f9;
}

.upload-icon {
  margin-bottom: 10px;
}

.upload-text {
  color: #666;
  font-size: 14px;
}

.file-types {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-top: 10px;
}

.remove-file {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 16px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn {
  background: none;
  border: 1px solid #ddd;
  color: #666;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.submit-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

/* 思考过程和源文档样式 */
.thinking-process, .source-documents {
  margin: 8px 0;
}

.collapsible-header {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 0.8em;
  margin: 4px 0;
  cursor: pointer;
}

.toggle-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 1em;
  padding: 0;
  margin-left: 4px;
  transform: rotate(0deg);
  transition: transform 0.3s;
}

.toggle-button.expanded {
  transform: rotate(90deg);
}

.collapsible-content {
  display: none;
  padding: 8px;
  background: #f7f7f8;
  border-radius: 4px;
  margin: 4px 0 8px 0;
  font-size: 13px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .recommended-questions {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }
}

@media (max-width: 576px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .main-content {
    flex-direction: column;
  }
}

/* Markdown 样式 */
.message-content h1,
.message-content h2,
.message-content h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
}

.message-content ul,
.message-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.message-content li {
  margin: 4px 0;
}

.message-content p {
  margin: 0.1em 0;
}

.message-content pre {
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.message-content code {
  font-family: monospace;
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 2px;
}

/* 进度条样式 */
.progress-container {
  width: 100%;
  background-color: #f1f1f1;
  border-radius: 4px;
  margin: 10px 0;
}

.progress-bar {
  height: 10px;
  background-color: #4CAF50;
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s;
}

/* 上传状态样式 */
.upload-status {
  margin-top: 20px;
  text-align: center;
}

.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 复制按钮样式 */
.actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.action-button {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.action-button::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='9' y='9' width='13' height='13' rx='2' ry='2'%3E%3C/rect%3E%3Cpath d='M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1'%3E%3C/path%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 4px;
}

/* 添加header-buttons样式 */
.header-buttons {
  display: flex;
  gap: 10px;
}

/* 事件提问按钮样式 */
.event-btn, .list-btn {
  background-color: #4e6ef2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.event-btn:hover, .list-btn:hover {
  background-color: #3a5ae8;
}

/* 历史记录删除按钮样式 */
.delete-history-btn {
  background: none;
  border: none;
  color: #999;
  font-size: 14px;
  cursor: pointer;
  padding: 4px;
  margin-left: auto;
  opacity: 0;
  transition: opacity 0.3s, color 0.3s;
}

.history-item:hover .delete-history-btn {
  opacity: 1;
}

.delete-history-btn:hover {
  color: #f44336;
}

.history-item {
  position: relative;
  padding-right: 30px;
}

/* 分页控件样式 */
.history-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 16px;
  border-top: 1px solid var(--border-color);
  background-color: #fff;
}

.pagination-btn {
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  color: var(--light-text-color);
  transition: all 0.3s;
}

.pagination-btn:hover {
  background-color: var(--hover-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

#page-info {
  margin: 0 10px;
  font-size: 12px;
  color: var(--light-text-color);
}

/* 现代化用户头像和菜单样式 */
.user-profile {
  position: relative;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 10000; /* 确保用户菜单容器在最顶层 */
}

.user-profile:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  color: white;
  margin-right: 10px;
  position: relative;
  overflow: hidden;
}

.user-avatar.admin {
  background: linear-gradient(135deg, #ff4757, #ff3742);
}

.user-avatar.user {
  background: linear-gradient(135deg, #3742fa, #2f3542);
}

.avatar-text {
  text-transform: uppercase;
}

.user-info {
  display: flex;
  flex-direction: column;
  margin-right: 8px;
}

.username {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.2;
  margin-bottom: 2px;
}

.user-role {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
  line-height: 1;
}

.user-role.admin {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.user-role.user {
  background: rgba(55, 66, 250, 0.1);
  color: #3742fa;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--light-text-color);
  transition: transform 0.3s ease;
}

.user-profile.active .dropdown-icon {
  transform: rotate(180deg);
}

/* 下拉菜单 */
.user-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  min-width: 240px;
  z-index: 9999; /* 提高z-index确保在最顶层 */
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.user-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-header {
  padding: 16px;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px 12px 0 0;
}

.dropdown-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  color: white;
  margin-right: 12px;
}

.dropdown-avatar.admin {
  background: linear-gradient(135deg, #ff4757, #ff3742);
}

.dropdown-avatar.user {
  background: linear-gradient(135deg, #3742fa, #2f3542);
}

.dropdown-avatar-text {
  text-transform: uppercase;
}

.dropdown-user-info {
  flex: 1;
}

.dropdown-username {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.dropdown-role {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 500;
  display: inline-block;
}

.dropdown-role.admin {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.dropdown-role.user {
  background: rgba(55, 66, 250, 0.1);
  color: #3742fa;
}

.dropdown-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
  margin: 0 16px;
}

.dropdown-menu {
  padding: 8px 0;
}

.dropdown-item {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dropdown-item:hover {
  background: rgba(0, 0, 0, 0.04);
}

.dropdown-item i {
  width: 16px;
  text-align: center;
}

.admin-item {
  color: var(--primary-color) !important;
}

.admin-item:hover {
  background: rgba(24, 144, 255, 0.08) !important;
}

.logout-item {
  color: #ff4d4f !important;
}

.logout-item:hover {
  background: rgba(255, 77, 79, 0.08) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-profile {
    padding: 6px 8px;
    margin-left: 8px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 12px;
    margin-right: 8px;
  }

  .username {
    font-size: 13px;
  }

  .user-role {
    font-size: 10px;
  }

  .user-dropdown {
    min-width: 200px;
    right: -20px;
  }
}