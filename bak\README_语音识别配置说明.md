# 语音识别配置说明

## 概述

本系统支持两种语音识别引擎：
- **whisper**: OpenAI 原版 Whisper 模型
- **fast-whisper**: 基于 CTranslate2 优化的 faster-whisper，速度更快，支持更多特性

## 安装依赖

### 使用 Whisper (原版)
```bash
pip install openai-whisper
```

### 使用 faster-whisper (推荐)
```bash
pip install faster-whisper
```

## 配置文件说明 (config.ini)

### 基础配置

```ini
[SPEECH_RECOGNITION]
# 语音识别引擎选择: whisper 或 fast-whisper
ENGINE = fast-whisper

# 计算设备选择: cpu 或 cuda
DEVICE = cpu

# 计算精度: int8, int16, float16, float32 (fast-whisper支持)
COMPUTE_TYPE = int8

# Whisper模型大小: tiny, base, small, medium, large
WHISPER_MODEL_SIZE = small

# 模型路径（可选，如果指定则从本地加载）
WHISPER_MODEL_PATH = Private_GPT\sentence-transformers\sound_model\small.pt

# fast-whisper模型路径（可选，如果指定则从本地加载）
FAST_WHISPER_MODEL_PATH = 

# 支持的音频格式
SUPPORTED_AUDIO_FORMATS = wav,mp3,m4a,flac,ogg,webm

# 音频文件大小限制(MB)
MAX_AUDIO_SIZE_MB = 10

# 音频处理超时时间(秒)
AUDIO_TIMEOUT = 30

# 语言设置 (zh=中文, en=英文, auto=自动检测)
LANGUAGE = zh

# 是否启用语音识别功能
ENABLE_SPEECH_RECOGNITION = true
```

### fast-whisper 专用配置

```ini
# beam搜索大小
BEAM_SIZE = 5

# VAD过滤开关（Voice Activity Detection）
VAD_FILTER = true

# VAD参数
VAD_PARAMETERS = {"threshold": 0.5, "min_speech_duration_ms": 250, "max_speech_duration_s": 30, "min_silence_duration_ms": 100, "speech_pad_ms": 30}
```

## 配置示例

### 使用 GPU 的 fast-whisper 配置（推荐）

```ini
ENGINE = fast-whisper
DEVICE = cuda
COMPUTE_TYPE = float16
WHISPER_MODEL_SIZE = medium
BEAM_SIZE = 5
VAD_FILTER = true
```

### 使用 CPU 的原版 Whisper 配置

```ini
ENGINE = whisper
DEVICE = cpu
WHISPER_MODEL_SIZE = base
```

### 使用本地模型文件

```ini
ENGINE = fast-whisper
DEVICE = cpu
COMPUTE_TYPE = int8
FAST_WHISPER_MODEL_PATH = /path/to/your/model
```

## 性能对比

| 特性 | Whisper | faster-whisper |
|-----|---------|----------------|
| 速度 | 较慢 | 快 2-4 倍 |
| 内存占用 | 较高 | 较低 |
| GPU 支持 | 支持 | 支持 |
| 量化支持 | 有限 | 多种精度选择 |
| VAD 过滤 | 需要额外实现 | 内置支持 |
| Beam Search | 基础支持 | 优化实现 |

## 模型大小说明

| 模型 | 参数 | 英文准确率 | 多语言准确率 | 相对速度 | 内存需求 |
|-----|------|----------|------------|----------|---------|
| tiny | 39M | ~32% | ~32% | ~32x | ~1 GB |
| base | 74M | ~34% | ~34% | ~16x | ~1 GB |
| small | 244M | ~36% | ~36% | ~6x | ~2 GB |
| medium | 769M | ~40% | ~40% | ~2x | ~5 GB |
| large | 1550M | ~45% | ~44% | 1x | ~10 GB |

## 计算精度说明 (faster-whisper)

| 精度 | 内存占用 | 速度 | 准确率 | 推荐使用场景 |
|-----|---------|------|--------|-------------|
| int8 | 最低 | 最快 | 略低 | CPU 推理、内存受限 |
| int16 | 低 | 快 | 中等 | CPU 推理、平衡性能 |
| float16 | 中等 | 中等 | 高 | GPU 推理推荐 |
| float32 | 最高 | 较慢 | 最高 | 高精度要求 |

## VAD (Voice Activity Detection) 参数说明

```json
{
  "threshold": 0.5,              // 语音检测阈值 (0.0-1.0)
  "min_speech_duration_ms": 250, // 最小语音片段长度 (毫秒)
  "max_speech_duration_s": 30,   // 最大语音片段长度 (秒)
  "min_silence_duration_ms": 100, // 最小静音长度 (毫秒)
  "speech_pad_ms": 30            // 语音片段前后填充 (毫秒)
}
```

## 故障排除

### 1. CUDA 相关错误
如果遇到 CUDA 相关错误，请：
- 确认 CUDA 已正确安装
- 检查 PyTorch CUDA 支持：`python -c "import torch; print(torch.cuda.is_available())"`
- 如果 CUDA 不可用，系统会自动回退到 CPU

### 2. 内存不足
如果遇到内存不足，请：
- 使用更小的模型 (tiny, base, small)
- 降低计算精度 (int8, int16)
- 使用 CPU 而不是 GPU

### 3. 模型加载失败
如果模型加载失败，请：
- 检查模型路径是否正确
- 确认相应的依赖包已安装
- 查看日志文件获取详细错误信息

### 4. 音频格式不支持
系统会尝试自动转换音频格式，如果失败请：
- 安装 FFmpeg 到系统 PATH
- 安装额外依赖：`pip install pydub librosa soundfile`

## 推荐配置

### 生产环境（高性能）
```ini
ENGINE = fast-whisper
DEVICE = cuda
COMPUTE_TYPE = float16
WHISPER_MODEL_SIZE = medium
BEAM_SIZE = 5
VAD_FILTER = true
```

### 开发环境（平衡性能）
```ini
ENGINE = fast-whisper
DEVICE = cpu
COMPUTE_TYPE = int8
WHISPER_MODEL_SIZE = small
BEAM_SIZE = 3
VAD_FILTER = true
```

### 资源受限环境
```ini
ENGINE = fast-whisper
DEVICE = cpu
COMPUTE_TYPE = int8
WHISPER_MODEL_SIZE = tiny
BEAM_SIZE = 1
VAD_FILTER = false
``` 