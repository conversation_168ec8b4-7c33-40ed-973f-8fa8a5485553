<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 应急指挥智能体</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .register-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo img {
            width: 60px;
            height: 60px;
            margin-bottom: 10px;
        }

        .logo h1 {
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .register-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .register-btn:hover {
            transform: translateY(-2px);
        }

        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #3c3;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .info-message {
            background: #e7f3ff;
            color: #0066cc;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .password-requirements {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="logo">
            <img src="/static/images/yingji.png" alt="应急指挥产品" onerror="this.style.display='none'">
            <h1>用户注册</h1>
        </div>

        <div class="info-message">
            注册后需要管理员审核通过才能使用系统
        </div>

        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <form id="register-form">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required minlength="3" maxlength="20">
                <div class="password-requirements">3-20个字符，只能包含字母、数字和下划线</div>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required minlength="6">
                <div class="password-requirements">至少6个字符</div>
            </div>
            <div class="form-group">
                <label for="confirm-password">确认密码</label>
                <input type="password" id="confirm-password" name="confirm-password" required>
            </div>
            <button type="submit" class="register-btn" id="register-btn">
                <span id="register-text">注册</span>
                <span id="register-loading" class="loading" style="display: none;"></span>
            </button>
        </form>

        <div class="login-link">
            已有账号？<a href="/login">立即登录</a>
        </div>
    </div>

    <script>
        document.getElementById('register-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const registerBtn = document.getElementById('register-btn');
            const registerText = document.getElementById('register-text');
            const registerLoading = document.getElementById('register-loading');
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');

            // 隐藏之前的消息
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            // 验证用户名格式
            if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
                errorMessage.textContent = '用户名格式不正确，只能包含字母、数字和下划线，长度3-20个字符';
                errorMessage.style.display = 'block';
                return;
            }

            // 验证密码
            if (password.length < 6) {
                errorMessage.textContent = '密码长度至少6个字符';
                errorMessage.style.display = 'block';
                return;
            }

            // 验证密码确认
            if (password !== confirmPassword) {
                errorMessage.textContent = '两次输入的密码不一致';
                errorMessage.style.display = 'block';
                return;
            }

            // 显示加载状态
            registerBtn.disabled = true;
            registerText.style.display = 'none';
            registerLoading.style.display = 'inline-block';

            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (data.success) {
                    successMessage.textContent = '注册成功！请等待管理员审核，审核通过后即可登录使用。';
                    successMessage.style.display = 'block';
                    
                    // 清空表单
                    document.getElementById('register-form').reset();
                    
                    // 3秒后跳转到登录页面
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 3000);
                } else {
                    errorMessage.textContent = data.message || '注册失败';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = '网络错误，请稍后重试';
                errorMessage.style.display = 'block';
            } finally {
                // 恢复按钮状态
                registerBtn.disabled = false;
                registerText.style.display = 'inline';
                registerLoading.style.display = 'none';
            }
        });

        // 检查是否已经登录
        if (localStorage.getItem('user_token')) {
            window.location.href = '/app';
        }
    </script>
</body>
</html>
