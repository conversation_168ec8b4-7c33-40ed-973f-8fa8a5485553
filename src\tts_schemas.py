"""
TTS数据模型定义
"""
from typing import List, Optional
from pydantic import BaseModel, Field, validator


class TTSRequest(BaseModel):
    """TTS请求模型"""
    text: str = Field(..., min_length=1, max_length=1000, description="要转换的文本")
    voice: str = Field(default="zf_001", description="语音名称")
    speed: float = Field(default=1.0, ge=0.5, le=2.0, description="语速倍率")
    format: str = Field(default="wav", description="音频格式")
    api_key: Optional[str] = Field(None, description="API密钥（可选）")
    
    @validator('text')
    def validate_text(cls, v):
        """验证文本内容"""
        if not v.strip():
            raise ValueError("文本内容不能为空")
        return v.strip()
    
    @validator('format')
    def validate_format(cls, v):
        """验证音频格式"""
        if v.lower() not in ['wav', 'mp3']:
            raise ValueError("音频格式只支持 wav 或 mp3")
        return v.lower()


class Voice(BaseModel):
    """语音模型"""
    name: str = Field(..., description="语音名称")
    display_name: str = Field(..., description="显示名称")
    language: str = Field(..., description="语言代码")
    gender: str = Field(..., description="性别")
    description: Optional[str] = Field(None, description="描述")


class VoicesResponse(BaseModel):
    """语音列表响应"""
    voices: List[Voice] = Field(..., description="可用语音列表")


class StatusResponse(BaseModel):
    """状态响应"""
    status: str = Field(..., description="服务状态")
    model_loaded: bool = Field(..., description="模型是否已加载")
    version: str = Field(..., description="版本号")
    available_voices: int = Field(..., description="可用语音数量")


class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    detail: Optional[str] = Field(None, description="详细信息")


class TTSResponse(BaseModel):
    """TTS响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    audio_url: Optional[str] = Field(None, description="音频文件URL")
    duration: Optional[float] = Field(None, description="音频时长(秒)")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
