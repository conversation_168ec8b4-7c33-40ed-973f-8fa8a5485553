import psycopg2
import psycopg2.extras
import uuid
import os
import json
import configparser
from datetime import datetime
import logging

# 设置日志
logger = logging.getLogger("db_manager")

# 从配置文件加载数据库连接参数
def load_db_config():
    try:
        config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.ini")
        config.read(config_path, encoding='utf-8')

        # 从配置文件读取PostgreSQL连接信息
        db_config = {
            "host": config.get("POSTGRES", "HOST", fallback="***********"),
            "port": config.getint("POSTGRES", "PORT", fallback=7543),
            "database": config.get("POSTGRES", "DATABASE", fallback="postgres"),
            "user": config.get("POSTGRES", "USER", fallback="postgres"),
            "password": config.get("POSTGRES", "PASSWORD", fallback="hik12345+")
        }

        logger.info(f"从配置文件加载数据库连接信息成功: {db_config['host']}:{db_config['port']}")
        return db_config
    except Exception as e:
        logger.error(f"从配置文件加载数据库连接信息失败: {str(e)}")
        # 返回默认配置
        return {
            "host": "***********",
            "port": 7543,
            "database": "postgres",
            "user": "postgres",
            "password": "hik12345+"
        }

# 加载数据库配置
DB_CONFIG = load_db_config()

class DBManager:
    def __init__(self, db_config=DB_CONFIG):
        """初始化数据库管理器"""
        self.db_config = db_config
        self.init_db()

    def get_connection(self):
        """获取数据库连接"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise

    def init_db(self):
        """初始化数据库，创建必要的表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 先删除可能存在的外键约束
            try:
                # 删除feedback表的外键约束
                cursor.execute("""
                DO $$
                BEGIN
                    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'feedback') THEN
                        ALTER TABLE feedback DROP CONSTRAINT IF EXISTS feedback_message_id_fkey;
                    END IF;
                END
                $$;
                """)

                # 删除messages表的外键约束
                cursor.execute("""
                DO $$
                BEGIN
                    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
                        ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_session_id_fkey;
                    END IF;
                END
                $$;
                """)

                conn.commit()
                logger.info("外键约束已删除")
            except Exception as e:
                logger.warning(f"删除外键约束时出错，可能不存在: {str(e)}")
                conn.rollback()

            # 创建会话表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                session_id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                user_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()

            # 添加user_id列（如果不存在）
            try:
                cursor.execute("ALTER TABLE sessions ADD COLUMN user_id TEXT")
                conn.commit()
                logger.info("已为sessions表添加user_id列")
            except Exception as e:
                # 列可能已存在，忽略错误
                conn.rollback()

            # 创建消息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                message_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                user_message TEXT NOT NULL,
                assistant_response TEXT NOT NULL,
                thinking_process TEXT,
                source_documents TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()

            # 创建反馈表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS feedback (
                feedback_id TEXT PRIMARY KEY,
                message_id TEXT NOT NULL,
                rating INTEGER NOT NULL,
                comment TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()

            # 创建永久存储反馈表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS permanent_feedback (
                id TEXT PRIMARY KEY,
                original_message_id TEXT,
                original_session_id TEXT,
                user_message TEXT NOT NULL,
                assistant_response TEXT NOT NULL,
                thinking_process TEXT,
                source_documents TEXT,
                rating INTEGER NOT NULL,
                comment TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
            ''')
            conn.commit()

            # 创建用户表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                approved_by TEXT,
                approved_at TIMESTAMP
            )
            ''')
            conn.commit()

            # 创建用户审核表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_approvals (
                approval_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                action TEXT NOT NULL,
                admin_id TEXT NOT NULL,
                reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()

            # 添加外键约束
            try:
                # 添加messages表的外键约束
                cursor.execute('''
                ALTER TABLE messages
                ADD CONSTRAINT messages_session_id_fkey
                FOREIGN KEY (session_id) REFERENCES sessions (session_id) ON DELETE CASCADE
                ''')

                # 添加feedback表的外键约束
                cursor.execute('''
                ALTER TABLE feedback
                ADD CONSTRAINT feedback_message_id_fkey
                FOREIGN KEY (message_id) REFERENCES messages (message_id) ON DELETE CASCADE
                ''')

                conn.commit()
                logger.info("外键约束添加成功")
            except Exception as e:
                logger.warning(f"添加外键约束时出错，可能已存在: {str(e)}")
                conn.rollback()

            # 创建API工具配置表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_tools (
                id SERIAL PRIMARY KEY,
                tool_name_cn VARCHAR(255) NOT NULL,
                tool_name_en VARCHAR(255) NOT NULL UNIQUE,
                service_path VARCHAR(500) NOT NULL,
                post_path VARCHAR(500) NOT NULL,
                keywords TEXT NOT NULL,
                description TEXT,
                request_params JSONB,
                response_params JSONB,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # 创建API工具表的索引
            cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_api_tools_name_en ON api_tools(tool_name_en)
            ''')
            cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_api_tools_active ON api_tools(is_active)
            ''')

            conn.commit()
            conn.close()
            logger.info("数据库初始化成功")

            # 初始化默认管理员用户
            self.init_admin_user()
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise

    # API工具配置相关方法
    def save_api_tool(self, tool_data):
        """保存API工具配置"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
            INSERT INTO api_tools (
                tool_name_cn, tool_name_en, service_path, post_path,
                keywords, description, request_params, response_params, is_active
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id
            ''', (
                tool_data['tool_name_cn'],
                tool_data['tool_name_en'],
                tool_data['service_path'],
                tool_data['post_path'],
                tool_data['keywords'],
                tool_data.get('description', ''),
                json.dumps(tool_data.get('request_params', {})),
                json.dumps(tool_data.get('response_params', {})),
                tool_data.get('is_active', True)
            ))

            tool_id = cursor.fetchone()[0]
            conn.commit()
            conn.close()

            logger.info(f"API工具配置保存成功，ID: {tool_id}")
            return tool_id
        except Exception as e:
            logger.error(f"保存API工具配置失败: {str(e)}")
            return None

    def update_api_tool(self, tool_id, tool_data):
        """更新API工具配置"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
            UPDATE api_tools SET
                tool_name_cn = %s,
                tool_name_en = %s,
                service_path = %s,
                post_path = %s,
                keywords = %s,
                description = %s,
                request_params = %s,
                response_params = %s,
                is_active = %s,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
            ''', (
                tool_data['tool_name_cn'],
                tool_data['tool_name_en'],
                tool_data['service_path'],
                tool_data['post_path'],
                tool_data['keywords'],
                tool_data.get('description', ''),
                json.dumps(tool_data.get('request_params', {})),
                json.dumps(tool_data.get('response_params', {})),
                tool_data.get('is_active', True),
                tool_id
            ))

            conn.commit()
            conn.close()

            logger.info(f"API工具配置更新成功，ID: {tool_id}")
            return True
        except Exception as e:
            logger.error(f"更新API工具配置失败: {str(e)}")
            return False

    def get_api_tool(self, tool_id):
        """获取单个API工具配置"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            cursor.execute('SELECT * FROM api_tools WHERE id = %s', (tool_id,))
            tool = cursor.fetchone()

            conn.close()

            if tool:
                # 解析JSON字段
                if isinstance(tool['request_params'], str):
                    tool['request_params'] = json.loads(tool['request_params']) if tool['request_params'] else {}
                if isinstance(tool['response_params'], str):
                    tool['response_params'] = json.loads(tool['response_params']) if tool['response_params'] else {}

            return tool
        except Exception as e:
            logger.error(f"获取API工具配置失败: {str(e)}")
            return None

    def get_all_api_tools(self, active_only=False):
        """获取所有API工具配置"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            if active_only:
                cursor.execute('SELECT * FROM api_tools WHERE is_active = TRUE ORDER BY created_at DESC')
            else:
                cursor.execute('SELECT * FROM api_tools ORDER BY created_at DESC')

            tools = cursor.fetchall()
            conn.close()

            # 解析JSON字段
            for tool in tools:
                if isinstance(tool['request_params'], str):
                    tool['request_params'] = json.loads(tool['request_params']) if tool['request_params'] else {}
                if isinstance(tool['response_params'], str):
                    tool['response_params'] = json.loads(tool['response_params']) if tool['response_params'] else {}

            return tools
        except Exception as e:
            logger.error(f"获取API工具配置列表失败: {str(e)}")
            return []

    def delete_api_tool(self, tool_id):
        """删除API工具配置"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('DELETE FROM api_tools WHERE id = %s', (tool_id,))

            conn.commit()
            conn.close()

            logger.info(f"API工具配置删除成功，ID: {tool_id}")
            return True
        except Exception as e:
            logger.error(f"删除API工具配置失败: {str(e)}")
            return False

    def export_api_tools(self):
        """导出所有API工具配置"""
        try:
            tools = self.get_all_api_tools()
            export_data = []

            for tool in tools:
                export_data.append({
                    'tool_name_cn': tool['tool_name_cn'],
                    'tool_name_en': tool['tool_name_en'],
                    'service_path': tool['service_path'],
                    'post_path': tool['post_path'],
                    'keywords': tool['keywords'],
                    'description': tool['description'],
                    'request_params': tool['request_params'],
                    'response_params': tool['response_params'],
                    'is_active': tool['is_active']
                })

            return export_data
        except Exception as e:
            logger.error(f"导出API工具配置失败: {str(e)}")
            return []

    def import_api_tools(self, tools_data):
        """导入API工具配置"""
        try:
            success_count = 0
            error_count = 0

            for tool_data in tools_data:
                try:
                    tool_id = self.save_api_tool(tool_data)
                    if tool_id:
                        success_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    logger.error(f"导入工具配置失败: {str(e)}")
                    error_count += 1

            logger.info(f"API工具配置导入完成，成功: {success_count}, 失败: {error_count}")
            return success_count, error_count
        except Exception as e:
            logger.error(f"导入API工具配置失败: {str(e)}")
            return 0, len(tools_data)

    # 会话相关方法
    def save_session(self, session_id, title, user_id=None):
        """保存或更新会话"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查会话是否已存在
            cursor.execute("SELECT * FROM sessions WHERE session_id = %s", (session_id,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有会话
                cursor.execute(
                    "UPDATE sessions SET title = %s, updated_at = %s WHERE session_id = %s",
                    (title, datetime.now(), session_id)
                )
            else:
                # 创建新会话
                cursor.execute(
                    "INSERT INTO sessions (session_id, title, user_id, created_at, updated_at) VALUES (%s, %s, %s, %s, %s)",
                    (session_id, title, user_id, datetime.now(), datetime.now())
                )

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"保存会话失败: {str(e)}")
            return False

    def get_sessions(self, user_id=None, is_admin=False):
        """获取会话列表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            if is_admin:
                # 管理员可以看到所有会话
                cursor.execute("SELECT * FROM sessions ORDER BY updated_at DESC")
            elif user_id:
                # 普通用户只能看到自己的会话
                cursor.execute("SELECT * FROM sessions WHERE user_id = %s ORDER BY updated_at DESC", (user_id,))
            else:
                # 没有用户ID，返回空列表
                conn.close()
                return []

            sessions = [dict(row) for row in cursor.fetchall()]

            # 将datetime对象转换为字符串
            for session in sessions:
                if 'created_at' in session and session['created_at']:
                    session['created_at'] = session['created_at'].isoformat()
                if 'updated_at' in session and session['updated_at']:
                    session['updated_at'] = session['updated_at'].isoformat()

            conn.close()
            return sessions
        except Exception as e:
            logger.error(f"获取会话失败: {str(e)}")
            return []

    def delete_session(self, session_id):
        """删除会话及其所有消息"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 标记永久存储表中的相关记录为已删除
            cursor.execute("""
                UPDATE permanent_feedback
                SET is_deleted = TRUE
                WHERE original_session_id = %s
            """, (session_id,))

            # 删除会话的所有反馈
            cursor.execute(
                "DELETE FROM feedback WHERE message_id IN (SELECT message_id FROM messages WHERE session_id = %s)",
                (session_id,)
            )

            # 删除会话的所有消息
            cursor.execute("DELETE FROM messages WHERE session_id = %s", (session_id,))

            # 删除会话
            cursor.execute("DELETE FROM sessions WHERE session_id = %s", (session_id,))

            conn.commit()
            conn.close()
            logger.info(f"会话 {session_id} 已删除，相关反馈已在永久存储表中标记为已删除")
            return True
        except Exception as e:
            logger.error(f"删除会话失败: {str(e)}")
            conn.rollback() if 'conn' in locals() else None
            if 'conn' in locals() and conn:
                conn.close()
            return False

    # 消息相关方法
    def save_message(self, message_id, session_id, user_message, assistant_response, thinking_process=None, source_documents=None, user_id=None):
        """保存消息"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 确保会话存在
            cursor.execute("SELECT * FROM sessions WHERE session_id = %s", (session_id,))
            session = cursor.fetchone()

            if not session:
                # 如果会话不存在，创建一个新会话
                title = user_message[:30] + "..." if len(user_message) > 30 else user_message
                cursor.execute(
                    "INSERT INTO sessions (session_id, title, user_id, created_at, updated_at) VALUES (%s, %s, %s, %s, %s)",
                    (session_id, title, user_id, datetime.now(), datetime.now())
                )

            # 保存消息
            cursor.execute(
                "INSERT INTO messages (message_id, session_id, user_message, assistant_response, thinking_process, source_documents) VALUES (%s, %s, %s, %s, %s, %s)",
                (message_id, session_id, user_message, assistant_response, thinking_process, source_documents)
            )

            # 更新会话的更新时间
            cursor.execute(
                "UPDATE sessions SET updated_at = %s WHERE session_id = %s",
                (datetime.now(), session_id)
            )

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"保存消息失败: {str(e)}")
            return False

    def get_session_messages(self, session_id):
        """获取会话的所有消息"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            cursor.execute("SELECT * FROM messages WHERE session_id = %s ORDER BY created_at", (session_id,))
            messages = [dict(row) for row in cursor.fetchall()]

            # 将datetime对象转换为字符串
            for message in messages:
                if 'created_at' in message and message['created_at']:
                    message['created_at'] = message['created_at'].isoformat()

            conn.close()
            return messages
        except Exception as e:
            logger.error(f"获取会话消息失败: {str(e)}")
            return []

    # 反馈相关方法
    def save_feedback(self, feedback_id, message_id, rating, comment=None):
        """保存反馈"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # 检查消息是否存在
            cursor.execute("SELECT * FROM messages WHERE message_id = %s", (message_id,))
            message = cursor.fetchone()

            if not message:
                logger.error(f"消息不存在: {message_id}")
                conn.close()
                return False

            # 检查是否已有反馈
            cursor.execute("SELECT * FROM feedback WHERE message_id = %s", (message_id,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有反馈
                cursor.execute(
                    "UPDATE feedback SET rating = %s, comment = %s WHERE message_id = %s",
                    (rating, comment, message_id)
                )
            else:
                # 创建新反馈
                cursor.execute(
                    "INSERT INTO feedback (feedback_id, message_id, rating, comment) VALUES (%s, %s, %s, %s)",
                    (feedback_id, message_id, rating, comment)
                )

            # 获取消息所属的会话信息
            cursor.execute("SELECT session_id FROM messages WHERE message_id = %s", (message_id,))
            session_result = cursor.fetchone()
            session_id = session_result['session_id'] if session_result else None

            if session_id:
                # 同时保存到永久存储表
                permanent_id = f"perm_{uuid.uuid4()}"

                # 将消息转换为字典格式以便于访问字段
                message_dict = dict(message)

                cursor.execute("""
                    INSERT INTO permanent_feedback (
                        id, original_message_id, original_session_id,
                        user_message, assistant_response, thinking_process, source_documents,
                        rating, comment, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    permanent_id, message_id, session_id,
                    message_dict['user_message'], message_dict['assistant_response'],
                    message_dict['thinking_process'], message_dict['source_documents'],
                    rating, comment, datetime.now()
                ))

                logger.info(f"消息 {message_id} 的反馈已永久保存，ID: {permanent_id}")

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"保存反馈失败: {str(e)}")
            conn.rollback() if 'conn' in locals() else None
            if 'conn' in locals() and conn:
                conn.close()
            return False

    def get_message_feedback(self, message_id):
        """获取消息的反馈"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            cursor.execute("SELECT * FROM feedback WHERE message_id = %s", (message_id,))
            feedback = [dict(row) for row in cursor.fetchall()]

            # 将datetime对象转换为字符串
            for fb in feedback:
                if 'created_at' in fb and fb['created_at']:
                    fb['created_at'] = fb['created_at'].isoformat()

            conn.close()
            return feedback
        except Exception as e:
            logger.error(f"获取消息反馈失败: {str(e)}")
            return []

    def get_permanent_feedback(self, limit=100, offset=0, rating=None, include_deleted=False):
        """获取永久存储的反馈数据"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # 构建查询
            query = "SELECT * FROM permanent_feedback"
            query_params = []
            where_clauses = []

            # 根据参数添加过滤条件
            if rating is not None:
                where_clauses.append("rating = %s")
                query_params.append(int(rating))

            if not include_deleted:
                where_clauses.append("is_deleted = FALSE")

            # 添加WHERE子句
            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)

            # 添加排序和分页
            query += " ORDER BY created_at DESC LIMIT %s OFFSET %s"
            query_params.extend([limit, offset])

            # 执行查询
            cursor.execute(query, query_params)
            feedback_data = [dict(row) for row in cursor.fetchall()]

            # 处理datetime对象
            for item in feedback_data:
                if 'created_at' in item and item['created_at']:
                    item['created_at'] = item['created_at'].isoformat()

            conn.close()
            return feedback_data
        except Exception as e:
            logger.error(f"获取永久反馈数据失败: {str(e)}")
            if 'conn' in locals() and conn:
                conn.close()
            return []

    def get_permanent_feedback_count(self, rating=None, include_deleted=False):
        """获取永久存储的反馈数据总数"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 构建查询
            query = "SELECT COUNT(*) FROM permanent_feedback"
            query_params = []
            where_clauses = []

            # 根据参数添加过滤条件
            if rating is not None:
                where_clauses.append("rating = %s")
                query_params.append(int(rating))

            if not include_deleted:
                where_clauses.append("is_deleted = FALSE")

            # 添加WHERE子句
            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)

            # 执行查询
            cursor.execute(query, query_params)
            count = cursor.fetchone()[0]

            conn.close()
            return count
        except Exception as e:
            logger.error(f"获取永久反馈数据总数失败: {str(e)}")
            if 'conn' in locals() and conn:
                conn.close()
            return 0

    def mark_permanent_feedback_deleted(self, feedback_id):
        """标记永久存储的反馈数据为已删除"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查反馈是否存在
            cursor.execute("SELECT * FROM permanent_feedback WHERE id = %s", (feedback_id,))
            feedback = cursor.fetchone()

            if not feedback:
                logger.warning(f"反馈不存在: {feedback_id}")
                conn.close()
                return False

            # 标记为已删除
            cursor.execute(
                "UPDATE permanent_feedback SET is_deleted = TRUE WHERE id = %s",
                (feedback_id,)
            )

            conn.commit()
            conn.close()
            logger.info(f"反馈 {feedback_id} 已标记为已删除")
            return True
        except Exception as e:
            logger.error(f"标记反馈为已删除失败: {str(e)}")
            conn.rollback() if 'conn' in locals() else None
            if 'conn' in locals() and conn:
                conn.close()
            return False

    # 用户管理相关方法
    def create_user(self, user_id, username, password_hash, role='user'):
        """创建新用户"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查用户名是否已存在
            cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            existing = cursor.fetchone()

            if existing:
                logger.warning(f"用户名已存在: {username}")
                conn.close()
                return False

            # 创建新用户
            cursor.execute(
                "INSERT INTO users (user_id, username, password_hash, role, status, created_at, updated_at) VALUES (%s, %s, %s, %s, %s, %s, %s)",
                (user_id, username, password_hash, role, 'pending', datetime.now(), datetime.now())
            )

            conn.commit()
            conn.close()
            logger.info(f"用户创建成功: {username}")
            return True
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            return False

    def get_user_by_username(self, username):
        """根据用户名获取用户信息"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            user = cursor.fetchone()

            if user:
                user_dict = dict(user)
                # 将datetime对象转换为字符串
                if 'created_at' in user_dict and user_dict['created_at']:
                    user_dict['created_at'] = user_dict['created_at'].isoformat()
                if 'updated_at' in user_dict and user_dict['updated_at']:
                    user_dict['updated_at'] = user_dict['updated_at'].isoformat()
                if 'approved_at' in user_dict and user_dict['approved_at']:
                    user_dict['approved_at'] = user_dict['approved_at'].isoformat()

            conn.close()
            return user_dict if user else None
        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return None

    def get_pending_users(self):
        """获取待审核的用户列表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            cursor.execute("SELECT * FROM users WHERE status = 'pending' ORDER BY created_at DESC")
            users = [dict(row) for row in cursor.fetchall()]

            # 将datetime对象转换为字符串
            for user in users:
                if 'created_at' in user and user['created_at']:
                    user['created_at'] = user['created_at'].isoformat()
                if 'updated_at' in user and user['updated_at']:
                    user['updated_at'] = user['updated_at'].isoformat()
                if 'approved_at' in user and user['approved_at']:
                    user['approved_at'] = user['approved_at'].isoformat()

            conn.close()
            return users
        except Exception as e:
            logger.error(f"获取待审核用户失败: {str(e)}")
            return []

    def approve_user(self, user_id, admin_id, reason=None):
        """审核通过用户"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 更新用户状态
            cursor.execute(
                "UPDATE users SET status = %s, approved_by = %s, approved_at = %s, updated_at = %s WHERE user_id = %s",
                ('approved', admin_id, datetime.now(), datetime.now(), user_id)
            )

            # 记录审核操作
            approval_id = f"app_{uuid.uuid4()}"
            cursor.execute(
                "INSERT INTO user_approvals (approval_id, user_id, action, admin_id, reason) VALUES (%s, %s, %s, %s, %s)",
                (approval_id, user_id, 'approve', admin_id, reason)
            )

            conn.commit()
            conn.close()
            logger.info(f"用户审核通过: {user_id}")
            return True
        except Exception as e:
            logger.error(f"审核用户失败: {str(e)}")
            return False

    def reject_user(self, user_id, admin_id, reason=None):
        """拒绝用户"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 更新用户状态
            cursor.execute(
                "UPDATE users SET status = %s, updated_at = %s WHERE user_id = %s",
                ('rejected', datetime.now(), user_id)
            )

            # 记录审核操作
            approval_id = f"app_{uuid.uuid4()}"
            cursor.execute(
                "INSERT INTO user_approvals (approval_id, user_id, action, admin_id, reason) VALUES (%s, %s, %s, %s, %s)",
                (approval_id, user_id, 'reject', admin_id, reason)
            )

            conn.commit()
            conn.close()
            logger.info(f"用户审核拒绝: {user_id}")
            return True
        except Exception as e:
            logger.error(f"拒绝用户失败: {str(e)}")
            return False

    def get_all_users(self):
        """获取所有用户列表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            cursor.execute("SELECT * FROM users ORDER BY created_at DESC")
            users = [dict(row) for row in cursor.fetchall()]

            # 将datetime对象转换为字符串
            for user in users:
                if 'created_at' in user and user['created_at']:
                    user['created_at'] = user['created_at'].isoformat()
                if 'updated_at' in user and user['updated_at']:
                    user['updated_at'] = user['updated_at'].isoformat()
                if 'approved_at' in user and user['approved_at']:
                    user['approved_at'] = user['approved_at'].isoformat()

            conn.close()
            return users
        except Exception as e:
            logger.error(f"获取所有用户失败: {str(e)}")
            return []

    def init_admin_user(self):
        """初始化默认管理员用户"""
        try:
            # 检查是否已存在admin用户
            admin_user = self.get_user_by_username('admin')
            if admin_user:
                logger.info("管理员用户已存在")
                return True

            # 创建默认admin用户
            import hashlib
            admin_id = f"admin_{uuid.uuid4()}"
            # 默认密码为'Yjt@2024++，实际使用时应该修改
            password_hash = hashlib.sha256('Yjt@2024++'.encode()).hexdigest()

            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute(
                "INSERT INTO users (user_id, username, password_hash, role, status, created_at, updated_at, approved_by, approved_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)",
                (admin_id, 'admin', password_hash, 'admin', 'approved', datetime.now(), datetime.now(), 'system', datetime.now())
            )

            conn.commit()
            conn.close()
            logger.info("默认管理员用户创建成功，用户名: admin, 密码: 'Yjt@2024++")
            return True
        except Exception as e:
            logger.error(f"初始化管理员用户失败: {str(e)}")
            return False
