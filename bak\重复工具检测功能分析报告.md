# 重复（相似）API工具检测功能分析报告

## 1. 背景与目标

### 1.1. 问题背景
当前系统允许管理员通过 `static/api_tools_config.html` 页面动态添加和配置API工具。这些工具的名称、关键词和描述是大型语言模型（LLM）理解并决定何时调用该工具的关键依据。如果系统中存在多个名称、功能描述或关键词高度相似的工具（例如，一个叫`查询天气`，另一个叫`获取天气信息`），LLM在面对用户请求时可能会产生混淆，导致无法做出正确的决策，最终造成工具调用失败或调用错误的工具。

### 1.2. 项目目标
为了提升工具调用的准确性和稳定性，计划在`api_tools_config.html`页面新增一个**重复/相似工具检测机制**。该机制旨在用户添加或修改API工具时，自动检测并提醒用户潜在的冲突，从源头上避免引入语义模糊的工具。

## 2. "相似性"的定义与检测方案

“相似”是一个相对主观的概念，我们可以从两个层面来定义和检测它：

1.  **精确匹配（Exact Match）**: 新工具的**工具名称(name)**或**关键词(keywords)**与现有工具完全相同。这是最基本、最容易实现的检测。

2.  **语义相似（Semantic Similarity）**: 新工具的**名称、描述和关键词组合**起来的**综合含义**与现有工具高度相似。这是问题的核心，也是技术上的难点。例如，“获取当前城市的天气预报”和“查询本地天气状况”在语义上就非常接近。

针对以上两种情况，我们推荐采用**前端辅助校验 + 后端核心校验**的混合方案。

## 3. 实施方案建议

### 3.1. 前端辅助校验（提升用户体验）

在用户填写表单时提供即时反馈，是提升用户体验的有效方式。

*   **实现位置**: `static/api_tools_config.js`
*   **触发时机**: 当用户填写完“工具名称”或“关键词”并移开鼠标焦点时（`onblur`事件）。
*   **检测逻辑**:
    1.  **精确匹配检测**:
        *   在页面加载时，前端JS已获取并渲染了所有现有工具的列表。
        *   当用户输入完成时，JS代码遍历这个列表，检查新输入的“工具名称”或“关键词”是否存在完全匹配项。
        *   如果存在，则在输入框旁边显示明确的错误提示（例如：“工具名称已存在”），并**禁用“保存”按钮**，直到用户修改为止。
*   **优点**: 响应速度快，用户体验好，能拦截最明显的重复。
*   **缺点**: 无法处理语义相似的情况。

### 3.2. 后端核心校验（保障数据质量）

后端是数据入库前的最后一道关卡，必须进行最严格的校验，尤其是语义相似性校验。

*   **实现位置**: `src/app/api_tools_endpoints.py` 中负责创建和更新API工具的API端点（例如 `POST /api/tools` 和 `PUT /api/tools/{tool_id}`）。
*   **检测逻辑**:
    1.  **接收请求**: 接收到前端传来
        的新增或修改工具的数据。
    2.  **精确匹配**: 首先执行与前端类似的精确匹配检查，如果匹配则直接返回`400 Bad Request`错误，提示字段冲突。
    3.  **语义相似度检测**:
        *   **文本整合**: 将新工具的“名称”、“描述”和“关键词”拼接成一个完整的描述性文本。
        *   **文本向量化**: 使用系统当前集成的**嵌入模型（Embedding Model）**（例如 `bge-large-zh-v1.5`）将这个描述性文本转换为一个高维向量（Embedding）。
        *   **相似度计算**:
            *   从数据库中查询出所有现有的API工具。
            *   将每个现有工具的“名称”、“描述”和“关键词”也整合成描述性文本，并计算其对应的向量。
            *   使用**余弦相似度（Cosine Similarity）**算法，计算新工具的向量与数据库中每一个现有工具向量的相似度得分。
        *   **阈值判断**:
            *   设定一个相似度阈值（例如 `0.95`）。这个值可以配置在 `config.ini` 文件中，方便未来调整。
            *   如果计算出的最高相似度得分**超过**这个阈值，则认为新工具与某个现有工具在语义上高度相似。
    4.  **返回结果**:
        *   如果检测到高度相似的工具，后端应返回一个明确的错误响应（例如 `409 Conflict`），并附带说明，如：`{"error": "工具与现有工具 'XX' 高度相似，请修改名称或描述以更好地区分。"}`。
        *   如果所有校验通过，则正常执行数据库的保存操作。

### 3.3. 数据库优化建议

为了提高后端语义相似度计算的效率，避免每次请求都对数据库中的所有工具进行实时向量化，可以考虑进行优化：

*   **新增向量列**: 在存储API工具的数据库表中，增加一个字段，例如 `semantic_embedding`，用于**持久化存储**每个工具的语义向量。
*   **向量生成**:
    *   当一个新工具被**首次创建**或**成功修改**时，计算其语义向量并存入 `semantic_embedding` 字段。
    *   需要编写一个一次性的脚本，为数据库中所有已存在的工具生成并填充它们的初始向量。
*   **查询优化**: 这样，在进行相似度检测时，只需计算新工具的向量，然后从数据库中直接读取所有现有工具的向量进行比较，大大减少了计算开销。

## 4. 总结

通过结合**前端的即时精确匹配校验**和**后端的严格语义相似度校验**，我们可以构建一个健壮的重复工具检测系统。

*   **前端**负责快速拦截明显错误，优化用户操作体验。
*   **后端**作为最终防线，利用文本向量化和余弦相似度算法，确保数据的一致性和独特性，从根本上解决LLM因工具定义模糊而导致的调用失败问题。

建议首先从后端的核心校验功能开始实施，因为它能提供最根本的保障。前端的辅助校验可以作为第二阶段的优化来完成。