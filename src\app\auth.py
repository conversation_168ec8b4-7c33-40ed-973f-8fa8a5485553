"""
用户认证相关功能模块
"""
import hashlib
import jwt
import uuid
from datetime import datetime, timedelta
from fastapi import HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from typing import Optional
import logging

logger = logging.getLogger(__name__)

# JWT配置
JWT_SECRET_KEY = "your-secret-key-change-in-production"  # 生产环境中应该使用环境变量
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 24

# 安全配置
security = HTTPBearer()

def create_access_token(data: dict) -> str:
    """创建JWT访问令牌"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """验证JWT令牌"""
    try:
        payload = jwt.decode(credentials.credentials, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="无效的认证令牌")
        return username
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="无效的认证令牌")

def get_current_user(username: str = Depends(verify_token)):
    """获取当前用户信息"""
    from ..db_manager import DatabaseManager
    
    # 这里需要传入数据库管理器实例
    # 在实际使用时，应该通过依赖注入获取
    db_manager = None  # 这个需要在调用时传入
    
    if db_manager is None:
        raise HTTPException(status_code=500, detail="数据库连接失败")
    
    user = db_manager.get_user_by_username(username)
    if user is None:
        raise HTTPException(status_code=401, detail="用户不存在")
    
    if user['status'] != 'approved':
        raise HTTPException(status_code=401, detail="用户未通过审核")
    
    return user

def get_current_admin_user(current_user: dict = Depends(get_current_user)) -> dict:
    """获取当前管理员用户"""
    if current_user['role'] != 'admin':
        raise HTTPException(status_code=403, detail="需要管理员权限")
    return current_user

def hash_password(password: str) -> str:
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed_password: str) -> bool:
    """验证密码"""
    return hash_password(password) == hashed_password

def generate_user_id() -> str:
    """生成用户ID"""
    return f"user_{uuid.uuid4()}"

def validate_username(username: str) -> bool:
    """验证用户名格式"""
    import re
    return bool(re.match(r'^[a-zA-Z0-9_]{3,20}$', username))

def validate_password(password: str) -> bool:
    """验证密码格式"""
    return len(password) >= 6

class AuthService:
    """认证服务类"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
    
    def register_user(self, username: str, password: str) -> dict:
        """用户注册"""
        try:
            # 验证输入
            if not username or not password:
                return {"success": False, "message": "用户名和密码不能为空"}
            
            if not validate_username(username):
                return {"success": False, "message": "用户名格式不正确，只能包含字母、数字和下划线，长度3-20个字符"}
            
            if not validate_password(password):
                return {"success": False, "message": "密码长度至少6个字符"}
            
            # 创建用户
            user_id = generate_user_id()
            password_hash = hash_password(password)
            
            success = self.db_manager.create_user(user_id, username, password_hash)
            
            if success:
                logger.info(f"新用户注册成功: {username}")
                return {"success": True, "message": "注册成功，请等待管理员审核"}
            else:
                return {"success": False, "message": "用户名已存在或注册失败"}
                
        except Exception as e:
            logger.error(f"用户注册失败: {str(e)}")
            return {"success": False, "message": "注册失败，请稍后重试"}
    
    def login_user(self, username: str, password: str) -> dict:
        """用户登录"""
        try:
            if not username or not password:
                return {"success": False, "message": "用户名和密码不能为空"}
            
            # 获取用户信息
            user = self.db_manager.get_user_by_username(username)
            
            if not user:
                return {"success": False, "message": "用户名或密码错误"}
            
            # 验证密码
            if not verify_password(password, user['password_hash']):
                return {"success": False, "message": "用户名或密码错误"}
            
            # 检查用户状态
            if user['status'] == 'pending':
                return {"success": False, "message": "账号待审核，请等待管理员审核通过"}
            elif user['status'] == 'rejected':
                return {"success": False, "message": "账号已被拒绝，请联系管理员"}
            elif user['status'] != 'approved':
                return {"success": False, "message": "账号状态异常，请联系管理员"}
            
            # 创建JWT令牌
            token_data = {"sub": username, "role": user['role']}
            access_token = create_access_token(token_data)
            
            # 返回用户信息（不包含密码）
            user_info = {
                "user_id": user['user_id'],
                "username": user['username'],
                "role": user['role'],
                "status": user['status']
            }
            
            logger.info(f"用户登录成功: {username}")
            return {
                "success": True,
                "message": "登录成功",
                "token": access_token,
                "user": user_info
            }
            
        except Exception as e:
            logger.error(f"用户登录失败: {str(e)}")
            return {"success": False, "message": "登录失败，请稍后重试"}
    
    def get_current_user_from_token(self, token: str) -> Optional[dict]:
        """从令牌获取当前用户"""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            username: str = payload.get("sub")
            if username is None:
                return None
            
            user = self.db_manager.get_user_by_username(username)
            if user is None or user['status'] != 'approved':
                return None
            
            return user
        except jwt.PyJWTError:
            return None
