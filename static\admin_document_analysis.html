<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档分析</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <link rel="stylesheet" href="/static/css/input-fix.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <style>
    .document-analysis-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
    }
    .document-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      border-bottom: 1px solid #eaeaea;
    }
    .back-btn {
      background-color: transparent;
      border: none;
      color: #666;
      cursor: pointer;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 5px;
    }
    .back-btn:hover {
      color: #333;
    }
    .directory-selector {
      display: flex;
      padding: 10px 20px;
      align-items: center;
      gap: 10px;
      border-bottom: 1px solid #eaeaea;
    }
    .directory-input {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    .apply-btn {
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 15px;
      cursor: pointer;
      font-weight: 500;
    }
    .apply-btn:hover {
      background-color: #45a049;
    }
    /* 确保左侧没有背景色 */
    .sidebar {
      background-color: #fff;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="/static/images/yingji.png" alt="应急指挥产品" class="logo-icon">
          <span class="logo-title">消防指挥智能体</span>
        </div>
      </div>
      <button class="new-chat-btn" id="new-chat-btn">
        <i class="fas fa-plus"></i>
        <span>新建会话</span>
      </button>
      <div class="history-title">历史记录</div>
      <div class="history-list" id="history-list">
        <!-- 历史记录将通过JavaScript动态加载 -->
      </div>
    </div>

    <!-- 文档分析主内容区 -->
    <div class="main-content">
      <div class="document-analysis-container">
        <div class="document-header">
          <button class="back-btn" id="back-btn">
            <i class="fas fa-arrow-left"></i>
            <span>返回主页</span>
          </button>
          <div class="welcome-text">请输入文档相关问题！</div>
        </div>

        <div class="directory-selector">
          <label for="directory-input">文档目录:</label>
          <input type="text" id="directory-input" class="directory-input" value="D:\LLM\RAG\RAG_demo6.5.7_askcase\Private_GPT\upload_files" />
          <button class="apply-btn" id="apply-btn">应用</button>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-container">
          <div class="chat-history" id="chat-history">
            <!-- 聊天记录将通过JavaScript动态加载 -->
          </div>
          <div class="input-container">
            <textarea class="message-input" id="message-input" placeholder="输入问题..." rows="1"></textarea>
            <button class="send-btn" id="send-btn">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化marked选项
      marked.setOptions({
        breaks: true,  // 支持GitHub风格的换行
        gfm: true      // 支持GitHub风格的Markdown
      });

      // 返回主页按钮
      document.getElementById('back-btn').addEventListener('click', function() {
        window.location.href = '/';
      });

      // 新建会话按钮
      document.getElementById('new-chat-btn').addEventListener('click', function() {
        clearChatHistory();
      });

      // 应用按钮
      document.getElementById('apply-btn').addEventListener('click', function() {
        const directoryInput = document.getElementById('directory-input');
        const directory = directoryInput.value.trim();

        // 添加系统消息
        const chatHistory = document.getElementById('chat-history');
        const systemMsg = document.createElement('div');
        systemMsg.className = 'message system-message';
        systemMsg.innerHTML = `
          <div class="message-content">
            已切换到目录: <code>${directory}</code>
          </div>
        `;
        chatHistory.appendChild(systemMsg);
        chatHistory.scrollTop = chatHistory.scrollHeight;
      });

      // 发送按钮和回车键发送
      document.getElementById('send-btn').addEventListener('click', sendMessage);
      document.getElementById('message-input').addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });

      // 清空聊天记录
      function clearChatHistory() {
        const chatHistory = document.getElementById('chat-history');
        chatHistory.innerHTML = '';
      }

      // 发送消息函数
      function sendMessage() {
        const messageInput = document.getElementById('message-input');
        const message = messageInput.value.trim();

        if (message) {
          // 清空输入框
          messageInput.value = '';

          // 添加用户消息到聊天历史
          const chatHistory = document.getElementById('chat-history');
          const userMsg = document.createElement('div');
          userMsg.className = 'message user-message';
          userMsg.innerHTML = `
            <div class="message-avatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="message-content">${message}</div>
          `;
          chatHistory.appendChild(userMsg);

          // 添加AI消息占位符
          const aiMsg = document.createElement('div');
          aiMsg.className = 'message ai-message';

          // 创建消息内容容器
          const contentContainer = document.createElement('div');
          contentContainer.className = 'message-content-container';

          // 创建思考过程区域
          const thinkingDiv = document.createElement('div');
          thinkingDiv.className = 'thinking-process collapsible';
          thinkingDiv.innerHTML = `
            <div class="collapsible-header">
              <span>思考过程</span>
              <button class="toggle-button">▶</button>
            </div>
            <div class="collapsible-content">
              <div class="thinking-content">思考中...</div>
            </div>
          `;

          // 创建正文内容区域
          const messageContent = document.createElement('div');
          messageContent.className = 'message-content';
          messageContent.innerHTML = `
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          `;

          // 添加头像和内容容器
          aiMsg.innerHTML = `
            <div class="message-avatar">
              <img src="/static/images/yingji.png" alt="AI">
            </div>
          `;

          // 按顺序添加元素：思考过程 -> 消息内容
          contentContainer.appendChild(thinkingDiv);
          contentContainer.appendChild(messageContent);

          // 将内容容器添加到AI消息中
          aiMsg.appendChild(contentContainer);

          // 将AI消息添加到聊天历史
          chatHistory.appendChild(aiMsg);
          chatHistory.scrollTop = chatHistory.scrollHeight;

          // 设置思考过程的可折叠功能
          thinkingDiv.querySelector('.collapsible-header').addEventListener('click', function() {
            const content = this.nextElementSibling;
            const toggleButton = this.querySelector('.toggle-button');

            if (content.style.display === 'block') {
              content.style.display = 'none';
              toggleButton.classList.remove('expanded');
            } else {
              content.style.display = 'block';
              toggleButton.classList.add('expanded');
            }
          });

          // 调用API获取回答
          const directoryPath = document.getElementById('directory-input').value.trim();
          analyzeDocuments(message, directoryPath, aiMsg);
        }
      }

      // 调用文档分析API
      async function analyzeDocuments(query, directoryPath, aiMsgElement) {
        // 记录开始时间，用于计算思考时间
        const startTime = new Date();

        try {
          const response = await fetch('/analyze_documents/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: query,
              directory_path: directoryPath
            })
          });

          if (!response.ok) {
            throw new Error('网络请求失败');
          }

          // 读取流式响应
          const reader = response.body.getReader();
          const decoder = new TextDecoder();
          let result = '';

          // 获取消息内容元素和思考内容元素
          const messageContent = aiMsgElement.querySelector('.message-content');
          const thinkingContent = aiMsgElement.querySelector('.thinking-content');
          const thinkingDiv = aiMsgElement.querySelector('.thinking-process');

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const text = decoder.decode(value);
            result += text;

            // 检查是否包含思考过程
            const thinkMatch = result.match(/<think>([\s\S]*?)<\/think>/);
            if (thinkMatch) {
              const thinking = thinkMatch[1].trim();
              // 更新思考过程内容
              thinkingContent.innerHTML = marked.parse(thinking);
              // 确保完全移除思考过程标记及其内容
              result = result.replace(/<think>[\s\S]*?<\/think>/, '').trim();
            }

            // 更新AI消息内容
            messageContent.innerHTML = marked.parse(result);

            // 滚动到底部
            const chatHistory = document.getElementById('chat-history');
            chatHistory.scrollTop = chatHistory.scrollHeight;
          }

          // 记录结束时间，计算思考时间
          const endTime = new Date();
          const thinkingTime = Math.round((endTime - startTime) / 1000);

          // 更新思考过程标题，显示思考时间
          const thinkingHeader = thinkingDiv.querySelector('.collapsible-header span:first-child');
          if (thinkingHeader) {
            thinkingHeader.textContent = `思考了 ${thinkingTime}s`;
          }

        } catch (error) {
          console.error('获取回答时出错:', error);
          aiMsgElement.querySelector('.message-content').textContent = '获取回答时出错，请稍后重试';
        }
      }
    });
  </script>
</body>
</html>