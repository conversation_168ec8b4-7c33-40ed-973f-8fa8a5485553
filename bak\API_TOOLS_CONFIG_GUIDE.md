# API工具配置管理系统使用指南

## 概述

本系统提供了一个可视化的API工具配置管理界面，允许用户自定义添加、编辑和管理API工具，而无需修改代码。

## 功能特性

### 1. 代码优化
- **优化了Function Calling执行逻辑**：将原来的大量if-else判断替换为映射表机制
- **参数转换自动化**：使用`_convert_and_call_function`方法和参数转换映射表
- **代码可维护性提升**：新增工具只需在映射表中添加转换规则

### 2. 可视化配置界面
- **现代化UI设计**：响应式布局，支持桌面和移动设备
- **直观的表单界面**：支持工具名称、路径、参数等配置
- **实时预览**：配置过程中可以实时查看效果

### 3. 完整的CRUD操作
- **添加工具**：支持完整的工具配置信息录入
- **编辑工具**：可修改现有工具的所有配置项
- **删除工具**：安全删除不需要的工具配置
- **查看工具**：列表展示所有工具及其状态

### 4. 导入导出功能
- **配置导出**：将所有工具配置导出为JSON文件
- **配置导入**：从JSON文件批量导入工具配置
- **数据备份**：支持配置的备份和恢复

## 访问方式

启动服务后，通过以下URL访问配置页面：
```
http://localhost:8087/tools-config
```

## 系统集成流程

用户配置的工具已完全集成到系统的工作流程中：

1. **用户查询** → `main.py` 的 `/run_workflow/` 端点
2. **流程分析** → `search_local_information_stream` 方法
3. **工具匹配** → `function_calling_agent` 方法检查用户配置的工具关键词
4. **代理选择** → 根据配置选择ReAct或Function Calling模式
5. **动态执行** → 整合内置工具和用户配置工具进行处理
6. **结果返回** → 格式化输出给用户

## 配置项说明

### 基本信息
- **工具名称（中文）**：工具的中文显示名称
- **工具名称（英文）**：工具的英文标识符，必须唯一
- **工具描述**：工具功能的详细描述

### 接口配置
- **服务段**：API服务的基础路径，如 `/emdispatch-web`
- **POST路径**：完整的API接口路径，如 `/emdispatch-web/third/v1/eventApi/app/`

### 关键词配置
- **关键词组合**：用于识别是否调用此API的关键词，用逗号分隔
- 示例：`查询,事件,周边,资源`

### 参数配置

#### 请求参数
定义发送给API的参数：
- **参数名**：参数的名称
- **类型**：参数类型（字符串、整数、浮点数、布尔值、对象、数组）
- **默认值**：参数的默认值
- **描述**：参数的说明

#### 返回参数
定义API返回的参数结构：
- **字段名**：返回数据中的字段名
- **类型**：字段的数据类型
- **描述**：字段的含义说明

## 使用流程

### 1. 添加新工具
1. 点击"添加工具"按钮
2. 填写工具的基本信息
3. 配置接口路径信息
4. 设置关键词组合
5. 定义请求和返回参数
6. 点击"保存工具"

### 2. 编辑现有工具
1. 在工具列表中找到要编辑的工具
2. 点击"编辑"按钮
3. 修改相应的配置项
4. 点击"保存工具"

### 3. 导出配置
1. 点击"导出配置"按钮
2. 系统会自动下载JSON格式的配置文件
3. 文件名格式：`api_tools_config_YYYY-MM-DD.json`

### 4. 导入配置
1. 点击"导入配置"按钮
2. 选择要导入的JSON配置文件
3. 点击"导入"按钮
4. 系统会显示导入结果

## 技术实现

### 核心组件

1. **DynamicToolsManager** (`src/agent/dynamic_tools_manager.py`)
   - 管理用户配置的工具
   - 提供关键词匹配功能
   - 执行动态API调用
   - 缓存工具配置

2. **DynamicCustomFunctionCallingAgent** (`src/agent/dynamic_agent_core.py`)
   - 整合内置工具和用户配置工具
   - 支持统一的函数调用格式
   - 自动参数转换和API调用

3. **集成的工作流程** (`src/agent/agent_service.py`)
   - 在`function_calling_agent`中添加动态工具检查
   - 根据关键词匹配自动选择合适的工具
   - 支持ReAct和Function Calling两种模式

### 数据库表结构
```sql
CREATE TABLE api_tools (
    id SERIAL PRIMARY KEY,
    tool_name_cn VARCHAR(255) NOT NULL,
    tool_name_en VARCHAR(255) NOT NULL UNIQUE,
    service_path VARCHAR(500) NOT NULL,
    post_path VARCHAR(500) NOT NULL,
    keywords TEXT NOT NULL,
    description TEXT,
    request_params JSONB,
    response_params JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### API端点
- `GET /api/tools` - 获取所有工具配置
- `GET /api/tools/{id}` - 获取单个工具配置
- `POST /api/tools` - 创建新工具配置
- `PUT /api/tools/{id}` - 更新工具配置
- `DELETE /api/tools/{id}` - 删除工具配置
- `GET /api/tools/export` - 导出工具配置
- `POST /api/tools/import` - 导入工具配置

### 优化后的函数调用机制
```python
# 参数转换映射表
param_converters = {
    "query_event_surround_resource": lambda args: self._build_param_string([
        args.get("event_name", ""),
        args.get("distance", 5),
        args.get("resource_type", "")
    ], skip_empty_tail=True),
    # ... 其他工具的转换规则
}
```

## 配置示例

### 工具配置JSON示例
```json
{
  "tool_name_cn": "查询事件周边资源",
  "tool_name_en": "query_event_surround_resource",
  "service_path": "/emdispatch-web",
  "post_path": "/emdispatch-web/third/v1/eventApi/app/",
  "keywords": "查询,事件,周边,资源",
  "description": "查询指定事件周边的应急资源",
  "request_params": {
    "event_name": {
      "type": "string",
      "default": "",
      "description": "事件名称"
    },
    "distance": {
      "type": "int", 
      "default": "5",
      "description": "查询半径（公里）"
    },
    "resource_type": {
      "type": "string",
      "default": "",
      "description": "资源类型（可选）"
    }
  },
  "response_params": {
    "code": {
      "type": "string",
      "description": "响应状态码"
    },
    "data": {
      "type": "array",
      "description": "资源列表数据"
    },
    "msg": {
      "type": "string", 
      "description": "响应消息"
    }
  },
  "is_active": true
}
```

## 注意事项

1. **工具英文名称必须唯一**：系统会检查重复性
2. **关键词设置要准确**：影响工具的自动识别和调用
3. **参数类型要正确**：确保与实际API接口匹配
4. **定期备份配置**：使用导出功能备份重要配置
5. **测试新工具**：添加新工具后建议进行功能测试

## 故障排除

1. **工具不被识别**：检查关键词配置是否正确
2. **参数解析错误**：检查参数类型和默认值设置
3. **API调用失败**：检查服务段和POST路径配置
4. **导入失败**：确保JSON文件格式正确

通过这个配置管理系统，您可以轻松地添加和管理API工具，无需修改代码，大大提高了系统的可维护性和扩展性。
