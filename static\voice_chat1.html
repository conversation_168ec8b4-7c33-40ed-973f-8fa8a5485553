<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>语音对话 - 应急指挥智能体</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <link rel="stylesheet" href="/static/css/feedback.css">
  <link rel="stylesheet" href="/static/css/input-fix.css">
  <link rel="stylesheet" href="/static/css/modal.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <style>
    /* 语音输入相关样式 */
    .voice-input-container {
      position: relative;
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 10px;
    }

    .voice-btn {
      background: #28a745;
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .voice-btn:hover {
      background: #218838;
      transform: scale(1.05);
    }

    .voice-btn.recording {
      background: #dc3545;
      animation: pulse 1s infinite;
    }

    .voice-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
      }
      70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
      }
      100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
      }
    }

    .voice-status {
      font-size: 12px;
      color: #666;
      margin-left: 10px;
    }

    .voice-status.recording {
      color: #dc3545;
      font-weight: bold;
    }

    .input-controls {
      display: flex;
      align-items: flex-end;
      gap: 10px;
      width: 100%;
    }

    .message-input-wrapper {
      flex: 1;
      position: relative;
    }

    .voice-recognition-result {
      background: #e8f5e8;
      border: 1px solid #28a745;
      border-radius: 8px;
      padding: 10px;
      margin-bottom: 10px;
      font-size: 14px;
      color: #155724;
      display: none;
    }

    .voice-recognition-result .close-btn {
      float: right;
      background: none;
      border: none;
      font-size: 16px;
      cursor: pointer;
      color: #155724;
    }

    .recording-indicator {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      display: none;
      color: #dc3545;
      font-size: 14px;
    }

    .voice-controls {
      display: flex;
      align-items: center;
      gap: 5px;
      margin-top: 5px;
      font-size: 12px;
      color: #666;
      width: 90px;
      margin-left: 10px;
    }

    .voice-upload-btn {
      background: #17a2b8;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      font-size: 12px;
      transition: background 0.3s;
    }

    .voice-upload-btn:hover {
      background: #138496;
    }

    #voice-file-input {
      display: none;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="/static/images/yingji.png" alt="应急指挥产品" class="logo-icon">
          <span class="logo-title">应急指挥智能体</span>
        </div>
      </div>
      <button class="new-chat-btn" id="new-chat-btn">
        <i class="fas fa-plus"></i>
        <span>新建会话</span>
      </button>
      <div class="history-header">
        <div class="history-title">历史记录</div>
        <button class="toggle-history-btn" id="toggle-history-btn" title="折叠/展开历史记录">
          <i class="fas fa-chevron-down"></i>
        </button>
      </div>
      <div class="history-list" id="history-list">
        <!-- 历史记录将通过JavaScript动态加载 -->
      </div>
      <div class="history-pagination" id="history-pagination" style="display: none;">
        <button id="prev-page-btn" class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
        <span id="page-info">第 <span id="current-page">1</span> 页</span>
        <button id="next-page-btn" class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
      </div>

      <!-- 返回主页按钮 -->
      <div style="margin-top: auto; padding: 20px;">
        <button class="upload-btn" onclick="window.location.href='/'">
          <i class="fas fa-home"></i> 返回主页
        </button>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 聊天区域 -->
      <div class="chat-container">
        <div class="header">
          <div class="welcome-text">
            <i class="fas fa-microphone" style="margin-right: 8px; color: #28a745;"></i>
            Hi，我是支持语音输入的智能助手，您可以语音提问或文字输入！
          </div>
          <div class="header-buttons">
            <button class="upload-btn" id="upload-btn">添加知识</button>
            <button class="event-btn" id="event-btn">文档提问</button>
            <button class="image-btn" id="image-btn">图片分析</button>
            <button class="scene-btn" id="scene-btn">场景分析</button>
          </div>
        </div>
        <div class="chat-history" id="chat-history">
          <!-- 聊天记录将通过JavaScript动态加载 -->
        </div>

        <!-- 语音识别结果显示区域 -->
        <div id="voice-recognition-result" class="voice-recognition-result">
          <button class="close-btn" onclick="hideVoiceResult()">&times;</button>
          <div id="voice-text"></div>
        </div>

        <div class="input-container">
          <div class="input-controls">
            <div class="message-input-wrapper">
              <textarea class="message-input" id="message-input" placeholder="请输入问题或点击麦克风进行语音输入..." rows="1"></textarea>
              <div id="recording-indicator" class="recording-indicator">
                <i class="fas fa-microphone"></i> 正在录音...
              </div>
            </div>

            <!-- 语音输入按钮 -->
            <button class="voice-btn" id="voice-btn" title="点击开始语音输入">
              <i class="fas fa-microphone"></i>
            </button>

            <button class="send-btn" id="send-btn">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>

          <!-- 语音控制选项 -->
          <div class="voice-controls">
            <label>
              <input type="checkbox" id="auto-send-checkbox" checked>
              自动发送
            </label>
            <!-- <button class="voice-upload-btn" onclick="document.getElementById('voice-file-input').click()">
              <i class="fas fa-upload"></i> 音频
            </button> -->
            <input type="file" id="voice-file-input" accept="audio/*">
            <span id="voice-status" class="voice-status"></span>
          </div>
        </div>
      </div>

      <!-- 推荐问题区域 -->
      <div class="recommended-questions">
        <div class="recommended-title">语音输入示例</div>
        <div id="recommended-questions">
          <div class="question-item" onclick="speakQuestion('告诉我应急响应的基本流程')">
            <i class="fas fa-volume-up"></i> "告诉我应急响应的基本流程"
          </div>
          <div class="question-item" onclick="speakQuestion('如何制定应急预案')">
            <i class="fas fa-volume-up"></i> "如何制定应急预案"
          </div>
          <div class="question-item" onclick="speakQuestion('应急物资如何管理')">
            <i class="fas fa-volume-up"></i> "应急物资如何管理"
          </div>
          <div class="question-item" onclick="speakQuestion('消防安全注意事项有哪些')">
            <i class="fas fa-volume-up"></i> "消防安全注意事项有哪些"
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 上传对话框 -->
  <div id="upload-dialog" class="dialog-overlay">
    <div class="dialog-content">
      <div class="dialog-header">
        <h3>添加知识</h3>
        <div class="dialog-header-actions">
          <button class="view-docs-btn" id="view-uploaded-docs">
            <i class="fas fa-eye"></i> 查看已上传文档
          </button>
          <button class="close-btn" onclick="hideUploadDialog()">×</button>
        </div>
      </div>
      <form id="upload-form" enctype="multipart/form-data">
        <div class="form-group">
          <label for="knowledge-title">* 知识标题</label>
          <input type="text" id="knowledge-title" name="title" placeholder="输入知识标题" required>
        </div>
        <div class="form-group">
          <label for="department">* 所属部门</label>
          <div class="custom-select-container">
            <input type="text" id="department" name="department" list="department-options" placeholder="选择或输入所属部门" required>
            <datalist id="department-options">
              <!-- 部门列表将通过JavaScript动态加载 -->
            </datalist>
          </div>
        </div>
        <div class="form-group">
          <label for="file-upload">* 上传文件</label>
          <div class="file-upload-area" id="drop-area">
            <div class="upload-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 16V8M12 8L9 11M12 8L15 11" stroke="#888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 15V16C3 17.6569 4.34315 19 6 19H18C19.6569 19 21 17.6569 21 16V15" stroke="#888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="upload-text">
              将文件拖放到此处或点击上传
            </div>
            <input type="file" id="file-upload" name="files" accept=".docx,.csv,.txt,.md" multiple style="display: none;">
          </div>
          <div class="file-info" id="file-info" style="display: none;">
            <span id="file-name"></span>
            <button type="button" class="remove-file" onclick="removeFile()">×</button>
          </div>
          <div class="file-types">支持的文件类型: .docx, .csv, .txt, .md </div>
        </div>
        <div class="form-actions">
          <button type="button" class="cancel-btn" onclick="hideUploadDialog()">取消</button>
          <button type="submit" class="submit-btn">提交</button>
        </div>
      </form>
      <div id="upload-status" class="upload-status" style="display: none;">
        <div class="spinner"></div>
        <div class="status-text">正在处理文档，请稍候...</div>
      </div>
    </div>
  </div>

  <script src="/static/js/main.js"></script>
  <script src="/static/js/recommended-questions.js"></script>
  <script src="/static/js/document-modal.js"></script>
  <script>
    // 语音相关全局变量
    let mediaRecorder = null;
    let audioChunks = [];
    let isRecording = false;
    let recognition = null;

    // 为事件提问按钮添加点击事件
    document.getElementById('event-btn').addEventListener('click', function() {
      window.location.href = '/document_analysis';
    });

    // 为图片分析按钮添加点击事件
    document.getElementById('image-btn').addEventListener('click', function() {
      window.location.href = '/image_analysis';
    });

    // 为场景分析按钮添加点击事件
    document.getElementById('scene-btn').addEventListener('click', function() {
      window.location.href = '/scene_analysis';
    });

    // 语音按钮事件
    document.getElementById('voice-btn').addEventListener('click', toggleVoiceRecording);

    // 音频文件上传事件
    document.getElementById('voice-file-input').addEventListener('change', handleAudioFileUpload);

    // 语音录音功能
    async function toggleVoiceRecording() {
      const voiceBtn = document.getElementById('voice-btn');
      const voiceStatus = document.getElementById('voice-status');
      const recordingIndicator = document.getElementById('recording-indicator');

      if (isRecording) {
        // 停止录音
        stopRecording();
      } else {
        // 开始录音
        await startRecording();
      }
    }

    async function startRecording() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true
          }
        });

        // 尝试使用WAV格式录音，如果不支持则回退到webm
        let mimeType = 'audio/wav';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'audio/webm;codecs=opus';
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'audio/webm';
          }
        }

        console.log('使用录音格式:', mimeType);

        mediaRecorder = new MediaRecorder(stream, {
          mimeType: mimeType
        });

        audioChunks = [];

        mediaRecorder.ondataavailable = event => {
          if (event.data.size > 0) {
            audioChunks.push(event.data);
          }
        };

        mediaRecorder.onstop = async () => {
          const audioBlob = new Blob(audioChunks, { type: mimeType });
          await processAudioBlob(audioBlob, mimeType);

          // 停止所有音频轨道
          stream.getTracks().forEach(track => track.stop());
        };

        mediaRecorder.start();
        isRecording = true;

        // 更新UI
        const voiceBtn = document.getElementById('voice-btn');
        const voiceStatus = document.getElementById('voice-status');
        const recordingIndicator = document.getElementById('recording-indicator');

        voiceBtn.classList.add('recording');
        voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
        voiceBtn.title = '点击停止录音';
        voiceStatus.textContent = '正在录音...';
        voiceStatus.classList.add('recording');
        recordingIndicator.style.display = 'block';

      } catch (error) {
        console.error('无法访问麦克风:', error);
        showMessage('无法访问麦克风，请检查权限设置', 'error');
      }
    }

    function stopRecording() {
      if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        isRecording = false;

        // 更新UI
        const voiceBtn = document.getElementById('voice-btn');
        const voiceStatus = document.getElementById('voice-status');
        const recordingIndicator = document.getElementById('recording-indicator');

        voiceBtn.classList.remove('recording');
        voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
        voiceBtn.title = '点击开始语音输入';
        voiceStatus.textContent = '录音完成，正在识别...';
        voiceStatus.classList.remove('recording');
        recordingIndicator.style.display = 'none';
      }
    }

    // 处理音频文件上传
    async function handleAudioFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        document.getElementById('voice-status').textContent = `已选择文件: ${file.name}，正在识别...`;
        await processAudioBlob(file);
      }
    }

    // 处理音频数据
    async function processAudioBlob(audioBlob, mimeType = 'audio/webm') {
      try {
        const formData = new FormData();

        // 根据MIME类型确定文件扩展名
        let fileName = 'recorded_audio.webm';
        if (mimeType.includes('wav')) {
          fileName = 'recorded_audio.wav';
        } else if (mimeType.includes('mp3')) {
          fileName = 'recorded_audio.mp3';
        } else if (mimeType.includes('ogg')) {
          fileName = 'recorded_audio.ogg';
        }

        formData.append('audio', audioBlob, fileName);
        formData.append('language', 'zh');

        const response = await fetch('/speech_to_text/', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.status === 'success') {
          const recognizedText = result.text;

          // 显示识别结果
          showVoiceResult(recognizedText);

          // 如果开启了自动发送，则直接发送
          if (document.getElementById('auto-send-checkbox').checked) {
            document.getElementById('message-input').value = recognizedText;
            document.getElementById('send-btn').click();
          } else {
            // 否则填入输入框
            document.getElementById('message-input').value = recognizedText;
            document.getElementById('message-input').focus();
          }

          document.getElementById('voice-status').textContent = '语音识别完成';
        } else {
          throw new Error(result.message || '语音识别失败');
        }

      } catch (error) {
        console.error('语音识别出错:', error);
        showMessage('语音识别失败: ' + error.message, 'error');
        document.getElementById('voice-status').textContent = '语音识别失败';
      }
    }

    // 显示语音识别结果
    function showVoiceResult(text) {
      const resultDiv = document.getElementById('voice-recognition-result');
      const textDiv = document.getElementById('voice-text');

      textDiv.textContent = `语音识别结果: "${text}"`;
      resultDiv.style.display = 'block';

      // 3秒后自动隐藏
      setTimeout(() => {
        hideVoiceResult();
      }, 3000);
    }

    // 隐藏语音识别结果
    function hideVoiceResult() {
      document.getElementById('voice-recognition-result').style.display = 'none';
    }

    // 模拟说出问题（实际上是填入输入框）
    function speakQuestion(question) {
      document.getElementById('message-input').value = question;
      if (document.getElementById('auto-send-checkbox').checked) {
        document.getElementById('send-btn').click();
      }
    }

    // 显示消息提示
    function showMessage(message, type = 'info') {
      // 创建消息元素
      const messageDiv = document.createElement('div');
      messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
      messageDiv.textContent = message;
      messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 10px 20px;
        border-radius: 5px;
        color: white;
        background: ${type === 'error' ? '#dc3545' : '#28a745'};
        z-index: 10000;
        max-width: 300px;
      `;

      document.body.appendChild(messageDiv);

      // 3秒后自动移除
      setTimeout(() => {
        if (messageDiv.parentNode) {
          messageDiv.parentNode.removeChild(messageDiv);
        }
      }, 3000);
    }

    // 检查浏览器语音支持
    document.addEventListener('DOMContentLoaded', function() {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        document.getElementById('voice-btn').disabled = true;
        document.getElementById('voice-status').textContent = '您的浏览器不支持语音输入';
        showMessage('您的浏览器不支持语音输入功能', 'error');
      }
    });
  </script>
</body>
</html>