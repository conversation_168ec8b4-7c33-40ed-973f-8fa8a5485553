#!/usr/bin/env python3
"""
动态代理集成示例
展示如何在主服务中集成改进后的动态工具系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agent.dynamic_agent_core import (
    create_dynamic_function_calling_agent, 
    check_dynamic_tool_trigger
)
from src.agent.agent_service import process_query  # 原有的查询处理函数

class EnhancedAgentService:
    """增强的代理服务，整合动态工具和原有功能"""
    
    def __init__(self):
        self.dynamic_agent = None
        self.initialize_dynamic_agent()
    
    def initialize_dynamic_agent(self):
        """初始化动态代理"""
        try:
            self.dynamic_agent = create_dynamic_function_calling_agent()
            if self.dynamic_agent:
                print("✓ 动态代理初始化成功")
            else:
                print("✗ 动态代理初始化失败")
        except Exception as e:
            print(f"✗ 动态代理初始化异常: {str(e)}")
    
    def process_user_query(self, user_input: str, chat_history: str = "") -> dict:
        """
        处理用户查询的统一入口
        优先检查动态工具触发，否则使用原有流程
        """
        try:
            # 1. 检查是否应该触发动态工具
            if check_dynamic_tool_trigger(user_input):
                print(f"🔧 触发动态工具处理: {user_input}")
                return self._handle_dynamic_tool_query(user_input, chat_history)
            
            # 2. 使用原有的查询处理逻辑
            print(f"💬 使用原有流程处理: {user_input}")
            return self._handle_regular_query(user_input, chat_history)
            
        except Exception as e:
            return {
                "success": False,
                "error": f"查询处理异常: {str(e)}",
                "output": "抱歉，处理您的请求时出现了错误。"
            }
    
    def _handle_dynamic_tool_query(self, user_input: str, chat_history: str = "") -> dict:
        """处理动态工具查询"""
        if not self.dynamic_agent:
            return {
                "success": False,
                "error": "动态代理未初始化",
                "output": "动态工具服务暂时不可用。"
            }
        
        try:
            # 调用动态代理
            result = self.dynamic_agent.invoke({
                "input": user_input,
                "chat_history": chat_history
            })
            
            return {
                "success": True,
                "output": result.get("output", ""),
                "type": "dynamic_tool"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"动态工具执行失败: {str(e)}",
                "output": "执行工具时出现错误，请稍后重试。"
            }
    
    def _handle_regular_query(self, user_input: str, chat_history: str = "") -> dict:
        """处理常规查询（使用原有逻辑）"""
        try:
            # 这里调用原有的 process_query 函数
            # 注意：您可能需要根据实际的 process_query 函数签名调整参数
            result = process_query(user_input, chat_history)
            
            if result is None:
                # 如果原有逻辑返回None，表示需要使用流式输出等其他处理
                return {
                    "success": True,
                    "output": None,  # None表示需要其他处理方式
                    "type": "regular_chat"
                }
            
            return {
                "success": True,
                "output": result,
                "type": "api_tool"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"常规查询处理失败: {str(e)}",
                "output": "处理您的请求时出现错误。"
            }
    
    def refresh_dynamic_tools(self):
        """刷新动态工具配置"""
        try:
            if self.dynamic_agent and hasattr(self.dynamic_agent, 'dynamic_tools_manager'):
                self.dynamic_agent.dynamic_tools_manager.refresh_tools()
                print("✓ 动态工具配置已刷新")
            else:
                print("✗ 无法刷新动态工具配置")
        except Exception as e:
            print(f"✗ 刷新动态工具配置失败: {str(e)}")


def demo_usage():
    """演示使用方法"""
    print("=== 动态代理集成演示 ===\n")
    
    # 创建增强的代理服务
    service = EnhancedAgentService()
    
    # 测试用例
    test_queries = [
        "查询事件周边资源",  # 可能触发动态工具
        "启动应急会议",      # 可能触发系统内置工具
        "今天天气怎么样",    # 常规聊天
        "事件附近有什么资源", # 可能触发动态工具
    ]
    
    for query in test_queries:
        print(f"用户输入: {query}")
        result = service.process_user_query(query)
        
        if result["success"]:
            print(f"处理类型: {result.get('type', 'unknown')}")
            if result["output"]:
                print(f"输出: {result['output'][:100]}...")  # 只显示前100个字符
            else:
                print("输出: [需要其他处理方式]")
        else:
            print(f"错误: {result['error']}")
        
        print("-" * 50)
    
    # 演示刷新动态工具
    print("\n刷新动态工具配置...")
    service.refresh_dynamic_tools()


if __name__ == "__main__":
    demo_usage()
