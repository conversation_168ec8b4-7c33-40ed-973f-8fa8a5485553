<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>应急指挥智能体</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <link rel="stylesheet" href="/static/css/feedback.css">
  <link rel="stylesheet" href="/static/css/input-fix.css">
  <link rel="stylesheet" href="/static/css/modal.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="/static/images/yingji.png" alt="应急指挥产品" class="logo-icon">
          <span class="logo-title">应急指挥智能体</span>
        </div>
      </div>
      <button class="new-chat-btn" id="new-chat-btn">
        <i class="fas fa-plus"></i>
        <span>新建会话</span>
      </button>
      <div class="history-header">
        <div class="history-title">历史记录</div>
        <button class="toggle-history-btn" id="toggle-history-btn" title="折叠/展开历史记录">
          <i class="fas fa-chevron-down"></i>
        </button>
      </div>
      <div class="history-list" id="history-list">
        <!-- 历史记录将通过JavaScript动态加载 -->
      </div>
      <div class="history-pagination" id="history-pagination" style="display: none;">
        <button id="prev-page-btn" class="pagination-btn"><i class="fas fa-chevron-left"></i></button>
        <span id="page-info">第 <span id="current-page">1</span> 页</span>
        <button id="next-page-btn" class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 聊天区域 -->
      <div class="chat-container">
        <div class="header">
          <div class="welcome-text">Hi，我是应急指挥智能体，请提问！</div>
          <div class="header-buttons">
            <button class="upload-btn" id="upload-btn">添加知识</button>
            <button class="event-btn" id="event-btn">文档提问</button>
            <button class="jiuan-btn" id="jiuan-btn">久安大模型</button>
            <!-- <button class="image-btn" id="image-btn">图片分析</button>
            <button class="scene-btn" id="scene-btn">场景分析</button> -->

            <!-- 用户头像和菜单 -->
            <div class="user-profile" id="user-profile">
              <div class="user-avatar" id="user-avatar">
                <span class="avatar-text" id="avatar-text">A</span>
              </div>
              <div class="user-info">
                <span class="username" id="username-display">admin</span>
                <span class="user-role" id="user-role">管理员</span>
              </div>
              <i class="fas fa-chevron-down dropdown-icon" id="dropdown-icon"></i>

              <!-- 下拉菜单 -->
              <div class="user-dropdown" id="user-dropdown">
                <div class="dropdown-header">
                  <div class="dropdown-avatar">
                    <span class="dropdown-avatar-text" id="dropdown-avatar-text">A</span>
                  </div>
                  <div class="dropdown-user-info">
                    <div class="dropdown-username" id="dropdown-username">admin</div>
                    <div class="dropdown-role" id="dropdown-role">管理员</div>
                  </div>
                </div>
                <div class="dropdown-divider"></div>
                <div class="dropdown-menu">
                  <button class="dropdown-item admin-item" id="admin-btn" style="display: none;">
                    <i class="fas fa-users"></i>
                    <span>用户管理</span>
                  </button>
                  <button class="dropdown-item logout-item" id="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>退出登录</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-history" id="chat-history">
          <!-- 聊天记录将通过JavaScript动态加载 -->
        </div>
        <div class="input-container">
          <div class="input-controls">
            <div class="rag-toggle">
              <label class="toggle-switch">
                <input type="checkbox" id="rag-toggle">
                <span class="toggle-slider"></span>
              </label>
              <span class="toggle-label">知识库</span>
            </div>
          </div>
          <div class="input-row">
            <textarea class="message-input" id="message-input" placeholder="请输入问题..." rows="1"></textarea>
            <button class="send-btn" id="send-btn">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 推荐问题区域 -->
      <div class="recommended-questions">
        <div class="recommended-title">示例问题</div>
        <div id="recommended-questions">
          <!-- 推荐问题将通过JavaScript动态加载 -->
        </div>
      </div>
    </div>
  </div>

  <!-- 上传对话框 -->
  <div id="upload-dialog" class="dialog-overlay">
    <div class="dialog-content">
      <div class="dialog-header">
        <h3>添加知识</h3>
        <div class="dialog-header-actions">
          <button class="view-docs-btn" id="view-uploaded-docs">
            <i class="fas fa-eye"></i> 查看已上传文档
          </button>
          <button class="close-btn" onclick="hideUploadDialog()">×</button>
        </div>
      </div>
      <form id="upload-form" enctype="multipart/form-data">
        <div class="form-group">
          <label for="knowledge-title">* 知识标题</label>
          <input type="text" id="knowledge-title" name="title" placeholder="输入知识标题" required>
        </div>
        <div class="form-group">
          <label for="department">* 所属部门</label>
          <div class="custom-select-container">
            <input type="text" id="department" name="department" list="department-options" placeholder="选择或输入所属部门" required>
            <datalist id="department-options">
              <!-- 部门列表将通过JavaScript动态加载 -->
            </datalist>
          </div>
        </div>
        <div class="form-group">
          <label for="file-upload">* 上传文件</label>
          <div class="file-upload-area" id="drop-area">
            <div class="upload-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 16V8M12 8L9 11M12 8L15 11" stroke="#888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M3 15V16C3 17.6569 4.34315 19 6 19H18C19.6569 19 21 17.6569 21 16V15" stroke="#888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="upload-text">
              将文件拖放到此处或点击上传
            </div>
            <input type="file" id="file-upload" name="files" accept=".docx,.csv,.txt,.md" multiple style="display: none;">
          </div>
          <div class="file-info" id="file-info" style="display: none;">
            <span id="file-name"></span>
            <button type="button" class="remove-file" onclick="removeFile()">×</button>
          </div>
          <div class="file-types">支持的文件类型: .docx, .csv, .txt, .md </div>
        </div>
        <div class="form-actions">
          <button type="button" class="cancel-btn" onclick="hideUploadDialog()">取消</button>
          <button type="submit" class="submit-btn">提交</button>
        </div>
      </form>
      <div id="upload-status" class="upload-status" style="display: none;">
        <div class="spinner"></div>
        <div class="status-text">正在处理文档，请稍候...</div>
      </div>
    </div>
  </div>

  <script src="/static/js/main.js"></script>
  <script src="/static/js/recommended-questions.js"></script>
  <script src="/static/js/document-modal.js"></script>
  <script>
    // 为事件提问按钮添加点击事件
    document.getElementById('event-btn').addEventListener('click', function() {
      window.location.href = '/document_analysis';
    });

    // // 为图片分析按钮添加点击事件
    // document.getElementById('image-btn').addEventListener('click', function() {
    //   window.location.href = '/image_analysis';
    // });

    // // 为场景分析按钮添加点击事件
    // document.getElementById('scene-btn').addEventListener('click', function() {
    //   window.location.href = '/scene_analysis';
    // });
    //为久安大模型按钮添加点击事件
    document.getElementById('jiuan-btn').addEventListener('click', function() {
      window.location.href = '/jiuan_model';
    });

    // 检查登录状态并显示用户信息
    const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
    if (userInfo.username) {
      // 调用displayUserInfo函数来设置所有用户信息
      displayUserInfo(userInfo);
    }
  </script>
</body>
</html>