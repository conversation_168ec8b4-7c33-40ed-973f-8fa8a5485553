"""
TTS流式处理模块
用于处理大模型流式输出的文本转语音功能
"""

import re
import asyncio
import logging
from typing import Optional, AsyncGenerator, Dict, Any
from src.tts_engine import TTSEngine

logger = logging.getLogger(__name__)

class TTSStreamHandler:
    """TTS流式处理器"""
    
    def __init__(self, tts_engine: TTSEngine):
        self.tts_engine = tts_engine
        
    def clean_text_for_tts(self, text: str) -> str:
        """
        清理文本，移除不适合朗读的内容
        """
        if not text:
            return ""
            
        # 移除think标签及其内容
        text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
        
        # 移除source标签及其内容
        text = re.sub(r'<source>.*?</source>', '', text, flags=re.DOTALL)
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除Markdown链接格式，保留链接文本
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)
        
        # 移除Markdown代码块
        text = re.sub(r'```[^`]*```', '', text, flags=re.DOTALL)
        text = re.sub(r'`[^`]+`', '', text)
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        return text
        
    def should_trigger_tts(self, accumulated_text: str, min_length: int = 50) -> bool:
        """
        判断是否应该触发TTS
        当累积的文本达到一定长度且以句号、问号、感叹号结尾时触发
        """
        if not accumulated_text or len(accumulated_text) < min_length:
            return False
            
        # 检查是否以句子结束符结尾
        return accumulated_text.rstrip().endswith(('.', '。', '!', '！', '?', '？'))
        
    async def process_stream_for_tts(
        self, 
        stream_text: AsyncGenerator[str, None],
        voice: str = "zf_001",
        speed: float = 1.0,
        format: str = "wav"
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理流式文本，在适当时机生成TTS音频
        
        Args:
            stream_text: 流式文本生成器
            voice: 语音模型名称
            speed: 语速倍率
            format: 音频格式
            
        Yields:
            Dict包含type和data字段：
            - type: "text" 表示文本片段，"audio" 表示音频数据
            - data: 对应的数据内容
        """
        accumulated_text = ""
        sentence_buffer = ""
        
        try:
            async for text_chunk in stream_text:
                # 先返回文本片段
                yield {
                    "type": "text",
                    "data": text_chunk
                }
                
                # 累积文本用于TTS处理
                accumulated_text += text_chunk
                
                # 清理文本
                clean_text = self.clean_text_for_tts(accumulated_text)
                
                # 检查是否有新的完整句子
                sentences = re.split(r'([.。!！?？])', clean_text)
                
                # 处理完整的句子
                for i in range(0, len(sentences) - 1, 2):
                    if i + 1 < len(sentences):
                        sentence = sentences[i] + sentences[i + 1]
                        sentence = sentence.strip()
                        
                        if sentence and len(sentence) > 10:  # 只处理有意义的句子
                            try:
                                # 生成TTS音频
                                audio_file, metadata = await self.tts_engine.synthesize(
                                    text=sentence,
                                    voice=voice,
                                    speed=speed,
                                    format=format
                                )
                                
                                # 读取音频文件内容
                                with open(audio_file, 'rb') as f:
                                    audio_data = f.read()
                                
                                # 返回音频数据
                                yield {
                                    "type": "audio",
                                    "data": {
                                        "audio": audio_data,
                                        "metadata": metadata,
                                        "text": sentence
                                    }
                                }
                                
                                logger.info(f"生成TTS音频: {sentence[:30]}...")
                                
                            except Exception as e:
                                logger.error(f"TTS生成失败: {e}")
                                # TTS失败不影响文本流式输出
                                continue
                
                # 更新累积文本，移除已处理的句子
                if len(sentences) >= 2:
                    # 保留最后一个不完整的句子
                    accumulated_text = sentences[-1] if len(sentences) % 2 == 1 else ""
                    
        except Exception as e:
            logger.error(f"TTS流式处理出错: {e}")
            
    async def generate_final_tts(
        self,
        final_text: str,
        voice: str = "zf_001", 
        speed: float = 1.0,
        format: str = "wav"
    ) -> Optional[Dict[str, Any]]:
        """
        为最终的完整文本生成TTS音频
        
        Args:
            final_text: 完整的响应文本
            voice: 语音模型名称
            speed: 语速倍率
            format: 音频格式
            
        Returns:
            包含音频数据的字典，如果失败则返回None
        """
        try:
            # 清理文本
            clean_text = self.clean_text_for_tts(final_text)
            
            if not clean_text or len(clean_text.strip()) < 5:
                return None
                
            # 生成TTS音频
            audio_file, metadata = await self.tts_engine.synthesize(
                text=clean_text,
                voice=voice,
                speed=speed,
                format=format
            )
            
            # 读取音频文件内容
            with open(audio_file, 'rb') as f:
                audio_data = f.read()
            
            return {
                "audio": audio_data,
                "metadata": metadata,
                "text": clean_text
            }
            
        except Exception as e:
            logger.error(f"最终TTS生成失败: {e}")
            return None
            
    def extract_clean_response(self, full_response: str) -> str:
        """
        从完整响应中提取干净的正文内容
        移除think和source标签内容
        """
        # 移除think标签及其内容
        clean_text = re.sub(r'<think>.*?</think>', '', full_response, flags=re.DOTALL)
        
        # 移除source标签及其内容  
        clean_text = re.sub(r'<source>.*?</source>', '', clean_text, flags=re.DOTALL)
        
        # 清理多余空白
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        
        return clean_text
