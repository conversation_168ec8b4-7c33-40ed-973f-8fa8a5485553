"""
API工具模块 - 包含API配置加载和URL构建功能
"""

import os
import configparser
import logging

# 创建logger
logger = logging.getLogger("langchain_agent")

def load_api_config():
    """
    从config.ini加载API配置信息
    """
    try:
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        
        # 检查是否存在API部分
        if 'API' not in config:
            logger.error("配置文件中缺少[API]部分")
            return None
        
        # 加载基础URL
        base_url = config.get('API', 'BASE_URL', fallback='')
        if not base_url:
            logger.error("缺少API基础URL配置")
            return None
        
        # 读取路径配置
        default_path = config.get('API', 'DEFAULT_PATH', fallback='')
        repository_path = config.get('API', 'REPOSITORY_PATH', fallback='')
        plan_path = config.get('API', 'PLAN_PATH', fallback='')
        
        # 构建路径配置字典
        endpoint_paths = {
            "default": default_path,
            "repository": repository_path,
            "plan": plan_path
        }
        
        api_config = {
            'base_url': base_url,
            'endpoint_paths': endpoint_paths,
            'content_type': config.get('API', 'CONTENT_TYPE', fallback=''),
            'token': config.get('API', 'TOKEN', fallback='')
        }
        
        # 验证必要的配置项
        if not api_config['token']:
            logger.error("缺少API访问令牌配置")
            return None
        
        return api_config
    except Exception as e:
        logger.error(f"加载API配置失败: {str(e)}")
        return None

def get_api_url(api_type="default", endpoint=""):
    """
    获取特定API的完整URL
    :param api_type: API类型，对应配置文件中的路径键
    :param endpoint: API端点路径
    :return: 完整的API URL
    """
    api_config = load_api_config()
    if not api_config:
        logger.error("无法加载API配置")
        return None
    
    base_url = api_config["base_url"]
    endpoint_paths = api_config["endpoint_paths"]
    
    # 获取API路径，如果不存在则使用默认路径
    if api_type in endpoint_paths and endpoint_paths[api_type]:
        path = endpoint_paths[api_type]
    else:
        logger.warning(f"未找到API类型 {api_type} 的路径配置，使用默认路径")
        path = endpoint_paths.get("default", "")
    
    # 确保路径以斜杠开头
    if path and not path.startswith("/"):
        path = f"/{path}"
    
    # 确保路径以斜杠结尾
    if path and not path.endswith("/"):
        path = f"{path}/"
    
    # 构建完整URL
    if endpoint:
        # 确保endpoint不以斜杠开头
        if endpoint.startswith("/"):
            endpoint = endpoint[1:]
        return f"{base_url}{path}{endpoint}"
    else:
        return f"{base_url}{path}" 