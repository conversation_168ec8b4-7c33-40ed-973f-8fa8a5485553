"""
API工具配置管理端点
"""

import logging
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse, FileResponse
from src.db_manager import DBManager

# 创建logger
logger = logging.getLogger("api_tools_endpoints")

# 创建路由器
router = APIRouter(prefix="/api/tools", tags=["API工具配置"])

# 数据库管理器实例
db_manager = DBManager()


@router.get("")
async def get_api_tools():
    """获取所有API工具配置"""
    try:
        tools = db_manager.get_all_api_tools()
        return {"success": True, "tools": tools}
    except Exception as e:
        logger.error(f"获取API工具配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"success": False, "message": str(e)}
        )


@router.get("/{tool_id}")
async def get_api_tool(tool_id: int):
    """获取单个API工具配置"""
    try:
        tool = db_manager.get_api_tool(tool_id)
        if tool:
            return {"success": True, "tool": tool}
        else:
            raise HTTPException(
                status_code=404,
                detail={"success": False, "message": "工具配置不存在"}
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取API工具配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"success": False, "message": str(e)}
        )


@router.post("")
async def create_api_tool(request: Request):
    """创建API工具配置"""
    try:
        data = await request.json()
        tool_id = db_manager.save_api_tool(data)
        if tool_id:
            return {"success": True, "tool_id": tool_id, "message": "工具配置创建成功"}
        else:
            raise HTTPException(
                status_code=400,
                detail={"success": False, "message": "创建工具配置失败"}
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建API工具配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"success": False, "message": str(e)}
        )


@router.put("/{tool_id}")
async def update_api_tool(tool_id: int, request: Request):
    """更新API工具配置"""
    try:
        data = await request.json()
        success = db_manager.update_api_tool(tool_id, data)
        if success:
            return {"success": True, "message": "工具配置更新成功"}
        else:
            raise HTTPException(
                status_code=400,
                detail={"success": False, "message": "更新工具配置失败"}
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新API工具配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"success": False, "message": str(e)}
        )


@router.delete("/{tool_id}")
async def delete_api_tool(tool_id: int):
    """删除API工具配置"""
    try:
        success = db_manager.delete_api_tool(tool_id)
        if success:
            return {"success": True, "message": "工具配置删除成功"}
        else:
            raise HTTPException(
                status_code=400,
                detail={"success": False, "message": "删除工具配置失败"}
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除API工具配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"success": False, "message": str(e)}
        )


@router.get("/export")
async def export_api_tools():
    """导出API工具配置"""
    try:
        tools = db_manager.export_api_tools()
        return {"success": True, "tools": tools}
    except Exception as e:
        logger.error(f"导出API工具配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"success": False, "message": str(e)}
        )


@router.post("/import")
async def import_api_tools(request: Request):
    """导入API工具配置"""
    try:
        data = await request.json()
        tools_data = data.get("tools", [])
        success_count, error_count = db_manager.import_api_tools(tools_data)
        return {
            "success": True, 
            "success_count": success_count, 
            "error_count": error_count,
            "message": f"导入完成，成功: {success_count}, 失败: {error_count}"
        }
    except Exception as e:
        logger.error(f"导入API工具配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"success": False, "message": str(e)}
        )


@router.post("/refresh")
async def refresh_tools_cache():
    """刷新动态工具缓存"""
    try:
        from src.agent.dynamic_tools_manager import dynamic_tools_manager
        dynamic_tools_manager.refresh_tools()
        return {"success": True, "message": "工具缓存刷新成功"}
    except Exception as e:
        logger.error(f"刷新工具缓存失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"success": False, "message": str(e)}
        )


# 创建一个单独的路由器用于页面访问
page_router = APIRouter(tags=["页面访问"])


@page_router.get("/tools-config")
async def tools_config_page():
    """API工具配置页面"""
    return FileResponse("static/api_tools_config.html")
