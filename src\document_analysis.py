#!/usr/bin/env python3
import asyncio
import configparser
import glob
import logging
import os
from typing import AsyncGenerator, List, Dict, Any, Optional

from dotenv import load_dotenv
from langchain.callbacks import AsyncIteratorCallbackHandler
from langchain.chains.question_answering import load_qa_chain
from langchain.prompts import PromptTemplate
from langchain_community.document_loaders import (
    PyPDFLoader,
    TextLoader,
    CSVLoader,
    Docx2txtLoader,
    UnstructuredMarkdownLoader,
)
from langchain_community.llms import Ollama
from langchain_openai import ChatOpenAI
# 导入 Langchain Document 类
from langchain.schema import Document

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("direct_document_analysis")

# 加载环境变量
load_dotenv()

# 文档分析模板 (保持不变或微调)
# 注意：这里的 {context} 将会是所有文档内容的集合
ANALYSIS_TEMPLATE = """
你是一个专业的文档分析助手，能够精确理解用户的问题并对提供的完整文档内容进行深入分析。

**参考文档全文内容**：
{context}

**用户问题**：
{question}

**分析要求**：
1. 请仔细理解用户问题的核心意图，并基于**完整的**文档内容进行有针对性的分析。
2. 仅使用提供的文档内容作为分析依据，不要添加未在文档中提及的信息或进行外部检索。
3. 回答要全面、有条理、有深度，必要时可以引用文档中的关键内容。
4. 如果问题无法从文档内容中得到回答，请明确指出并说明原因。
5. 请使用中文回答，保持专业、客观的语言风格。
6. 如果文档包含多个相关但不同的信息点，请进行整合对比分析。
"""

# 文件类型和对应的加载器 (保持不变)
FILE_LOADERS = {
    ".pdf": PyPDFLoader,
    ".txt": TextLoader,
    ".csv": CSVLoader,
    ".md": UnstructuredMarkdownLoader,
    ".docx": Docx2txtLoader
    #".doc": Docx2txtLoader, # .doc 也用 Docx2txtLoader 尝试加载
}

async def load_documents_from_directory(directory_path: str, file_list: Optional[List[str]] = None) -> List[Document]:
    """
    从指定目录加载文档
    Args:
        directory_path: 文档所在目录路径
        file_list: 可选的文件名列表，如果提供则只加载指定的文件
    返回 Langchain Document 对象列表
    """
    documents = []
    logger.info(f"开始从目录加载文档: {directory_path}")
    logger.info(f"指定文件列表: {file_list if file_list else '未指定，将加载所有文件'}")

    # 确保目录存在
    if not os.path.exists(directory_path):
        logger.error(f"目录不存在: {directory_path}")
        return documents
    if not os.path.isdir(directory_path):
        logger.error(f"提供的路径不是一个目录: {directory_path}")
        return documents

    # 遍历目录中的文件
    found_files = False
    if file_list:
        # 如果提供了文件列表，只处理指定的文件
        for filename in file_list:
            file_path = os.path.join(directory_path, filename)
            if not os.path.exists(file_path):
                logger.warning(f"指定的文件不存在: {file_path}")
                continue
            if not os.path.isfile(file_path):
                logger.warning(f"指定的路径不是文件: {file_path}")
                continue

            found_files = True
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in FILE_LOADERS:
                try:
                    logger.info(f"尝试加载文件: {file_path}")
                    loader_class = FILE_LOADERS[file_ext]
                    loader = loader_class(file_path)
                    file_documents = loader.load()
                    if file_documents:
                        documents.extend(file_documents)
                        logger.info(f"成功加载文件: {file_path}, 页数/片段数: {len(file_documents)}")
                    else:
                        logger.warning(f"文件加载成功但未返回任何内容: {file_path}")
                except Exception as e:
                    logger.error(f"加载文件失败 {file_path}: {str(e)}", exc_info=True)
            else:
                logger.warning(f"跳过不支持的文件类型: {file_path}")
    else:
        # 如果没有提供文件列表，加载目录中的所有文件
        for file_path in glob.glob(os.path.join(directory_path, "*")):
            if os.path.isfile(file_path):
                found_files = True
                file_ext = os.path.splitext(file_path)[1].lower()
                if file_ext in FILE_LOADERS:
                    try:
                        logger.info(f"尝试加载文件: {file_path}")
                        loader_class = FILE_LOADERS[file_ext]
                        loader = loader_class(file_path)
                        file_documents = loader.load()
                        if file_documents:
                            documents.extend(file_documents)
                            logger.info(f"成功加载文件: {file_path}, 页数/片段数: {len(file_documents)}")
                        else:
                            logger.warning(f"文件加载成功但未返回任何内容: {file_path}")
                    except Exception as e:
                        logger.error(f"加载文件失败 {file_path}: {str(e)}", exc_info=True)
                else:
                    logger.warning(f"跳过不支持的文件类型: {file_path}")
            else:
                logger.debug(f"跳过非文件项: {file_path}")

    if not found_files:
        logger.warning(f"目录 {directory_path} 中没有找到任何{'指定的' if file_list else ''}文件。")
    elif not documents:
        logger.warning(f"目录 {directory_path} 中找到了文件，但未能成功加载任何支持的文档内容。请检查文件格式或加载器。")

    logger.info(f"总共加载了 {len(documents)} 个文档页面/片段")
    return documents

async def analyze_documents_stream(
    query: str,
    directory_path: str = r"D:\LLM\RAG\RAG_demo6.5.7_askcase\Private_GPT\upload_files", # 使用原始字符串防止转义问题
    file_list: Optional[List[str]] = None
) -> AsyncGenerator[str, None]:
    """
    直接加载目录中的文档，并将其内容传递给LLM进行分析，流式返回结果。
    不使用向量数据库。

    Args:
        query: 用户的查询问题
        directory_path: 文档所在目录路径
        file_list: 可选的文件名列表，如果提供则只分析指定的文件
    """
    logger.info(f"开始直接文档分析查询: '{query}'")
    logger.info(f"目标文档目录: {directory_path}")
    logger.info(f"指定文件列表: {file_list if file_list else '未指定，将分析所有文件'}")

    # 添加思考过程标记
    #yield "<think>正在分析问题并加载文档内容...</think>"

    # 1. 加载文档
    try:
        documents = await load_documents_from_directory(directory_path, file_list)
        if not documents:
            yield f"错误：在目录 '{directory_path}' 中未能加载任何{'指定的' if file_list else ''}可分析的文档。请检查路径、文件是否存在或文件格式是否支持。"
            return
        logger.info(f"成功加载 {len(documents)} 个文档片段/页面用于分析。")

        # --- 检查文档内容是否过长 (可选，但推荐) ---
        total_chars = sum(len(doc.page_content) for doc in documents)
        logger.info(f"加载的总字符数: {total_chars}")

        # 添加思考过程
        #yield f"<think>成功加载了{len(documents)}个文档片段，总计{total_chars}个字符。\n\n正在准备分析...</think>"

        # 可以在这里根据你LLM上下文限制添加一个警告或错误
        # 例如: if total_chars > 30000: logger.warning("文档总内容可能过长，超过LLM上下文限制！")

    except Exception as e:
        logger.error(f"加载文档过程中发生严重错误: {str(e)}", exc_info=True)
        yield f"错误：加载文档时出错，请查看服务器日志。错误信息: {str(e)}"
        return

    # 2. 配置LLM (复用原代码逻辑)
    config = configparser.ConfigParser()
    try:
        # 尝试从当前目录、上级目录和项目根目录加载配置文件
        config_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini"),  # src目录中
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.ini"),  # 项目根目录
            "config.ini"  # 当前工作目录
        ]

        config_loaded = False
        for config_path in config_paths:
            if os.path.exists(config_path):
                try:
                    # 明确指定 UTF-8 编码
                    config.read(config_path, encoding='utf-8')
                    logger.info(f"成功从 {config_path} 加载配置文件")
                    config_loaded = True
                    break
                except Exception as e:
                    logger.warning(f"使用 UTF-8 编码从 {config_path} 加载配置文件失败: {str(e)}")
                    try:
                        config.read(config_path, encoding='latin1')
                        logger.info(f"成功从 {config_path} 使用latin1编码加载配置文件")
                        config_loaded = True
                        break
                    except Exception as e2:
                        logger.warning(f"使用 latin1 编码从 {config_path} 加载配置文件失败: {str(e2)}")

        if not config_loaded:
            logger.error("未能从任何位置加载配置文件")
            yield "错误：加载服务器配置失败，请联系管理员查看日志。"
            return

    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}", exc_info=True)
        yield "错误：加载服务器配置失败，请联系管理员查看日志。"
        return

    # 初始化回调处理器
    callback_handler = AsyncIteratorCallbackHandler()

    # 获取LLM配置
    llm_type = config.get("LLM", "LLM_TYPE", fallback="ollama")
    model_name = config.get("LLM", "MODEL", fallback="deepseek-r1:32b") # 注意模型名称可能需要适配
    logger.info(f"配置的LLM类型: {llm_type}, 模型: {model_name}")

    # 添加思考过程
    #yield f"<think>使用{llm_type}类型的语言模型: {model_name}\n\n正在初始化模型...</think>"

    try:
        # 根据配置选择LLM类型
        if llm_type.lower() == "openai":
            openai_api_key = config.get("LLM", "OPENAI_API_KEY", fallback=os.getenv("OPENAI_API_KEY")) # 优先从配置读，再从环境变量读
            openai_api_base = config.get("LLM", "OPENAI_API_BASE", fallback=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1"))

            if not openai_api_key:
                logger.error("OpenAI API Key未配置 (config.ini 或 OPENAI_API_KEY 环境变量)")
                yield "错误：OpenAI API Key 未配置。"
                return

            logger.info(f"使用OpenAI模型: {model_name}, API Base: {openai_api_base}")
            llm = ChatOpenAI(
                model_name=model_name,
                openai_api_key=openai_api_key,
                openai_api_base=openai_api_base,
                streaming=True,
                callbacks=[callback_handler],
                temperature=0.7 # 可以根据需要调整
            )
        else:
            # 默认使用Ollama
            ollama_base_url = config.get("LLM", "OLLAMA_BASE_URL", fallback="http://localhost:11434")
            logger.info(f"使用Ollama模型: {model_name}, Base URL: {ollama_base_url}")
            llm = Ollama(
                model=model_name,
                base_url=ollama_base_url,
                callbacks=[callback_handler],
                temperature=0.7,
                num_ctx=40000 # 根据需要可以添加 num_ctx 等参数，但注意它可能与模型本身限制交互
            )
    except Exception as e:
        logger.error(f"初始化LLM失败: {str(e)}", exc_info=True)
        yield f"错误：初始化语言模型失败，请检查配置和模型服务状态。错误: {str(e)}"
        return

    # 3. 创建并运行 QA 链 (使用 load_qa_chain)
    # 创建提示模板
    prompt = PromptTemplate(
        template=ANALYSIS_TEMPLATE,
        input_variables=["context", "question"]
    )



    # 使用 load_qa_chain，选择 "stuff" 方法将所有文档内容塞入上下文
    # 如果文档过大，可以考虑 chain_type="map_reduce" 或 "refine"
    chain_type = "stuff" # 对于内容不多的情况适用
    logger.info(f"创建QA链，类型: {chain_type}")
    try:
        qa_chain = load_qa_chain(
            llm=llm,
            chain_type=chain_type, # "stuff", "map_reduce", "refine", "map_rerank"
            prompt=prompt # 将自定义的 prompt 传递给 chain
        )
    except Exception as e:
         logger.error(f"创建 QA 链失败: {str(e)}", exc_info=True)
         yield f"错误: 创建处理链失败。错误: {str(e)}"
         return


    # 4. 异步执行查询并流式返回
    logger.info("开始异步执行QA链...")
    # 创建一个任务来运行QA链
    # load_qa_chain 需要的输入是 'input_documents' 和 'question'
    task = asyncio.create_task(
        qa_chain.acall({"input_documents": documents, "question": query})
    )

    # 开始流式输出结果
    try:
        async for token in callback_handler.aiter():
            yield token
        logger.debug("Callback aiter() 迭代完成.")
    except Exception as e:
        logger.error(f"流式输出Token时出错: {str(e)}", exc_info=True)
        yield f"\n错误：在生成回答时发生内部错误。请查看日志。错误: {str(e)}"
        # 即使aiter出错，也尝试等待任务完成以获取可能的最终错误
        # 但要小心，如果aiter是因为task内部错误而停止，这里可能会重复报错

    # 等待任务完成并处理最终结果或错误
    try:
        result = await task
        logger.info(f"QA链执行完成。")
        # logger.debug(f"QA链最终结果: {result}") # 可选：打印最终完整结果（通常包含在流式输出里了）

    except Exception as e:
        logger.error(f"QA链异步任务执行失败: {str(e)}", exc_info=True)
        # 检查是否已经输出了错误信息，避免重复
        if not callback_handler.done.is_set() or callback_handler.response is None:
             # 如果流式输出没出错，但最终任务失败了，补充错误信息
            yield f"\n\n错误：查询执行过程中出现错误，未能完全生成回答。请稍后重试或联系管理员。错误详情请查看服务器日志。"

    logger.info("直接文档分析流处理结束。")