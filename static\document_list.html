<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>知识库文档列表</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    .document-list-container {
      max-width: 100%;
      margin: 0 auto;
      padding: 15px;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    .page-title {
      font-size: 24px;
      font-weight: bold;
      color: var(--text-color);
    }

    .back-btn {
      padding: 8px 16px;
      background-color: #4e6ef2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .back-btn:hover {
      background-color: #3a5ae8;
    }

    .folder-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .folder-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      transition: transform 0.2s, box-shadow 0.2s;
    }

    .folder-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    }

    .folder-header {
      padding: 15px;
      background-color: #f5f5f5;
      border-bottom: 1px solid var(--border-color);
    }

    .folder-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
      color: var(--text-color);
    }

    .folder-info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: var(--light-text-color);
    }

    .folder-department {
      background-color: #e6f7ff;
      padding: 2px 8px;
      border-radius: 10px;
      color: #1890ff;
    }

    .folder-content {
      padding: 15px;
    }

    .file-list {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .file-item:last-child {
      border-bottom: none;
    }

    .file-icon {
      margin-right: 10px;
      color: #666;
      width: 20px;
      text-align: center;
    }

    .file-name {
      flex: 1;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .file-ext {
      font-size: 12px;
      color: #999;
      padding: 2px 6px;
      background-color: #f5f5f5;
      border-radius: 4px;
      margin-left: 8px;
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: var(--light-text-color);
    }

    .empty-icon {
      font-size: 48px;
      margin-bottom: 20px;
      color: #ddd;
    }

    .loading {
      text-align: center;
      padding: 40px 20px;
    }

    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid var(--primary-color);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-message {
      text-align: center;
      padding: 20px;
      color: #f44336;
      background-color: #ffebee;
      border-radius: 4px;
      margin-bottom: 20px;
    }

    .refresh-btn {
      padding: 8px 16px;
      background-color: #4e6ef2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-top: 10px;
    }

    .refresh-btn:hover {
      background-color: #3a5ae8;
    }

    /* 筛选器样式 */
    .filter-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .filter-label {
      font-weight: bold;
      margin-right: 15px;
      color: #555;
    }

    .department-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .filter-btn {
      padding: 6px 12px;
      background-color: #f0f0f0;
      border: 1px solid #ddd;
      border-radius: 20px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s;
    }

    .filter-btn:hover {
      background-color: #e0e0e0;
    }

    .filter-btn.active {
      background-color: #4e6ef2;
      color: white;
      border-color: #4e6ef2;
    }

    /* 空筛选结果样式 */
    .empty-filter-result {
      text-align: center;
      padding: 30px;
      color: #888;
      background-color: #f9f9f9;
      border-radius: 8px;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="document-list-container">
    <div class="page-header">
      <h1 class="page-title">知识库文档列表</h1>
      <button class="back-btn" id="refresh-btn">
        <i class="fas fa-sync-alt"></i> 刷新列表
      </button>
    </div>

    <div class="filter-container">
      <div class="filter-label">按部门筛选：</div>
      <div class="department-filters" id="department-filters">
        <button class="filter-btn active" data-department="all">全部</button>
        <!-- 部门筛选按钮将通过JavaScript动态生成 -->
      </div>
    </div>

    <div id="content-area">
      <div class="loading" id="loading-indicator">
        <div class="spinner"></div>
        <div>正在加载文档列表...</div>
      </div>
    </div>
  </div>

  <script>
    // 全局变量存储所有文件夹数据
    let allFolders = [];
    // 当前选中的部门筛选器
    let currentDepartment = 'all';

    // 获取文件图标
    function getFileIcon(fileName) {
      const ext = fileName.split('.').pop().toLowerCase();

      switch(ext) {
        case 'pdf':
          return '<i class="fas fa-file-pdf" style="color: #e74c3c;"></i>';
        case 'doc':
        case 'docx':
          return '<i class="fas fa-file-word" style="color: #2b579a;"></i>';
        case 'xls':
        case 'xlsx':
          return '<i class="fas fa-file-excel" style="color: #217346;"></i>';
        case 'ppt':
        case 'pptx':
          return '<i class="fas fa-file-powerpoint" style="color: #d24726;"></i>';
        case 'txt':
          return '<i class="fas fa-file-alt" style="color: #888;"></i>';
        case 'csv':
          return '<i class="fas fa-file-csv" style="color: #217346;"></i>';
        case 'md':
          return '<i class="fab fa-markdown" style="color: #555;"></i>';
        default:
          return '<i class="fas fa-file" style="color: #888;"></i>';
      }
    }

    // 解析文件夹名称
    function parseFolderName(folderName) {
      // 文件夹名称格式: timestamp_folder_id_title_department
      const parts = folderName.split('_');

      if (parts.length >= 4) {
        const timestamp = parts[0];
        const folderId = parts[1];

        // 获取标题和部门
        const remainingParts = parts.slice(2).join('_');
        const lastUnderscoreIndex = remainingParts.lastIndexOf('_');

        if (lastUnderscoreIndex !== -1) {
          const title = remainingParts.substring(0, lastUnderscoreIndex);
          const department = remainingParts.substring(lastUnderscoreIndex + 1);

          // 格式化时间戳
          // 处理时间戳中的下划线，例如 "20250422_152927"
          const parts = timestamp.split('_');
          const datePart = parts[0]; // 日期部分，例如 "20250422"
          const timePart = parts[1] || ''; // 时间部分，例如 "152927"

          const year = datePart.substring(0, 4);
          const month = datePart.substring(4, 6);
          const day = datePart.substring(6, 8);

          // 提取时分秒，如果没有则使用默认值
          const hour = timePart.substring(0, 2) || '00';
          const minute = timePart.substring(2, 4) || '00';
          const second = timePart.substring(4, 6) || '00';

          const formattedDate = `${year}-${month}-${day} `;//${hour}:${minute}:${second}

          return {
            title: title,
            department: department,
            date: formattedDate,
            id: folderId
          };
        }
      }

      // 如果无法解析，返回原始文件夹名
      return {
        title: folderName,
        department: '未知',
        date: '未知',
        id: '未知'
      };
    }

    // 渲染文件夹列表
    function renderFolders(folders) {
      const contentArea = document.getElementById('content-area');

      if (folders.length === 0) {
        // 显示空状态
        if (currentDepartment === 'all') {
          contentArea.innerHTML = `
            <div class="empty-state">
              <div class="empty-icon"><i class="fas fa-folder-open"></i></div>
              <h3>暂无文档</h3>
              <p>知识库中还没有上传任何文档</p>
            </div>
          `;
        } else {
          contentArea.innerHTML = `
            <div class="empty-filter-result">
              <div class="empty-icon"><i class="fas fa-filter"></i></div>
              <h3>没有符合条件的文档</h3>
              <p>当前部门下没有找到文档，请尝试其他筛选条件</p>
            </div>
          `;
        }
        return;
      }

      // 创建文件夹列表
      let foldersHTML = '<div class="folder-list">';

      folders.forEach(folder => {
        const folderInfo = parseFolderName(folder.name);
        const fileListHTML = folder.files.map(file => {
          const ext = file.split('.').pop().toLowerCase();
          return `
            <li class="file-item">
              <div class="file-icon">${getFileIcon(file)}</div>
              <div class="file-name">${file}</div>
              <div class="file-ext">${ext}</div>
            </li>
          `;
        }).join('');

        foldersHTML += `
          <div class="folder-card">
            <div class="folder-header">
              <div class="folder-title">${folderInfo.title}</div>
              <div class="folder-info">
                <span class="folder-date"><i class="far fa-calendar-alt"></i> ${folderInfo.date}</span>
                <span class="folder-department">${folderInfo.department}</span>
              </div>
            </div>
            <div class="folder-content">
              <ul class="file-list">
                ${fileListHTML || '<li class="file-item">无文件</li>'}
              </ul>
            </div>
          </div>
        `;
      });

      foldersHTML += '</div>';
      contentArea.innerHTML = foldersHTML;
    }

    // 创建部门筛选器
    function createDepartmentFilters(folders) {
      // 提取所有不同的部门
      const departments = new Set();
      folders.forEach(folder => {
        const folderInfo = parseFolderName(folder.name);
        if (folderInfo.department && folderInfo.department !== '未知') {
          departments.add(folderInfo.department);
        }
      });

      // 如果没有部门信息，隐藏筛选器
      if (departments.size === 0) {
        document.querySelector('.filter-container').style.display = 'none';
        return;
      }

      // 创建部门筛选按钮
      const filterContainer = document.getElementById('department-filters');

      // 清空现有按钮，保留“全部”按钮
      const allButton = filterContainer.querySelector('button[data-department="all"]');
      filterContainer.innerHTML = '';

      // 重新创建“全部”按钮
      const newAllButton = document.createElement('button');
      newAllButton.className = 'filter-btn active';
      newAllButton.setAttribute('data-department', 'all');
      newAllButton.textContent = '全部';
      newAllButton.addEventListener('click', () => filterByDepartment('all'));
      filterContainer.appendChild(newAllButton);

      // 按字母排序部门
      const sortedDepartments = Array.from(departments).sort();

      sortedDepartments.forEach(department => {
        const btn = document.createElement('button');
        btn.className = 'filter-btn';
        btn.setAttribute('data-department', department);
        btn.textContent = department;
        btn.addEventListener('click', () => filterByDepartment(department));
        filterContainer.appendChild(btn);
      });
    }

    // 按部门筛选文件夹
    function filterByDepartment(department) {
      console.log('Filtering by department:', department);

      // 更新当前选中的部门
      currentDepartment = department;

      // 更新按钮样式
      const buttons = document.querySelectorAll('.filter-btn');
      buttons.forEach(btn => {
        if (btn.getAttribute('data-department') === department) {
          btn.classList.add('active');
        } else {
          btn.classList.remove('active');
        }
      });

      // 筛选文件夹
      let filteredFolders;
      if (department === 'all') {
        filteredFolders = allFolders;
        console.log('Showing all folders:', filteredFolders.length);
      } else {
        filteredFolders = allFolders.filter(folder => {
          const folderInfo = parseFolderName(folder.name);
          return folderInfo.department === department;
        });
        console.log('Filtered folders:', filteredFolders.length);
      }

      // 渲染筛选后的文件夹
      renderFolders(filteredFolders);
    }

    // 加载文档列表
    async function loadDocumentList() {
      const contentArea = document.getElementById('content-area');
      const loadingIndicator = document.getElementById('loading-indicator');

      // 重置当前部门为全部
      currentDepartment = 'all';

      try {
        const response = await fetch('/list_documents/');

        if (!response.ok) {
          throw new Error('获取文档列表失败');
        }

        const data = await response.json();

        // 隐藏加载指示器
        loadingIndicator.style.display = 'none';

        // 保存所有文件夹数据
        allFolders = data.folders;
        console.log('Loaded folders:', allFolders.length);

        // 创建部门筛选器
        createDepartmentFilters(allFolders);

        // 渲染所有文件夹
        renderFolders(allFolders);

      } catch (error) {
        console.error('Error:', error);
        loadingIndicator.style.display = 'none';
        contentArea.innerHTML = `
          <div class="error-message">
            <h3><i class="fas fa-exclamation-triangle"></i> 加载失败</h3>
            <p>${error.message}</p>
            <button class="refresh-btn" onclick="location.reload()">重新加载</button>
          </div>
        `;
      }
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      loadDocumentList();

      // 添加刷新按钮事件
      document.getElementById('refresh-btn').addEventListener('click', function() {
        loadDocumentList();
      });
    });
  </script>
</body>
</html>
