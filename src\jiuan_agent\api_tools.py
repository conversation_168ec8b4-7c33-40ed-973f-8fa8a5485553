"""
久安大模型API工具模块 - 包含所有API调用相关函数
"""

import os
import json
import re
import requests
import urllib3
import logging
from typing import Optional, Dict, Any, List

from .api_utils import load_api_config, get_api_url

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 创建logger
logger = logging.getLogger("jiuan_agent")

# 全局变量存储当前用户ID和会话ID
_current_user_id = ""
_current_session_id = ""

def set_user_id(user_id: str):
    """设置当前用户ID"""
    global _current_user_id
    _current_user_id = user_id

def get_user_id() -> str:
    """获取当前用户ID"""
    return _current_user_id

def set_session_id(session_id: str):
    """设置当前会话ID"""
    global _current_session_id
    _current_session_id = session_id

def get_session_id() -> str:
    """获取当前会话ID"""
    return _current_session_id

def get_api_headers(api_config: dict) -> dict:
    """获取包含用户ID和会话ID的API请求头"""
    headers = {
        "Content-Type": "application/json",
        "Token": api_config['token']
    }

    # 添加用户ID到请求头（如果存在）
    user_id = get_user_id()
    if user_id:
        headers["userId"] = user_id

    # 添加会话ID到请求头（如果存在）
    session_id = get_session_id()
    if session_id:
        headers["sessionId"] = session_id

    return headers

def query_event_surround_resource(input_param: str) -> str:
    """
    查询事件周边资源
    输入参数格式: 事件名称,距离,资源类型(可选)
    例如：西兴街道火灾事件,5,救援队伍
    """
    try:
        # 解析参数
        params = input_param.split(',')
        if len(params) < 2:
            return "参数不足，请提供事件名称和查询距离"

        event_name = params[0].strip()
        if not event_name:
            return "事件名称不能为空"

        try:
            distance = int(params[1].strip())
        except ValueError:
            return "距离必须是整数"

        resource_type = ""
        if len(params) > 2:
            resource_type = params[2].strip()

        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"

        # 调用API - 使用get_api_url函数获取完整URL
        api_url = get_api_url("default", "queryEventSurroundResource")
        if not api_url:
            return "API URL构建失败"

        headers = get_api_headers(api_config)

        payload = {
            "eventName": event_name,
            "arroundDistance": distance,
            "resourceType": resource_type
        }

        response = requests.post(api_url, json=payload, headers=headers, verify=False)

        # 记录API调用信息
        logger.info(f"调用查询事件周边资源API: {api_url}")
        logger.info(f"请求参数: {payload}")

        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")

        if response_data.get("code") == "0":
            resources = response_data.get("data", [])
            count = len(resources)

            if not resources:
                if resource_type:
                    return f"{event_name}{distance}公里范围内未找到{resource_type}资源"
                else:
                    return f"{event_name}{distance}公里范围内未找到应急资源"
            else:
                # 按类型分组
                resource_groups = {}
                for resource in resources:
                    res_type = resource.get("type", "other")
                    if res_type not in resource_groups:
                        resource_groups[res_type] = []
                    resource_groups[res_type].append(resource)

                # 类型映射表（英文到中文）
                type_mapping = {
                    "rescue_team": "救援队伍",
                    "repository": "物资仓库",
                    "medical_health": "医疗健康机构",
                    "protection_target": "保护目标",
                    "emergency_expert": "应急专家",
                    "material": "应急物资",
                    "equipment": "应急设备",
                    "common": "通用设施",
                    "enterpriseInfo": "企业信息",
                    "refuge": "避难场所",       
                    "hospital": "医院",          
                    "hazard": "危险源", 
                    "camera": "监控设备",        
                    "video_terminal": "视频监控",         
                    "monitor": "监控点位",
                    "person": "人员",
                    "guaranteeEquipment": "保障装备",
                    "communicationSupportTeam": "通信保障队伍",                          
                    "other": "其他资源"
                }

                # 格式化输出
                result = [f"{event_name}{distance}公里范围内的应急资源包括："]

                # 按类型处理资源
                for res_type in ["rescue_team", "repository", "medical_health", "hospital", 
                                "emergency_expert", "material", "equipment", "refuge", 
                                "protection_target", "dangerous_source", "hazard","camera", "video_terminal", 
                                "monitor", "enterpriseInfo", "common","person","guaranteeEquipment","communicationSupportTeam","other"]:
                    if res_type in resource_groups and resource_groups[res_type]:
                        type_name = type_mapping.get(res_type, res_type)
                        result.append(f"\n{type_name}:")

                        for item in resource_groups[res_type]:
                            name = item.get("name", "未知")
                            item_distance = item.get("distance", "未知")
                            phone = item.get("phoneNumber", "")

                            resource_info = f"- {name}"
                            if item_distance:
                                resource_info += f"，距离{item_distance}公里"
                            if phone:
                                resource_info += f"，联系电话{phone}"

                            result.append(resource_info)

                return "\n".join(result)
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询事件周边资源错误: {str(e)}")
        return f"处理查询时出错: {str(e)}"

def preview_camera(input_param: str) -> str:
    """
    预览摄像头
    输入参数格式: 摄像头ID
    例如：camera001
    """
    try:
        # 解析参数
        camera_id = input_param.strip()
        if not camera_id:
            return "摄像头ID不能为空"

        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"

        # 调用API
        api_url = get_api_url("default", "oneMapPreviewCameraByName")
        if not api_url:
            return "API URL构建失败"

        headers = get_api_headers(api_config)

        payload = {
            "cameraName": camera_id
        }

        response = requests.post(api_url, json=payload, headers=headers, verify=False)

        # 记录API调用信息
        logger.info(f"调用预览摄像头API: {api_url}")
        logger.info(f"请求参数: {payload}")

        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")

        if response_data.get("code") == "0":
            camera_data = response_data.get("data", {})
            camera_url = camera_data.get("url", "")

            if camera_url:
                # 返回开启摄像头预览成功的消息
                return f"已成功开启 {camera_id} 摄像头预览"
            elif camera_data.get("resultMsg"):
                # 如果有resultMsg字段，直接返回该信息
                return camera_data.get("resultMsg")
            else:
                return f"未能成功开启 {camera_id} 摄像头预览"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"预览失败: {error_msg}"
    except Exception as e:
        logger.error(f"预览摄像头错误: {str(e)}")
        return f"处理预览请求时出错: {str(e)}"

def one_map_position_resource(input_param: str) -> str:
    """
    一张图查看并定位资源
    输入参数格式: 资源名称
    例如：防汛抗旱物资储备库
    """
    try:
        # 解析参数
        resource_name = input_param.strip()
        if not resource_name:
            return "资源名称不能为空"

        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"

        # 调用API
        api_url = get_api_url("default", "oneMapPositionResourceByName")
        if not api_url:
            return "API URL构建失败"

        headers = get_api_headers(api_config)

        payload = {
            "resourceName": resource_name,
            "resourceType": ""
        }

        response = requests.post(api_url, json=payload, headers=headers, verify=False)

        # 记录API调用信息
        logger.info(f"调用查询资源位置API: {api_url}")
        logger.info(f"请求参数: {payload}")

        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")

        if response_data.get("code") == "0":
            position_data = response_data.get("data", {})
            # 如果有resultMsg字段，直接返回该信息
            if position_data.get("resultMsg"):
                return position_data.get("resultMsg")
            # 否则返回默认成功信息
            return f"【{resource_name}】成功定位！"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询资源位置错误: {str(e)}")
        return f"处理查询请求时出错: {str(e)}"

def event_stop_plan(input_param: str) -> str:
    """
    终止事件预案
    输入格式: 事件名称,预案名称
    例如: 西兴街道火灾事件,消防预案
    """
    try:
        # 解析参数
        params = input_param.split(',')

        event_name = params[0].strip()
        plan_name = ""
        if len(params) > 1:
            plan_name = params[1].strip()

        if not event_name:
            return "事件名称不能为空"

        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"

        # 调用API
        api_url = get_api_url("default", "stopEventPlan")
        if not api_url:
            return "API URL构建失败"

        headers = get_api_headers(api_config)

        payload = {
            "eventName": event_name,
            "planName": plan_name
        }

        response = requests.post(api_url, json=payload, headers=headers, verify=False)

        # 记录API调用信息
        logger.info(f"调用终止事件预案API: {api_url}")
        logger.info(f"请求参数: {payload}")

        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")

        if response_data.get("code") == "0":
            if plan_name:
                return f"已成功终止事件{event_name}的{plan_name}预案"
            else:
                return f"已成功终止事件{event_name}的预案"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"终止失败: {error_msg}"
    except Exception as e:
        logger.error(f"终止事件预案错误: {str(e)}")
        return f"处理终止请求时出错: {str(e)}"

def event_start_plan(input_param: str) -> str:
    """
    启动事件预案
    输入格式: 事件名称,预案名称
    例如: 西兴街道火灾事件,消防预案
    """
    try:
        # 解析参数
        params = input_param.split(',')

        event_name = params[0].strip()
        plan_name = ""
        if len(params) > 1:
            plan_name = params[1].strip()

        if not event_name:
            return "事件名称不能为空"

        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"

        # 调用API
        api_url = get_api_url("default", "startEventPlan")
        if not api_url:
            return "API URL构建失败"

        headers = get_api_headers(api_config)

        payload = {
            "eventName": event_name,
            "planName": plan_name
        }

        response = requests.post(api_url, json=payload, headers=headers, verify=False)

        # 记录API调用信息
        logger.info(f"调用启动事件预案API: {api_url}")
        logger.info(f"请求参数: {payload}")

        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")

        if response_data.get("code") == "0":
            if plan_name:
                return f"已成功启动事件{event_name}的{plan_name}预案"
            else:
                return f"已成功启动事件{event_name}的预案"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"启动失败: {error_msg}"
    except Exception as e:
        logger.error(f"启动事件预案错误: {str(e)}")
        return f"处理启动请求时出错: {str(e)}"

def query_area_plans(input_param: str) -> str:
    """
    根据区域名称查询预案信息
    输入格式: 区域名称（市/区）
    例如: 杭州市
    """
    try:
        # 解析参数
        area_name = input_param.strip()
        if not area_name:
            return "区域名称不能为空"

        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"

        # 调用API
        api_url = get_api_url("plan", "queryPlanInfosByAreaName")
        if not api_url:
            return "API URL构建失败"

        headers = get_api_headers(api_config)

        payload = {
            "areaName": area_name
        }

        response = requests.post(api_url, json=payload, headers=headers, verify=False)

        # 记录API调用信息
        logger.info(f"调用查询区域预案API: {api_url}")
        logger.info(f"请求参数: {payload}")

        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")

        if response_data.get("code") == "0":
            plans = response_data.get("data", [])

            if not plans:
                return f"{area_name}未找到相关预案"

            # 格式化输出
            result = [f"{area_name}的应急预案包括：\n"]

            for i, plan in enumerate(plans, 1):
                plan_name = plan.get("planName", "未知预案")
                plan_type = plan.get("planType", "未知类型")

                result.append(f"{i}. 预案名称：{plan_name}")
                result.append(f"- 预案类型：{plan_type}")
                result.append("")  # 添加空行分隔不同预案

            return "\n".join(result).strip()
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询区域预案错误: {str(e)}")
        return f"处理查询请求时出错: {str(e)}"

def query_resource_cameras(input_param: str) -> str:
    """
    查询资源周边的视频信息
    输入格式: 资源名称,资源类型
    例如: 防汛物资仓库,repository
    
    资源类型对照:
    - protection_target: 防护目标
    - rescue_team: 救援队伍
    - repository: 应急仓库
    - enterpriseInfo: 企业
    - person: 人员
    """
    try:
        # 解析参数
        params = input_param.split(',')
        if len(params) < 2:
            return "参数不足，请提供资源名称和资源类型"
        
        resource_name = params[0].strip()
        resource_type = params[1].strip()
        
        if not resource_name:
            return "资源名称不能为空"
        
        # 验证资源类型的有效性
        valid_resource_types = {
            "protection_target", "rescue_team", "repository", 
            "enterpriseInfo", "person","refuge", "medical_health"
        }
        
        if resource_type not in valid_resource_types:
            return f"资源类型 '{resource_type}' 无效，有效类型包括: protection_target(防护目标), rescue_team(救援队伍), repository(应急仓库), enterpriseInfo(企业), person(人员), refuge(避难场所), medical_health(医疗卫生)"  
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "queryResourceArroundCameraInfos")
        if not api_url:
            return "API URL构建失败"
            
        headers = get_api_headers(api_config)

        payload = {
            "resourceName": resource_name,
            "resourceType": resource_type
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用查询资源周边视频信息API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            cameras = response_data.get("data", [])
            total = len(cameras)
            
            if not cameras:
                return f"在{resource_name}周边未找到视频监控点"
            
            # 资源类型映射（英文到中文）
            resource_type_map = {
                "protection_target": "防护目标",
                "rescue_team": "救援队伍",
                "repository": "应急仓库",
                "enterpriseInfo": "企业",
                "person": "人员",
                "refuge":"避难场所",
                "medical_health":"医疗卫生"
            }
            
            resource_type_zh = resource_type_map.get(resource_type, resource_type)
            
            # 格式化输出
            result = [f"已找到{resource_type_zh}{resource_name}周边的{total}个监控点，并已打开所有监控画面。"]
            
            for i, camera in enumerate(cameras, 1):
                camera_name = camera.get("name", "未知监控点")
                camera_id = camera.get("indexCode", "")
                
                result.append(f"{i}. {camera_name}")
                if camera_id:
                    result.append(f"   ID: {camera_id}")
            
            return "\n".join(result)
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询资源周边视频错误: {str(e)}")
        return f"处理查询请求时出错: {str(e)}"

def start_call_by_name(input_param: str) -> str:
    """
    根据队伍/人名发起指定的呼叫
    输入格式: 资源名称,资源类型,呼叫类型
    例如: 李四,person,0 (视频通话)
    """
    try:
        # 解析参数
        params = input_param.split(',')
        if len(params) < 3:
            return "参数不足，请提供资源名称、资源类型和呼叫类型"

        resource_name = params[0].strip()
        resource_type = params[1].strip()
        calling_type = params[2].strip()

        if not resource_name or not resource_type or not calling_type:
            return "资源名称、资源类型和呼叫类型不能为空"

        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"

        # 调用API
        api_url = get_api_url("default", "startVideoOrPhoneCallByName")
        if not api_url:
            return "API URL构建失败"

        headers = get_api_headers(api_config)

        payload = {
            "resourceName": resource_name,
            "resourceType": resource_type,
            "callingType": calling_type
        }

        response = requests.post(api_url, json=payload, headers=headers, verify=False)

        # 记录API调用信息
        logger.info(f"调用发起呼叫API: {api_url}")
        logger.info(f"请求参数: {payload}")

        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")

        if response_data.get("code") == "0":
            call_type_name = "视频通话" if calling_type == "0" else "电话通话"
            return f"已成功向{resource_name}发起{call_type_name}"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"发起呼叫失败: {error_msg}"
    except Exception as e:
        logger.error(f"发起呼叫错误: {str(e)}")
        return f"处理呼叫请求时出错: {str(e)}"

# 查询所有事件信息的完整实现
def query_all_event_infos(input_param: str) -> str:
    """
    查询所有事件信息
    输入格式: 页码,页面大小,事件状态,事件名称,事件级别
    例如: 1,50,,,
    """
    try:
        # 解析参数
        params = input_param.split(',')
        
        # 默认参数
        page_no = "1"
        page_size = "50"
        event_status = ""
        event_name = ""
        event_level = ""
        
        # 根据提供的参数个数设置对应的值
        if len(params) > 0 and params[0].strip():
            page_no = params[0].strip()
        
        if len(params) > 1 and params[1].strip():
            page_size = params[1].strip()
            
        if len(params) > 2 and params[2].strip():
            event_status = params[2].strip()
            
        if len(params) > 3 and params[3].strip():
            event_name = params[3].strip()
            
        if len(params) > 4 and params[4].strip():
            event_level = params[4].strip()
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "queryAllEventInfos")
        if not api_url:
            return "API URL构建失败"
            
        headers = get_api_headers(api_config)

        payload = {
            "pageNo": page_no,
            "pageSize": page_size,
            "eventStatus": event_status,
            "eventName": event_name,
            "eventLevel": event_level
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用查询所有事件信息API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            data = response_data.get("data", {})
            rows = data.get("rows", [])
            total = data.get("total", 0)
            
            if not rows:
                return "未找到符合条件的事件"
            
            # 格式化输出
            result = [f"当前系统中共有{total}个事件，以下是事件信息：\n"]
            
            for i, event in enumerate(rows, 1):
                event_name = event.get("eventName", "未知事件")
                event_type = event.get("eventType", "未知类型")
                event_status = event.get("eventStatus", "未知状态")
                event_level = event.get("eventLevel", "未知级别")
                location = event.get("location", "未知位置")
                
                # 添加事件信息
                result.append(f"{i}. 事件名称：{event_name}")
                result.append(f"- 事件类型：{event_type}")
                result.append(f"- 事件状态：{event_status}")
                result.append(f"- 事件级别：{event_level}")
                result.append(f"- 事件位置：{location}")
                result.append("")  # 添加空行分隔不同事件
            
            return "\n".join(result).strip()
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询所有事件信息错误: {str(e)}")
        return f"处理查询请求时出错: {str(e)}"

def search_emergency_plan(input_param: str) -> str:
    """
    搜索应急预案
    输入格式: 预案类型（为空时查所有预案），页码(可选，默认为1),页面大小(可选，默认20)
    例如: 森林火灾,1,20
    """
    try:
        # 解析参数
        params = input_param.split(',')
        
        # 默认参数
        plan_type_name = ""
        page_no = "1"
        page_size = "120"
        
        # 根据提供的参数个数设置对应的值
        if len(params) > 0 and params[0].strip():
            plan_type_name = params[0].strip()
        
        if len(params) > 1 and params[1].strip():
            page_no = params[1].strip()
        
        if len(params) > 2 and params[2].strip():
            page_size = params[2].strip()
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API - 使用plan类型的API URL
        api_url = get_api_url("plan", "queryPlanInfo")
        if not api_url:
            return "API URL构建失败"
            
        headers = get_api_headers(api_config)

        payload = {
            "planTypeName": plan_type_name,
            "pageNo": page_no,
            "pageSize": page_size
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用搜索应急预案API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            data = response_data.get("data", {})
            rows = data.get("rows", [])
            total = data.get("total", 0)
            
            if not rows:
                return "未找到符合条件的应急预案"
            
            # 格式化输出
            result = [f"当前系统中共有{total}个预案，以下是所有预案的信息：\n"]
            
            for i, plan in enumerate(rows, 1):
                plan_name = plan.get("planName", "未知预案")
                plan_type = plan.get("planType", "未知类型")
                plan_status = plan.get("examineStatus", "未知状态")
                plan_tag = plan.get("planTagDetailName", "无标签")
                
                # 添加预案信息
                result.append(f"{i}. 预案名称：{plan_name}")
                result.append(f"- 预案类型：{plan_type}")
                result.append(f"- 预案状态：{plan_status}")
                result.append(f"- 预案标签：{plan_tag}")
                result.append("")  # 添加空行分隔不同预案
            
            return "\n".join(result).strip()
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"搜索应急预案错误: {str(e)}")
        return f"处理搜索请求时出错: {str(e)}"

def query_model_resources(input_param: str) -> str:
    """
    按照事件模型名称搜索附近应急资源
    输入格式: 模型名称
    例如: 火灾模型
    """
    try:
        # 解析参数
        model_name = input_param.strip()
        if not model_name:
            return "模型名称不能为空"
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "queryArroundResourceByModelName")
        if not api_url:
            return "API URL构建失败"
            
        headers = get_api_headers(api_config)

        payload = {
            "modelName": model_name
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用按模型查询资源API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            resources = response_data.get("data", [])
            
            if not resources:
                return f"未找到基于{model_name}的附近应急资源"
            
            # 格式化输出
            result = [f"基于{model_name}找到以下应急资源：\n"]
            
            for i, resource in enumerate(resources, 1):
                resource_name = resource.get("resourceName", "未知资源")
                resource_type = resource.get("resourceType", "未知类型")
                distance = resource.get("distance", "未知距离")
                
                result.append(f"{i}. 资源名称：{resource_name}")
                result.append(f"- 资源类型：{resource_type}")
                result.append(f"- 距离：{distance}")
                result.append("")  # 添加空行分隔不同资源
            
            return "\n".join(result).strip()
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"按模型查询资源错误: {str(e)}")
        return f"处理查询请求时出错: {str(e)}"

def start_real_time_travel(input_param: str = "") -> str:
    """
    启动一键调度跟进所有救援队伍实时轨迹
    不需要输入参数
    """
    try:
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "realTimeTravel")
        if not api_url:
            return "API URL构建失败"
            
        headers = get_api_headers(api_config)

        payload = {}  # 空参数
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用启动实时轨迹API: {api_url}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            return "已成功启动一键调度，正在跟进所有救援队伍的实时轨迹"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"启动失败: {error_msg}"
    except Exception as e:
        logger.error(f"启动实时轨迹错误: {str(e)}")
        return f"处理启动请求时出错: {str(e)}"

def start_meeting(input_param: str) -> str:
    """
    开启会议
    输入格式: 会议名称
    例如: 应急指挥会议
    """
    try:
        # 解析参数
        meeting_name = input_param.strip()
        if not meeting_name:
            return "会议名称不能为空"
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
         # 调用API:startMeetingByName、startPlanMeetingByPlanGroupName
        api_url = get_api_url("default", "startPlanMeetingByPlanGroupName")
        if not api_url:
            return "API URL构建失败"
            
        headers = get_api_headers(api_config)

        payload = {
            "meetingName": meeting_name
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用开启会议API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            return f"已成功开启'{meeting_name}'会议"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"开启会议失败: {error_msg}"
    except Exception as e:
        logger.error(f"开启会议错误: {str(e)}")
        return f"处理开启会议请求时出错: {str(e)}"

def end_meeting(input_param: str) -> str:
    """
    结束会议
    输入格式: 会议名称
    例如: 应急指挥会议
    """
    try:
        # 解析参数
        meeting_name = input_param.strip()
        if not meeting_name:
            return "会议名称不能为空"
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API：endMeetingByName、endPlanMeetingByPlanGroupName
        api_url = get_api_url("default", "endPlanMeetingByPlanGroupName")
        if not api_url:
            return "API URL构建失败"
            
        headers = get_api_headers(api_config)

        payload = {
            "meetingName": meeting_name
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用结束会议API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            return f"已成功结束'{meeting_name}'会议"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"结束会议失败: {error_msg}"
    except Exception as e:
        logger.error(f"结束会议错误: {str(e)}")
        return f"处理结束会议请求时出错: {str(e)}"
