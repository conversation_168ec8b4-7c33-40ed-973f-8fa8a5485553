{"api_key_header": {"method": "API密钥认证（请求头）", "status_code": null, "success": false, "error": "HTTPConnectionPool(host='***********', port=8089): Max retries exceeded with url: /api/asr/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A4328AE110>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "api_key_query": {"method": "API密钥认证（查询参数）", "status_code": null, "success": false, "error": "HTTPConnectionPool(host='***********', port=8089): Max retries exceeded with url: /api/asr/status?api_key=asr-api-key-2025 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A4328AE500>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "token_auth": {"method": "Token认证", "status_code": null, "success": false, "error": "HTTPConnectionPool(host='***********', port=8089): Max retries exceeded with url: /api/asr/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A4328AE1A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "no_auth": {"method": "无认证", "status_code": null, "success": false, "error": "HTTPConnectionPool(host='***********', port=8089): Max retries exceeded with url: /api/asr/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A4328AD6F0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "wrong_api_key": {"method": "错误API密钥", "status_code": null, "success": false, "error": "HTTPConnectionPool(host='***********', port=8089): Max retries exceeded with url: /api/asr/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A4328AE860>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "multiple_interfaces": {"/api/asr/status": {"status_code": null, "success": false, "error": "HTTPConnectionPool(host='***********', port=8089): Max retries exceeded with url: /api/asr/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A4328AEBC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "/api/asr/tasks": {"status_code": null, "success": false, "error": "HTTPConnectionPool(host='***********', port=8089): Max retries exceeded with url: /api/asr/tasks (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A4328AEEF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "/api/asr/websocket/url?mode=single": {"status_code": null, "success": false, "error": "HTTPConnectionPool(host='***********', port=8089): Max retries exceeded with url: /api/asr/websocket/url?mode=single (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001A4328AF3D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}}}