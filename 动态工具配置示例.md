# 动态工具配置示例

## 前端配置界面使用指南

### 1. 基本工具配置

#### 示例1：查询天气工具
```
工具名称（中文）: 天气查询
工具名称（英文）: weather_query
描述: 查询指定城市的天气信息
POST路径: /api/weather/query
关键词组合: 天气,查询;天气,预报;查看,天气
```

**关键词组合说明：**
- `天气,查询` - 用户说"查询天气"时触发
- `天气,预报` - 用户说"天气预报"时触发  
- `查看,天气` - 用户说"查看天气"时触发

#### 示例2：资源搜索工具
```
工具名称（中文）: 资源搜索
工具名称（英文）: resource_search
描述: 搜索指定类型的应急资源
POST路径: /api/resource/search
关键词组合: 搜索,资源;查找,资源;资源,查询;找,资源
```

### 2. 参数配置示例

#### 请求参数配置
```json
{
  "city": {
    "type": "string",
    "description": "城市名称",
    "default": null
  },
  "days": {
    "type": "int", 
    "description": "预报天数",
    "default": 3
  }
}
```

#### 返回参数配置
```json
{
  "temperature": {
    "description": "温度"
  },
  "weather": {
    "description": "天气状况"
  },
  "humidity": {
    "description": "湿度"
  }
}
```

## 关键词组合配置详解

### 格式说明
```
单个关键词: 天气
词组合: 天气,查询
多个组合: 天气,查询;天气,预报;查看,天气
```

### 匹配逻辑
1. **单个关键词**: 用户输入包含该词即触发
2. **词组合**: 用户输入必须同时包含组合内所有词才触发
3. **多个组合**: 满足任意一个组合即可触发

### 实际配置示例

#### 事件管理工具
```
关键词组合: 事件,查询;事件,搜索;查找,事件;事件,列表
```
匹配的用户输入：
- ✅ "查询最近的事件"
- ✅ "搜索火灾事件" 
- ✅ "查找交通事件"
- ✅ "显示事件列表"
- ❌ "查询天气" (只包含"查询"，不包含"事件")

#### 资源定位工具
```
关键词组合: 一张图,查看;一张图,定位;地图,查看;地图,定位
```
匹配的用户输入：
- ✅ "一张图查看资源位置"
- ✅ "在地图上定位医院"
- ❌ "查看资源信息" (不包含"一张图"或"地图")

#### 会议管理工具
```
关键词组合: 开启,会议;启动,会议;开始,会议;结束,会议;关闭,会议
```
匹配的用户输入：
- ✅ "开启应急会议"
- ✅ "结束当前会议"
- ❌ "会议室在哪里" (只包含"会议"，不包含动作词)

## 最佳实践建议

### 1. 关键词选择原则
- **具体性**: 选择具体、明确的关键词
- **唯一性**: 避免与其他工具的关键词冲突
- **自然性**: 符合用户的自然表达习惯

### 2. 组合设计技巧
- **动词+名词**: 如"查询,事件"、"启动,会议"
- **修饰词+核心词**: 如"一张图,查看"、"实时,轨迹"
- **同义词覆盖**: 如"查询;搜索;查找"

### 3. 避免常见问题
- **过于宽泛**: 避免使用"查询"、"获取"等过于通用的单词
- **过于严格**: 不要设置过多必需词，可能导致无法触发
- **冲突重叠**: 确保不同工具的关键词组合不会相互冲突

## 测试验证

### 配置完成后的测试步骤
1. 在前端界面保存工具配置
2. 使用测试脚本验证关键词匹配
3. 在实际对话中测试工具触发
4. 根据测试结果调整关键词组合

### 测试用例模板
```python
test_cases = [
    ("你的关键词组合", "用户输入示例", True/False),
    ("事件,查询", "查询最近的事件", True),
    ("事件,查询", "查询天气情况", False),
]
```

这样的配置方式既保证了触发的准确性，又提供了足够的灵活性来适应不同的用户表达习惯。
