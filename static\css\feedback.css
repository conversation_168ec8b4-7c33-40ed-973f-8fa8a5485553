/* 反馈区域样式 */
.message-feedback {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.feedback-question {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.feedback-buttons {
  display: flex;
  gap: 10px;
}

.feedback-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.feedback-btn:hover {
  background-color: #f5f5f5;
}

.feedback-btn.selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.thumbs-up.selected {
  background-color: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
}

.thumbs-down.selected {
  background-color: #fff2f0;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.feedback-comment {
  margin-top: 10px;
}

.feedback-comment textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.submit-feedback-btn {
  margin-top: 8px;
  padding: 5px 12px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.submit-feedback-btn:hover {
  background-color: #40a9ff;
}

.feedback-thanks {
  padding: 10px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  color: #52c41a;
  text-align: center;
}
