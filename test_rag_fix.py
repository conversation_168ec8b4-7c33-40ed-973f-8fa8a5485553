#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RAG修复后的多轮对话功能
"""

import asyncio
import json
from src.privateGPT_res import search_local_information_stream, stream_chat

async def test_rag_with_history():
    """测试RAG模式的多轮对话"""
    print("=== 测试RAG模式多轮对话 ===")
    
    # 模拟历史对话
    history = [
        {"role": "user", "content": "什么是人工智能？"},
        {"role": "assistant", "content": "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"}
    ]
    
    # 当前问题
    current_query = "它有哪些应用领域？"
    
    print(f"历史对话: {len(history)} 条消息")
    print(f"当前问题: {current_query}")
    print("RAG回答:")
    
    try:
        async for token in search_local_information_stream(current_query, history=history):
            print(token, end='', flush=True)
        print("\n")
    except Exception as e:
        print(f"RAG测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_rag_without_history():
    """测试RAG模式的单轮对话"""
    print("=== 测试RAG模式单轮对话 ===")
    
    # 当前问题
    current_query = "你好"
    
    print(f"当前问题: {current_query}")
    print("RAG回答:")
    
    try:
        async for token in search_local_information_stream(current_query):
            print(token, end='', flush=True)
        print("\n")
    except Exception as e:
        print(f"RAG单轮测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_chat_with_history():
    """测试普通对话模式的多轮对话"""
    print("=== 测试普通对话模式多轮对话 ===")
    
    # 模拟历史对话
    history = [
        {"role": "user", "content": "你好，我想了解一下Python编程"},
        {"role": "assistant", "content": "你好！Python是一种高级编程语言，以其简洁易读的语法而闻名。"}
    ]
    
    # 当前问题
    current_query = "它适合初学者学习吗？"
    
    print(f"历史对话: {len(history)} 条消息")
    print(f"当前问题: {current_query}")
    print("普通对话回答:")
    
    try:
        async for token in stream_chat(current_query, history=history):
            print(token, end='', flush=True)
        print("\n")
    except Exception as e:
        print(f"普通对话测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_request_format():
    """测试前端请求格式"""
    print("=== 测试前端请求格式 ===")
    
    # 模拟前端发送的请求数据
    request_data = {
        "query": "这个问题的答案是什么？",
        "enable_rag": True,
        "history": [
            {"role": "user", "content": "什么是机器学习？"},
            {"role": "assistant", "content": "机器学习是人工智能的一个子领域。"}
        ]
    }
    
    print("请求数据格式:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    # 验证数据解析
    query = request_data.get("query", "")
    enable_rag = request_data.get("enable_rag", True)
    history = request_data.get("history", [])
    
    print(f"\n解析结果:")
    print(f"查询: {query}")
    print(f"启用RAG: {enable_rag}")
    print(f"历史消息数量: {len(history)}")

async def main():
    """主测试函数"""
    print("开始RAG修复后的多轮对话功能测试...\n")
    
    # 测试请求格式
    test_request_format()
    print()
    
    # 测试RAG单轮对话
    await test_rag_without_history()
    
    # 测试普通对话多轮
    await test_chat_with_history()
    
    # 测试RAG多轮对话
    await test_rag_with_history()
    
    print("测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
