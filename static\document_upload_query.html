<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>文档提问</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <link rel="stylesheet" href="/static/css/input-fix.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <style>
    .document-analysis-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
    }
    .document-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      border-bottom: 1px solid #eaeaea;
    }
    .header-left {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    .header-right {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    .back-btn {
      background-color: transparent;
      border: none;
      color: #666;
      cursor: pointer;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 5px;
    }
    .back-btn:hover {
      color: #333;
    }
    .upload-btn-container {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .upload-button {
      background-color: #4e6ef2;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 14px;
    }
    .upload-button:hover {
      background-color: #3a5ae8;
    }
    .file-types {
      font-size: 12px;
      color: #888;
      margin-left: 10px;
    }
    .file-error {
      color: #ff5252;
      font-size: 14px;
      position: absolute;
      top: 55px;
      right: 20px;
      background-color: #ffeeee;
      padding: 5px 10px;
      border-radius: 4px;
      border: 1px solid #ffcccc;
      display: none;
      z-index: 1000;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .files-section {
      display: flex;
      align-items: center;
    }
    .files-header {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      white-space: nowrap;
    }
    .files-content {
      display: none;
      position: absolute;
      top: 50px;
      right: 20px;
      width: 300px;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 5px;
      background-color: white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      z-index: 100;
    }
    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    .file-item:last-child {
      border-bottom: none;
    }
    .file-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 13px;
    }
    .remove-file-btn {
      background: none;
      border: none;
      color: #ff5252;
      cursor: pointer;
      font-size: 16px;
      padding: 0 5px;
    }
    /* 使用全局CSS中的思考过程样式 */
    .files-toggle-button {
      background: none;
      border: none;
      font-size: 14px;
      transition: transform 0.3s;
      padding: 0 5px;
      margin-left: 5px;
    }
    .files-toggle-button.expanded {
      transform: rotate(90deg);
    }
    .welcome-text {
      font-size: 14px;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="/static/images/yingji.png" alt="应急指挥产品" class="logo-icon">
          <span class="logo-title">应急指挥智能体</span>
        </div>
      </div>
      <button class="new-chat-btn" id="new-chat-btn">
        <i class="fas fa-plus"></i>
        <span>新建会话</span>
      </button>
      <div class="history-title"></div>
      <div class="history-list" id="history-list">
        <!-- 历史记录将通过JavaScript动态加载 -->
      </div>
    </div>

    <!-- 文档分析主内容区 -->
    <div class="main-content">
      <div class="document-analysis-container">
        <div class="document-header">
          <div class="header-left">
            <button class="back-btn" id="back-btn">
              <i class="fas fa-arrow-left"></i>
              <span>返回主页</span>
            </button>

            <div class="upload-btn-container">
              <button class="upload-button" id="upload-button" title="支持: .pdf, .txt, .csv, .md, .docx (最多5个)">
                <i class="fas fa-cloud-upload-alt"></i>
                <span>上传文档</span>
              </button>
              <input type="file" id="file-upload" accept=".pdf,.txt,.csv,.md,.docx" multiple style="display: none;">
            </div>

            <div class="files-section" id="files-section" style="display: none;">
              <div class="files-header" id="files-header">
                <span>已上传文件 (<span id="file-count">0</span>)</span>
                <button class="files-toggle-button" id="files-toggle-button">▶</button>
              </div>
              <div class="files-content" id="files-content" style="display: block;">
                <!-- 已选择的文件将在这里显示 -->
              </div>
            </div>
          </div>

          <div class="header-right">
            <span class="file-error" id="file-error"></span>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-container">
          <div class="chat-history" id="chat-history">
            <!-- 聊天记录将通过JavaScript动态加载 -->
          </div>
          <div class="input-container">
            <textarea class="message-input" id="message-input" placeholder="输入问题..." rows="1"></textarea>
            <button class="send-btn" id="send-btn">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化marked选项
      marked.setOptions({
        breaks: true,  // 支持GitHub风格的换行
        gfm: true      // 支持GitHub风格的Markdown
      });

      // 返回主页按钮
      document.getElementById('back-btn').addEventListener('click', function() {
        window.location.href = '/';
      });

      // 新建会话按钮
      document.getElementById('new-chat-btn').addEventListener('click', function() {
        clearChatHistory();
        clearSelectedFiles();
      });

      // 文件上传相关
      const uploadButton = document.getElementById('upload-button');
      const fileUpload = document.getElementById('file-upload');
      const filesSection = document.getElementById('files-section');
      const filesHeader = document.getElementById('files-header');
      const filesContent = document.getElementById('files-content');
      const fileCount = document.getElementById('file-count');
      const filesToggleButton = document.getElementById('files-toggle-button');
      const fileError = document.getElementById('file-error');
      let uploadedFiles = []; // 存储已上传的文件

      // 点击上传按钮触发文件选择
      uploadButton.addEventListener('click', function() {
        fileUpload.click();
      });

      // 文件列表折叠/展开功能
      filesHeader.addEventListener('click', function() {
        if (filesContent.style.display === 'block') {
          filesContent.style.display = 'none';
          filesToggleButton.classList.remove('expanded');
        } else {
          filesContent.style.display = 'block';
          filesToggleButton.classList.add('expanded');
        }
      });

      // 文件选择处理
      fileUpload.addEventListener('change', function() {
        handleFileSelection(this.files);
        this.value = ''; // 清空文件输入，允许重复选择相同文件
      });

      // 处理文件选择
      function handleFileSelection(files) {
        // 检查文件数量限制
        if (uploadedFiles.length + files.length > 5) {
          showFileError('最多只能上传5个文件');
          return;
        }

        // 处理每个文件
        for (let i = 0; i < files.length; i++) {
          const file = files[i];

          // 检查文件类型
          const fileExt = file.name.split('.').pop().toLowerCase();
          const allowedTypes = ['pdf', 'txt', 'csv', 'md', 'docx'];

          if (!allowedTypes.includes(fileExt)) {
            showFileError(`不支持的文件类型: ${file.name}`);
            continue;
          }

          // 添加到已上传文件列表
          uploadedFiles.push(file);
        }

        // 更新文件列表
        refreshFileList();

        // 显示文件区域
        if (uploadedFiles.length > 0) {
          filesSection.style.display = 'block';
          // 默认展开文件列表
          filesContent.style.display = 'block';
          filesToggleButton.classList.add('expanded');
          // 更新文件计数
          fileCount.textContent = uploadedFiles.length;
        }
      }

      // 显示文件错误
      function showFileError(message) {
        fileError.textContent = message;
        fileError.style.display = 'block';

        // 3秒后自动隐藏错误
        setTimeout(() => {
          fileError.style.display = 'none';
        }, 3000);

        // 点击错误消息可以关闭
        fileError.addEventListener('click', function() {
          this.style.display = 'none';
        });
      }

      // 添加文件到列表显示
      function addFileToList(file, index) {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
          <div class="file-name" title="${file.name}">${file.name}</div>
          <button class="remove-file-btn" data-index="${index}">×</button>
        `;
        filesContent.appendChild(fileItem);

        // 添加删除按钮事件
        fileItem.querySelector('.remove-file-btn').addEventListener('click', function(e) {
          e.stopPropagation(); // 防止点击事件传递到文件头部
          const index = parseInt(this.getAttribute('data-index'));
          removeFile(index);
        });
      }

      // 移除文件
      function removeFile(index) {
        uploadedFiles.splice(index, 1);
        refreshFileList();

        // 更新文件计数
        fileCount.textContent = uploadedFiles.length;

        // 如果没有文件了，隐藏文件区域
        if (uploadedFiles.length === 0) {
          filesSection.style.display = 'none';
        }
      }

      // 刷新文件列表显示
      function refreshFileList() {
        filesContent.innerHTML = '';
        uploadedFiles.forEach((file, index) => {
          addFileToList(file, index);
        });
      }

      // 清空已选择的文件
      function clearSelectedFiles() {
        uploadedFiles = [];
        filesContent.innerHTML = '';
        filesSection.style.display = 'none';
        fileCount.textContent = '0';
      }

      // 发送按钮和回车键发送
      document.getElementById('send-btn').addEventListener('click', sendMessage);
      document.getElementById('message-input').addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });

      // 清空聊天记录
      function clearChatHistory() {
        const chatHistory = document.getElementById('chat-history');
        chatHistory.innerHTML = '';
      }

      // 发送消息函数
      function sendMessage() {
        const messageInput = document.getElementById('message-input');
        const message = messageInput.value.trim();

        if (message) {
          // 检查是否有上传的文件
          if (uploadedFiles.length === 0) {
            showFileError('请先上传至少一个文件再提问');
            return;
          }

          // 清空输入框
          messageInput.value = '';

          // 添加用户消息到聊天历史
          const chatHistory = document.getElementById('chat-history');
          const userMsg = document.createElement('div');
          userMsg.className = 'message user-message';
          userMsg.innerHTML = `
            <div class="message-avatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="message-content">${message}</div>
          `;
          chatHistory.appendChild(userMsg);

          // 添加AI消息占位符
          const aiMsg = document.createElement('div');
          aiMsg.className = 'message ai-message';

          // 创建消息内容容器
          const contentContainer = document.createElement('div');
          contentContainer.className = 'message-content-container';

          // 创建思考过程区域
          const thinkingDiv = document.createElement('div');
          thinkingDiv.className = 'thinking-process collapsible';
          thinkingDiv.innerHTML = `
            <div class="collapsible-header">
              <span>思考过程</span>
              <button class="toggle-button">▶</button>
            </div>
            <div class="collapsible-content">
              <div class="thinking-content">思考中...</div>
            </div>
          `;

          // 创建正文内容区域
          const messageContent = document.createElement('div');
          messageContent.className = 'message-content';
          messageContent.innerHTML = `
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          `;

          // 添加头像和内容容器
          aiMsg.innerHTML = `
            <div class="message-avatar">
              <img src="/static/images/yingji.png" alt="AI">
            </div>
          `;

          // 按顺序添加元素：思考过程 -> 消息内容
          contentContainer.appendChild(thinkingDiv);
          contentContainer.appendChild(messageContent);

          // 将内容容器添加到AI消息中
          aiMsg.appendChild(contentContainer);

          // 将AI消息添加到聊天历史
          chatHistory.appendChild(aiMsg);
          chatHistory.scrollTop = chatHistory.scrollHeight;

          // 设置思考过程的可折叠功能
          thinkingDiv.querySelector('.collapsible-header').addEventListener('click', function() {
            const content = this.nextElementSibling;
            const toggleButton = this.querySelector('.toggle-button');

            if (content.style.display === 'block') {
              content.style.display = 'none';
              toggleButton.classList.remove('expanded');
            } else {
              content.style.display = 'block';
              toggleButton.classList.add('expanded');
            }
          });

          // 准备文件数据
          const formData = new FormData();
          const fileList = [];
          uploadedFiles.forEach(file => {
            fileList.push(file.name);
          });

          // 调用API获取回答
          analyzeDocuments(message, fileList, aiMsg);
        }
      }

      // 调用文档分析API
      async function analyzeDocuments(query, fileList, aiMsgElement) {
        // 记录开始时间，用于计算思考时间
        const startTime = new Date();

        try {
          // 准备上传文件
          const formData = new FormData();
          uploadedFiles.forEach(file => {
            formData.append('files', file);
          });

          // 先上传文件
          const uploadResponse = await fetch('/temp_document_upload/', {
            method: 'POST',
            body: formData
          });

          if (!uploadResponse.ok) {
            throw new Error('文件上传失败');
          }

          const uploadResult = await uploadResponse.json();

          if (uploadResult.status !== 'success') {
            throw new Error(uploadResult.message || '文件上传失败');
          }

          // 然后发送分析请求
          const response = await fetch('/analyze_documents/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: query,
              directory_path: uploadResult.temp_directory,
              file_list: fileList
            })
          });

          if (!response.ok) {
            throw new Error('网络请求失败');
          }

          // 读取流式响应
          const reader = response.body.getReader();
          const decoder = new TextDecoder();
          let result = '';

          // 获取消息内容元素和思考内容元素
          const messageContent = aiMsgElement.querySelector('.message-content');
          const thinkingContent = aiMsgElement.querySelector('.thinking-content');
          const thinkingDiv = aiMsgElement.querySelector('.thinking-process');

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const text = decoder.decode(value);
            result += text;

            // 检查是否包含思考过程
            const thinkMatch = result.match(/<think>([\s\S]*?)<\/think>/);
            if (thinkMatch) {
              const thinking = thinkMatch[1].trim();
              // 更新思考过程内容
              thinkingContent.innerHTML = marked.parse(thinking);
              // 确保完全移除思考过程标记及其内容
              result = result.replace(/<think>[\s\S]*?<\/think>/, '').trim();
            }

            // 更新AI消息内容
            messageContent.innerHTML = marked.parse(result);

            // 滚动到底部
            const chatHistory = document.getElementById('chat-history');
            chatHistory.scrollTop = chatHistory.scrollHeight;
          }

          // 记录结束时间，计算思考时间
          const endTime = new Date();
          const thinkingTime = Math.round((endTime - startTime) / 1000);

          // 更新思考过程标题，显示思考时间
          const thinkingHeader = thinkingDiv.querySelector('.collapsible-header span:first-child');
          if (thinkingHeader) {
            thinkingHeader.textContent = `思考了 ${thinkingTime}s`;
          }

        } catch (error) {
          console.error('获取回答时出错:', error);
          aiMsgElement.querySelector('.message-content').textContent = '获取回答时出错，请稍后重试';
        }
      }
    });
  </script>
</body>
</html>
