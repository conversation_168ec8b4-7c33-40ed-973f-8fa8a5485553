# Gemini Project Context: RAG and AI Agent System

This document provides a comprehensive overview of the RAG (Retrieval-Augmented Generation) and AI Agent system. It is intended to be used as a context file for Gemini to understand the project's structure, purpose, and key functionalities.

## 1. Project Overview

This is a sophisticated Python-based AI system that combines several advanced features to create a powerful conversational AI and data processing platform. At its core, it is a Retrieval-Augmented Generation (RAG) system, but it extends far beyond simple document Q&A.

The key capabilities include:

*   **Multi-Modal Interaction:** Supports text, voice (Speech-to-Text and Text-to-Speech), and image inputs.
*   **Intelligent Agent Framework:** Utilizes an agent-based architecture (similar to LangChain Agents) to intelligently decide when to use external tools or APIs versus answering from a knowledge base.
*   **Dynamic, Configurable API Tools:** A standout feature is the ability for administrators to add, configure, and manage external API tools through a web interface without touching the code. The system can then intelligently call these APIs based on user queries.
*   **Dual-Mode Chat:**
    *   **RAG Mode:** Answers questions based on information retrieved from a vector database populated with ingested documents.
    *   **Chat Mode:** A direct, "闲聊" (small talk) mode that interacts with the LLM without knowledge retrieval, for more general conversation.
*   **Comprehensive User & System Management:** Includes user authentication, role-based access control (admin/user), and a web UI for system configuration.

The system is built with a modern Python stack, using **FastAPI** for the web server, **LangChain** for the AI agent framework, and **Qdrant** as the vector store. It is designed to be modular and extensible.

### Core Technologies

*   **Backend:** Python, FastAPI
*   **AI/LLM Framework:** LangChain
*   **LLM Serving:** Ollama (or OpenAI)
*   **Vector Store:** Qdrant
*   **Speech Recognition:** `faster-whisper` (or OpenAI Whisper)
*   **Text-to-Speech (TTS):** `edge-tts` or a custom Kokoro TTS engine
*   **Database (for users/tools):** SQLite (default) or PostgreSQL
*   **Frontend:** HTML, CSS, JavaScript (served as static files)

## 2. Building and Running the Project

The project is designed to be run using a shell script that handles setup and execution.

### Prerequisites

*   Python 3.10+
*   An LLM service running (e.g., Ollama with a model like `deepseek-r1:32b`)
*   A Qdrant vector store instance running.

### Running the Application

1.  **Install Dependencies:** The `start_rag.sh` script handles this automatically. To do it manually:
    ```bash
    pip install -r requirement.txt
    ```

2.  **Configure the System:**
    *   Modify `config.ini` to set up database connections, LLM endpoints (Ollama/OpenAI), Qdrant server details, and other system parameters. This is a critical step.

3.  **Start the Server:**
    *   Use the provided shell script:
        ```bash
        ./start_rag.sh
        ```
    *   Alternatively, run `uvicorn` directly (as the script does):
        ```bash
        uvicorn main:app --host 0.0.0.0 --port 8087
        ```

The server will be accessible at `http://0.0.0.0:8087` by default.

## 3. Project Structure and Key Files

*   `main.py`: The FastAPI application entry point. It defines all API endpoints, handles application startup logic, and integrates the various modules.
*   `config.ini`: The central configuration file for the entire application. This is where all service URLs, model names, feature flags, and credentials should be managed.
*   `requirement.txt`: A list of all Python dependencies for the project.
*   `start_rag.sh`: The main script for installing dependencies and launching the application.

*   `src/`: Contains the core application logic.
    *   `src/privateGPT_res.py`: The heart of the RAG and chat logic.
        *   `search_local_information_stream()`: The main RAG workflow. It decides whether to use a Function Calling agent, a standard RAG chain, or a ReAct agent.
        *   `stream_chat()`: Handles the non-RAG "闲聊" (chat) mode.
    *   `src/agent/`: The directory for the intelligent agent and dynamic tool management.
        *   `dynamic_tools_manager.py`: Manages loading API tool configurations from the database, matching tools based on keywords, and executing API calls.
        *   `dynamic_agent_core.py`: Defines the `DynamicCustomFunctionCallingAgent` which uses an LLM to extract parameters from a query and then calls the matched tool.
        *   `agent_service.py`: The service layer that orchestrates the agent's decision-making process.
    *   `src/app/`: Contains FastAPI-related modules, including routing and authentication.
        *   `api_tools_endpoints.py`: Defines all API endpoints for the visual API Tool configuration UI.
        *   `auth.py`: User authentication logic (JWT-based).
    *   `src/db_manager.py`: A class for managing database interactions (for users and API tools).
    *   `src/voice_recognition.py`, `src/tts_engine.py`: Modules for handling speech-to-text and text-to-speech functionalities.

*   `static/`: Contains all frontend assets (HTML, CSS, JS).
    *   `api_tools_config.html`: The web interface for managing the dynamic API tools.
    *   `index.html`: The main chat interface for the application.

*   `Private_GPT/`: A directory likely used for data ingestion, containing source documents and the local Qdrant database path.

## 4. Development Conventions

*   **Configuration over Code:** The system heavily relies on the `config.ini` file. New features and integrations (like different LLMs, databases) are designed to be configurable here.
*   **Modularity:** The code is well-structured into modules with clear responsibilities (e.g., `agent`, `app`, `db_manager`).
*   **Dynamic Tooling:** The dynamic API tool system is a core architectural pattern. Instead of hardcoding tools, the system is designed to be extended at runtime through a database and a UI.
*   **Asynchronous Operations:** The codebase makes extensive use of `async` and `await` for non-blocking I/O, which is essential for a responsive web application handling LLM streams and API calls.
*   **Logging:** The application is configured with structured logging to different files (`rag_system.log`, `langchain_agent.log`), which is crucial for debugging the complex interactions between components.
*   **Stream-First Responses:** All primary LLM interactions are handled as asynchronous generators, streaming responses back to the client for a better user experience.
