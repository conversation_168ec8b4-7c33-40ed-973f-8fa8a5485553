<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>场景安全分析</title>
  <link rel="stylesheet" href="/static/css/style.css">
  <link rel="stylesheet" href="/static/css/input-fix.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <style>
    .scene-analysis-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
    }
    .scene-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      border-bottom: 1px solid #eaeaea;
    }
    .header-left {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    .header-right {
      display: flex;
      align-items: center;
      gap: 15px;
      position: relative;
    }
    .back-btn {
      background-color: transparent;
      border: none;
      color: #666;
      cursor: pointer;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 5px;
    }
    .back-btn:hover {
      color: #333;
    }
    .upload-btn-container {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .upload-button {
      background-color: #4e6ef2;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 14px;
    }
    .upload-button:hover {
      background-color: #3a5ae8;
    }
    .file-types {
      font-size: 12px;
      color: #888;
      margin-left: 10px;
    }
    .file-error {
      color: #ff5252;
      font-size: 14px;
      position: absolute;
      top: 55px;
      right: 20px;
      background-color: #ffeeee;
      padding: 5px 10px;
      border-radius: 4px;
      border: 1px solid #ffcccc;
      display: none;
      z-index: 1000;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .image-section {
      display: flex;
      align-items: center;
      position: relative;
    }
    .image-header {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      white-space: nowrap;
      background-color: #f5f5f5;
      padding: 5px 10px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    .image-content {
      display: none;
      position: absolute;
      top: 30px;
      right: 0;
      width: 300px;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 5px;
      background-color: white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      z-index: 100;
    }
    .image-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border-bottom: 1px solid #f0f0f0;
      background-color: #fff;
      transition: background-color 0.2s;
      cursor: pointer;
      position: relative;
    }
    .image-item:hover {
      background-color: #f9f9f9;
    }
    .image-item:hover::after {
      content: "点击查看原图";
      position: absolute;
      bottom: 5px;
      left: 70px;
      background-color: rgba(0,0,0,0.7);
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 12px;
      opacity: 0.9;
    }
    .image-item:last-child {
      border-bottom: none;
    }
    .image-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 13px;
      color: #333;
    }
    .image-thumbnail {
      width: 60px;
      height: 60px;
      object-fit: cover;
      border-radius: 4px;
      margin-right: 10px;
      border: 1px solid #eaeaea;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .remove-image-btn {
      background: none;
      border: none;
      color: #ff5252;
      cursor: pointer;
      font-size: 16px;
      padding: 0 5px;
    }
    .image-toggle-button {
      background: none;
      border: none;
      font-size: 14px;
      padding: 0 5px;
      margin-left: 5px;
      color: #666;
    }
    .image-toggle-button.expanded {
      color: #4e6ef2;
    }
    .welcome-text {
      font-size: 14px;
      font-weight: 500;
    }
    .scene-selection {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-left: 20px;
    }
    .scene-label {
      font-size: 14px;
      font-weight: 500;
    }
    .scene-select {
      padding: 6px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      background-color: white;
    }
    .analyze-button {
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    .analyze-button:hover {
      background-color: #45a049;
    }
    .analyze-button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .preview-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      z-index: 1000;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
    .preview-image {
      max-width: 90%;
      max-height: 90%;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .close-preview {
      position: absolute;
      top: 20px;
      right: 20px;
      color: white;
      font-size: 30px;
      cursor: pointer;
      background: rgba(0, 0, 0, 0.5);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: background-color 0.3s;
    }
    .close-preview:hover {
      background-color: rgba(255, 0, 0, 0.5);
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="/static/images/yingji.png" alt="应急指挥产品" class="logo-icon">
          <span class="logo-title">应急指挥智能体</span>
        </div>
      </div>
      <button class="new-chat-btn" id="new-chat-btn">
        <i class="fas fa-plus"></i>
        <span>新建会话</span>
      </button>
      <div class="history-title"></div>
      <div class="history-list" id="history-list">
        <!-- 历史记录将通过JavaScript动态加载 -->
      </div>
    </div>

    <!-- 场景分析主内容区 -->
    <div class="main-content">
      <div class="scene-analysis-container">
        <div class="scene-header">
          <div class="header-left">
            <button class="back-btn" id="back-btn">
              <i class="fas fa-arrow-left"></i>
              <span>返回主页</span>
            </button>

            <div class="upload-btn-container">
              <button class="upload-button" id="upload-button" title="支持: .jpg, .jpeg, .png, .gif, .webp">
                <i class="fas fa-cloud-upload-alt"></i>
                <span>上传图片</span>
              </button>
              <input type="file" id="file-upload" accept="image/*" style="display: none;">
            </div>

            <div class="scene-selection">
              <span class="scene-label">选择场景:</span>
              <select class="scene-select" id="scene-select">
                <option value="">请选择场景</option>
                <option value="flood">防汛抗旱</option>
                <option value="safety">安全生产</option>
                <option value="fire">消防巡查</option>
              </select>
              <button class="analyze-button" id="analyze-button" disabled>开始分析</button>
            </div>

          </div>

          <div class="header-right">
            <span class="file-error" id="file-error"></span>
            <div class="image-section" id="image-section" style="display: none;">
              <div class="image-header" id="image-header">
                <span><i class="fas fa-image" style="margin-right: 5px;"></i>已上传图片 <small style="color: #666; font-size: 12px;">(点击查看原图)</small></span>
                <button class="image-toggle-button" id="image-toggle-button">▶</button>
              </div>
              <div class="image-content" id="image-content" style="display: block;">
                <!-- 已选择的图片将在这里显示 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 图片预览区域 -->
        <div class="preview-container" id="preview-container" style="display: none;">
          <img id="preview-image" class="preview-image" src="" alt="预览图片">
          <div class="close-preview" id="close-preview">×</div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-container">
          <div class="chat-history" id="chat-history">
            <!-- 聊天记录将通过JavaScript动态加载 -->
          </div>
          <div class="input-container" style="display: none;">
            <textarea class="message-input" id="message-input" placeholder="输入问题..." rows="1"></textarea>
            <button class="send-btn" id="send-btn">
              <i class="fas fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 初始化marked选项
      marked.setOptions({
        breaks: true,  // 支持GitHub风格的换行
        gfm: true      // 支持GitHub风格的Markdown
      });

      // 返回主页按钮
      document.getElementById('back-btn').addEventListener('click', function() {
        window.location.href = '/';
      });

      // 新建会话按钮
      document.getElementById('new-chat-btn').addEventListener('click', function() {
        clearChatHistory();
        clearUploadedImage();
        document.getElementById('scene-select').value = '';
        document.getElementById('analyze-button').disabled = true;
        document.getElementById('preview-container').style.display = 'none';
      });

      // 图片上传相关
      const uploadButton = document.getElementById('upload-button');
      const fileUpload = document.getElementById('file-upload');
      const imageSection = document.getElementById('image-section');
      const imageHeader = document.getElementById('image-header');
      const imageContent = document.getElementById('image-content');
      const imageToggleButton = document.getElementById('image-toggle-button');
      const fileError = document.getElementById('file-error');
      const analyzeButton = document.getElementById('analyze-button');
      const sceneSelect = document.getElementById('scene-select');
      const previewContainer = document.getElementById('preview-container');
      const previewImage = document.getElementById('preview-image');
      const closePreview = document.getElementById('close-preview');
      let uploadedImage = null; // 存储已上传的图片

      // 点击关闭按钮关闭预览
      closePreview.addEventListener('click', function() {
        previewContainer.style.display = 'none';
      });

      // 点击预览背景也可以关闭预览
      previewContainer.addEventListener('click', function(e) {
        if (e.target === previewContainer) {
          previewContainer.style.display = 'none';
        }
      });

      // 添加键盘支持，按ESC键关闭预览
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && previewContainer.style.display === 'flex') {
          previewContainer.style.display = 'none';
        }
      });

      // 点击上传按钮触发文件选择
      uploadButton.addEventListener('click', function() {
        fileUpload.click();
      });

      // 图片列表折叠/展开功能
      imageHeader.addEventListener('click', function() {
        if (imageContent.style.display === 'block') {
          imageContent.style.display = 'none';
          imageToggleButton.classList.remove('expanded');
          imageToggleButton.textContent = '▶';
        } else {
          imageContent.style.display = 'block';
          imageToggleButton.classList.add('expanded');
          imageToggleButton.textContent = '▼';
        }
      });

      // 文件选择处理
      fileUpload.addEventListener('change', function() {
        handleImageSelection(this.files);
        this.value = ''; // 清空文件输入，允许重复选择相同文件
      });

      // 场景选择变化时检查是否可以启用分析按钮
      sceneSelect.addEventListener('change', function() {
        checkAnalyzeButtonState();
      });

      // 分析按钮点击事件
      analyzeButton.addEventListener('click', function() {
        if (uploadedImage && sceneSelect.value) {
          analyzeScene();
        }
      });

      // 检查分析按钮状态
      function checkAnalyzeButtonState() {
        analyzeButton.disabled = !(uploadedImage && sceneSelect.value);
      }

      // 处理图片选择
      function handleImageSelection(files) {
        if (files.length === 0) return;

        // 只处理第一张图片
        const file = files[0];

        // 检查文件类型
        if (!file.type.startsWith('image/')) {
          showFileError('请上传图片文件');
          return;
        }

        // 清除之前的图片
        clearUploadedImage();

        // 保存新上传的图片
        uploadedImage = file;

        // 更新图片显示
        refreshImageDisplay();

        // 显示图片区域
        imageSection.style.display = 'block';
        // 默认展开图片列表
        imageContent.style.display = 'block';
        imageToggleButton.classList.add('expanded');
        imageToggleButton.textContent = '▼';

        // 设置预览图片但不显示
        previewImage.src = URL.createObjectURL(uploadedImage);
        // 不显示预览图片，只在右上角显示缩略图
        previewContainer.style.display = 'none';

        // 检查分析按钮状态
        checkAnalyzeButtonState();
      }

      // 显示文件错误
      function showFileError(message) {
        fileError.textContent = message;
        fileError.style.display = 'block';

        // 3秒后自动隐藏错误
        setTimeout(() => {
          fileError.style.display = 'none';
        }, 3000);

        // 点击错误消息可以关闭
        fileError.addEventListener('click', function() {
          this.style.display = 'none';
        });
      }

      // 添加图片到显示区域
      function refreshImageDisplay() {
        imageContent.innerHTML = '';

        if (uploadedImage) {
          const imageItem = document.createElement('div');
          imageItem.className = 'image-item';

          // 创建图片缩略图
          const thumbnail = document.createElement('img');
          thumbnail.className = 'image-thumbnail';
          thumbnail.src = URL.createObjectURL(uploadedImage);

          // 添加点击缩略图显示原图的功能
          thumbnail.addEventListener('click', function(e) {
            e.stopPropagation(); // 防止点击事件传递
            showFullImage();
          });

          // 创建图片名称
          const imageName = document.createElement('div');
          imageName.className = 'image-name';
          imageName.textContent = uploadedImage.name;
          imageName.title = uploadedImage.name;

          // 添加点击图片名称也可以显示原图
          imageName.addEventListener('click', function(e) {
            e.stopPropagation(); // 防止点击事件传递
            showFullImage();
          });

          // 创建删除按钮
          const removeBtn = document.createElement('button');
          removeBtn.className = 'remove-image-btn';
          removeBtn.textContent = '×';
          removeBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 防止点击事件传递
            clearUploadedImage();
            previewContainer.style.display = 'none';
            checkAnalyzeButtonState();
          });

          // 为整个图片项添加点击事件
          imageItem.addEventListener('click', function(e) {
            // 如果点击的不是删除按钮，则显示原图
            if (!e.target.classList.contains('remove-image-btn')) {
              showFullImage();
            }
          });

          // 组装元素
          imageItem.appendChild(thumbnail);
          imageItem.appendChild(imageName);
          imageItem.appendChild(removeBtn);
          imageContent.appendChild(imageItem);
        }
      }

      // 显示全尺寸图片
      function showFullImage() {
        if (uploadedImage) {
          previewImage.src = URL.createObjectURL(uploadedImage);
          previewContainer.style.display = 'flex';
        }
      }

      // 清空已上传的图片
      function clearUploadedImage() {
        uploadedImage = null;
        imageContent.innerHTML = '';
        imageSection.style.display = 'none';
        analyzeButton.disabled = true;
      }

      // 清空聊天记录
      function clearChatHistory() {
        const chatHistory = document.getElementById('chat-history');
        chatHistory.innerHTML = '';
      }

      // 场景分析函数
      function analyzeScene() {
        const scene = sceneSelect.value;
        const chatHistory = document.getElementById('chat-history');

        // 清空之前的聊天记录
        clearChatHistory();

        // 添加用户消息
        const userMsg = document.createElement('div');
        userMsg.className = 'message user-message';

        // 创建用户头像
        const userAvatar = document.createElement('div');
        userAvatar.className = 'message-avatar';
        userAvatar.innerHTML = '<i class="fas fa-user"></i>';

        // 创建用户消息内容
        const userContent = document.createElement('div');
        userContent.className = 'message-content';

        let sceneText = '';
        switch(scene) {
          case 'flood':
            sceneText = '请分析这张图片中的防汛抗旱安全隐患';
            break;
          case 'safety':
            sceneText = '请分析这张图片中的安全生产隐患';
            break;
          case 'fire':
            sceneText = '请分析这张图片中的消防安全隐患';
            break;
        }

        userContent.textContent = sceneText;

        // 组装用户消息
        userMsg.appendChild(userAvatar);
        userMsg.appendChild(userContent);
        chatHistory.appendChild(userMsg);

        // 添加AI消息占位符
        const aiMsg = document.createElement('div');
        aiMsg.className = 'message ai-message';

        // 创建消息内容容器
        const contentContainer = document.createElement('div');
        contentContainer.className = 'message-content-container';

        // 创建思考过程区域
        const thinkingDiv = document.createElement('div');
        thinkingDiv.className = 'thinking-process collapsible';
        thinkingDiv.innerHTML = `
          <div class="collapsible-header">
            <span>思考过程</span>
            <button class="toggle-button">▶</button>
          </div>
          <div class="collapsible-content">
            <div class="thinking-content">思考中...</div>
          </div>
        `;

        // 创建正文内容区域
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = `
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        `;

        // 添加头像和内容容器
        aiMsg.innerHTML = `
          <div class="message-avatar">
            <img src="/static/images/yingji.png" alt="AI">
          </div>
        `;

        // 按顺序添加元素：思考过程 -> 消息内容
        contentContainer.appendChild(thinkingDiv);
        contentContainer.appendChild(messageContent);

        // 将内容容器添加到AI消息中
        aiMsg.appendChild(contentContainer);

        // 将AI消息添加到聊天历史
        chatHistory.appendChild(aiMsg);
        chatHistory.scrollTop = chatHistory.scrollHeight;

        // 设置思考过程的可折叠功能
        thinkingDiv.querySelector('.collapsible-header').addEventListener('click', function() {
          const content = this.nextElementSibling;
          const toggleButton = this.querySelector('.toggle-button');

          if (content.style.display === 'block') {
            content.style.display = 'none';
            toggleButton.classList.remove('expanded');
          } else {
            content.style.display = 'block';
            toggleButton.classList.add('expanded');
          }
        });

        // 调用API获取回答
        analyzeImageWithScene(scene, aiMsg);
      }

      // 调用图片分析API（带场景）
      async function analyzeImageWithScene(scene, aiMsgElement) {
        // 记录开始时间，用于计算思考时间
        const startTime = new Date();

        try {
          // 准备上传图片
          const formData = new FormData();
          formData.append('image', uploadedImage);
          formData.append('scene', scene);

          // 发送分析请求
          const response = await fetch('/analyze_scene/', {
            method: 'POST',
            body: formData
          });

          if (!response.ok) {
            throw new Error('网络请求失败');
          }

          // 读取流式响应
          const reader = response.body.getReader();
          const decoder = new TextDecoder();
          let result = '';

          // 获取消息内容元素和思考内容元素
          const messageContent = aiMsgElement.querySelector('.message-content');
          const thinkingContent = aiMsgElement.querySelector('.thinking-content');
          const thinkingDiv = aiMsgElement.querySelector('.thinking-process');

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const text = decoder.decode(value);
            result += text;

            // 检查是否包含思考过程
            const thinkMatch = result.match(/<think>([\s\S]*?)<\/think>/);
            if (thinkMatch) {
              const thinking = thinkMatch[1].trim();
              // 更新思考过程内容
              thinkingContent.innerHTML = marked.parse(thinking);
              // 确保完全移除思考过程标记及其内容
              result = result.replace(/<think>[\s\S]*?<\/think>/, '').trim();
            }

            // 更新AI消息内容
            messageContent.innerHTML = marked.parse(result);

            // 滚动到底部
            const chatHistory = document.getElementById('chat-history');
            chatHistory.scrollTop = chatHistory.scrollHeight;
          }

          // 记录结束时间，计算思考时间
          const endTime = new Date();
          const thinkingTime = Math.round((endTime - startTime) / 1000);

          // 更新思考过程标题，显示思考时间
          const thinkingHeader = thinkingDiv.querySelector('.collapsible-header span:first-child');
          if (thinkingHeader) {
            thinkingHeader.textContent = `思考了 ${thinkingTime}s`;
          }

        } catch (error) {
          console.error('获取回答时出错:', error);
          aiMsgElement.querySelector('.message-content').textContent = '获取回答时出错，请稍后重试';
        }
      }
    });
  </script>
</body>
</html>
