#!/usr/bin/env python3
"""
TTS依赖库导入调试脚本
用于检测Linux环境中具体的导入失败原因
"""

print("=== TTS依赖库导入测试 ===\n")

# 测试基础音频处理库
print("1. 测试基础音频处理库:")
try:
    import soundfile as sf
    print("✅ soundfile 导入成功")
    print(f"   版本: {sf.__version__ if hasattr(sf, '__version__') else '未知'}")
except ImportError as e:
    print(f"❌ soundfile 导入失败: {e}")

try:
    import numpy as np
    print("✅ numpy 导入成功")
    print(f"   版本: {np.__version__}")
except ImportError as e:
    print(f"❌ numpy 导入失败: {e}")

try:
    import torch
    print("✅ torch 导入成功")
    print(f"   版本: {torch.__version__}")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    print(f"   设备数量: {torch.cuda.device_count() if torch.cuda.is_available() else 'N/A'}")
except ImportError as e:
    print(f"❌ torch 导入失败: {e}")
except Exception as e:
    print(f"⚠️ torch 导入成功但初始化异常: {e}")

print("\n2. 测试TTS核心库:")
try:
    from kokoro import KModel, KPipeline
    print("✅ kokoro (KModel, KPipeline) 导入成功")
except ImportError as e:
    print(f"❌ kokoro 导入失败: {e}")

try:
    from misaki.zh import ZHG2P as Misaki
    print("✅ misaki.zh.ZHG2P 导入成功")
except ImportError as e:
    print(f"❌ misaki.zh.ZHG2P 导入失败: {e}")

print("\n3. 测试完整TTS引擎导入:")
try:
    import sys
    import os
    
    # 添加当前目录到路径
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    # 测试TTS引擎导入
    from src.tts_engine import TTSEngine, AUDIO_LIBS_AVAILABLE, TTS_LIBS_AVAILABLE
    
    print(f"✅ TTS引擎模块导入成功")
    print(f"   AUDIO_LIBS_AVAILABLE: {AUDIO_LIBS_AVAILABLE}")
    print(f"   TTS_LIBS_AVAILABLE: {TTS_LIBS_AVAILABLE}")
    
    # 尝试创建TTS引擎实例
    engine = TTSEngine()
    print("✅ TTS引擎实例创建成功")
    
except ImportError as e:
    print(f"❌ TTS引擎导入失败: {e}")
except Exception as e:
    print(f"⚠️ TTS引擎导入成功但实例化异常: {e}")

print("\n4. 系统信息:")
import platform
print(f"   操作系统: {platform.system()} {platform.release()}")
print(f"   Python版本: {platform.python_version()}")
print(f"   架构: {platform.machine()}")

print("\n=== 测试完成 ===")