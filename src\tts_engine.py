"""
TTS引擎模块 - 基于Kokoro TTS
适配主程序的配置和路径结构
"""
import os
import asyncio
import tempfile
import logging
import configparser
from typing import Dict, List, Optional, Tuple
from pathlib import Path
try:
    import soundfile as sf
    import numpy as np
    import torch
    AUDIO_LIBS_AVAILABLE = True
except ImportError:
    AUDIO_LIBS_AVAILABLE = False
    # 提供模拟实现
    class sf:
        @staticmethod
        def write(filename, data, samplerate):
            pass

    class np:
        @staticmethod
        def array(data):
            return data

        @staticmethod
        def concatenate(arrays):
            return arrays[0] if arrays else []

        @staticmethod
        def random():
            class Random:
                @staticmethod
                def randn(size):
                    return [0.0] * size
            return Random()

    class torch:
        @staticmethod
        def load(path, map_location=None):
            return "mock_tensor"

try:
    from kokoro import KModel, KPipeline
    from misaki.zh import ZHG2P as Misaki  # 使用正确的中文处理器
    TTS_LIBS_AVAILABLE = True
except ImportError:
    TTS_LIBS_AVAILABLE = False
    # 如果导入失败，提供模拟实现用于开发测试
    class KModel:
        def __init__(self, *args, **kwargs):
            pass

    class KPipeline:
        def __init__(self, *args, **kwargs):
            pass

        def __call__(self, *args, **kwargs):
            # 返回模拟的音频数据
            return [type('MockResult', (), {'audio': np.random().randn(24000)})]

    class Misaki:
        def __init__(self, *args, **kwargs):
            pass

        def __call__(self, *args, **kwargs):
            # 返回处理后的中文文本（模拟）
            return ("ni hao", None)

logger = logging.getLogger(__name__)


class Voice:
    """语音模型"""
    def __init__(self, name: str, display_name: str, language: str, gender: str, description: Optional[str] = None):
        self.name = name
        self.display_name = display_name
        self.language = language
        self.gender = gender
        self.description = description


class TTSConfig:
    """TTS配置类"""
    def __init__(self, config_file: str = "config.ini"):
        self.config = configparser.ConfigParser()
        self.config.read(config_file, encoding='utf-8')
        
        # 从配置文件读取TTS配置
        if self.config.has_section('TTS'):
            # 模型配置
            self.model_name = self.config.get('TTS', 'MODEL_NAME', fallback='hexgrad/Kokoro-82M-v1.1-zh')
            self.model_path = self.config.get('TTS', 'MODEL_PATH', fallback='Private_GPT/sentence-transformers/kokoro-tts-v1.1-zh/kokoro-v1_1-zh.pth')
            self.voices_dir = self.config.get('TTS', 'VOICES_DIR', fallback='Private_GPT/sentence-transformers/kokoro-tts-v1.1-zh/voices')

            # 音频配置
            self.sample_rate = self.config.getint('TTS', 'SAMPLE_RATE', fallback=24000)
            self.audio_format = self.config.get('TTS', 'AUDIO_FORMAT', fallback='wav')
            self.max_text_length = self.config.getint('TTS', 'MAX_TEXT_LENGTH', fallback=1000)
        else:
            # 使用默认配置
            self.model_name = 'hexgrad/Kokoro-82M-v1.1-zh'
            self.model_path = 'Private_GPT/sentence-transformers/kokoro-tts-v1.1-zh/kokoro-v1_1-zh.pth'
            self.voices_dir = 'Private_GPT/sentence-transformers/kokoro-tts-v1.1-zh/voices'
            self.sample_rate = 24000
            self.audio_format = 'wav'
            self.max_text_length = 1000
        
        # 安全配置
        if self.config.has_section('TTS'):
            self.max_concurrent_requests = self.config.getint('TTS', 'MAX_CONCURRENT_REQUESTS', fallback=10)
            self.request_timeout = self.config.getint('TTS', 'REQUEST_TIMEOUT', fallback=30)
            # 临时文件配置
            self.temp_dir = self.config.get('TTS', 'TEMP_DIR', fallback='./temp_files')
            self.cleanup_interval = self.config.getint('TTS', 'CLEANUP_INTERVAL', fallback=3600)
        else:
            self.max_concurrent_requests = 10
            self.request_timeout = 30
            self.temp_dir = './temp_files'
            self.cleanup_interval = 3600


class TTSEngine:
    """TTS引擎类"""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config = TTSConfig(config_file)
        self.model: Optional[KModel] = None
        self.pipeline: Optional[KPipeline] = None
        self.misaki: Optional[Misaki] = None
        self.is_loaded = False
        self._voices_cache: Optional[List[Voice]] = None
        
    async def initialize(self) -> bool:
        """初始化TTS引擎"""
        try:
            logger.info("正在初始化TTS引擎...")

            # 检查必要的库是否可用
            if not AUDIO_LIBS_AVAILABLE:
                logger.warning("音频处理库不可用，TTS功能将使用模拟模式")

            if not TTS_LIBS_AVAILABLE:
                logger.warning("TTS库不可用，TTS功能将使用模拟模式")

            # 创建必要的目录
            os.makedirs(self.config.temp_dir, exist_ok=True)

            # 在线程池中加载模型，避免阻塞事件循环
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._load_models)

            self.is_loaded = True
            logger.info("TTS引擎初始化完成")
            return True

        except Exception as e:
            logger.error(f"TTS引擎初始化失败: {e}")
            return False
    
    def _load_models(self):
        """加载模型（在线程池中执行）"""
        try:
            # 如果库不可用，使用模拟模式
            if not TTS_LIBS_AVAILABLE or not AUDIO_LIBS_AVAILABLE:
                logger.info("使用模拟模式初始化TTS组件")
                self.model = KModel()
                self.misaki = Misaki()
                self.pipeline = KPipeline()
                self._load_voices()
                return

            # 检查本地模型文件是否存在
            if not os.path.exists(self.config.model_path):
                logger.error(f"本地模型文件不存在: {self.config.model_path}")
                # 如果本地文件不存在，回退到从HuggingFace加载
                logger.info("尝试从HuggingFace加载模型...")
                self.model = KModel(repo_id=self.config.model_name)
            else:
                # 使用本地模型文件
                logger.info(f"从本地加载模型: {self.config.model_path}")
                self.model = KModel(model=self.config.model_path)

            # 创建推理管道，传入语言代码和模型
            self.pipeline = KPipeline(
                lang_code="zh",
                model=self.model
            )

            # 加载中文文本处理器
            self.misaki = Misaki()

            # 加载语音模型
            self._load_voices()

            logger.info("模型加载完成")

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise

    def _load_voices(self):
        """加载语音模型"""
        try:
            self.voices = {}

            # 如果库不可用，使用模拟语音列表
            if not TTS_LIBS_AVAILABLE or not AUDIO_LIBS_AVAILABLE:
                self.voices = {
                    "zf_001": {"name": "中文女声1", "language": "zh", "gender": "female"},
                    "zf_002": {"name": "中文女声2", "language": "zh", "gender": "female"},
                    "zm_009": {"name": "中文男声1", "language": "zh", "gender": "male"},
                    "zm_010": {"name": "中文男声2", "language": "zh", "gender": "male"},
                }
                logger.info("使用模拟语音列表")
                return

            # 扫描语音目录
            if os.path.exists(self.config.voices_dir):
                for voice_file in os.listdir(self.config.voices_dir):
                    if voice_file.endswith('.pt'):
                        voice_name = voice_file.replace('.pt', '')
                        voice_path = os.path.join(self.config.voices_dir, voice_file)

                        # 根据文件名推断语音信息
                        gender = "female" if voice_name.startswith(('zf_', 'af_')) else "male"
                        language = "zh" if voice_name.startswith(('zf_', 'zm_')) else "en"

                        self.voices[voice_name] = {
                            "name": voice_name,
                            "path": voice_path,
                            "language": language,
                            "gender": gender
                        }

                logger.info(f"加载了 {len(self.voices)} 个语音模型")
            else:
                logger.warning(f"语音目录不存在: {self.config.voices_dir}")

        except Exception as e:
            logger.error(f"加载语音模型失败: {e}")
            self.voices = {}

    async def synthesize(
        self,
        text: str,
        voice: str = "zf_001",  # 使用实际存在的中文女声作为默认
        speed: float = 1.0,
        format: str = "wav"
    ) -> Tuple[str, Dict]:
        """
        合成语音

        Args:
            text: 要合成的文本
            voice: 语音名称
            speed: 语速倍率
            format: 音频格式

        Returns:
            Tuple[音频文件路径, 元数据]
        """
        if not self.is_loaded:
            raise RuntimeError("TTS引擎未初始化")

        try:
            # 在线程池中执行TTS合成
            loop = asyncio.get_event_loop()
            audio_data, sample_rate, metadata = await loop.run_in_executor(
                None, self._synthesize_audio, text, voice, speed
            )

            # 保存音频文件
            temp_file = await self._save_audio(audio_data, sample_rate, format)

            # 更新元数据
            metadata.update({
                "file_path": temp_file,
                "format": format,
                "sample_rate": sample_rate,
                "duration": len(audio_data) / sample_rate,
                "file_size": os.path.getsize(temp_file)
            })

            return temp_file, metadata

        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            raise

    def _synthesize_audio(
        self,
        text: str,
        voice: str,
        speed: float
    ) -> Tuple[np.ndarray, int, Dict]:
        """执行音频合成（在线程池中执行）"""
        try:
            # 如果库不可用，返回模拟音频数据
            if not TTS_LIBS_AVAILABLE or not AUDIO_LIBS_AVAILABLE:
                logger.info(f"模拟模式：合成文本 '{text}' 使用语音 '{voice}'")
                # 生成模拟音频数据（1秒的静音）
                sample_rate = 24000
                duration = max(1.0, len(text) * 0.1)  # 根据文本长度估算时长
                audio_data = np.array([0.0] * int(sample_rate * duration))

                metadata = {
                    "text": text,
                    "voice": voice,
                    "speed": speed,
                    "mode": "simulation"
                }

                return audio_data, sample_rate, metadata

            # 预处理中文文本
            if self._is_chinese_text(text):
                # ZHG2P返回元组(phonetic_text, tones)，我们需要第一个元素
                result = self.misaki(text)
                processed_text = result[0] if isinstance(result, tuple) else result
            else:
                processed_text = text

            # 加载本地语音张量文件
            voice_file_path = os.path.join(self.config.voices_dir, f"{voice}.pt")

            if not os.path.exists(voice_file_path):
                logger.error(f"本地语音文件不存在: {voice_file_path}")
                # 如果本地文件不存在，回退到使用字符串名称
                logger.warning(f"回退到使用字符串语音名称: {voice}")
                voice_tensor = voice
            else:
                # 加载语音张量文件
                logger.info(f"正在加载语音文件: {voice_file_path}")
                voice_tensor = torch.load(voice_file_path, map_location="cpu")
                logger.info(f"语音文件加载成功，类型: {type(voice_tensor)}")

            # 使用Kokoro进行语音合成
            # KPipeline返回生成器，需要收集所有结果
            results = list(self.pipeline(
                processed_text,
                voice=voice_tensor,  # 传入加载好的voice_tensor或字符串名称
                speed=speed
            ))

            # 合并所有音频片段
            if results:
                # Kokoro的默认采样率是24000Hz
                sample_rate = 24000
                audio_segments = [result.audio.numpy() if hasattr(result.audio, 'numpy') else result.audio
                                  for result in results]
                audio_data = np.concatenate(audio_segments) if len(audio_segments) > 1 else audio_segments[0]
            else:
                # 如果没有结果，返回空音频
                sample_rate = self.config.sample_rate
                audio_data = np.array([])

            # 确保音频数据是numpy数组
            if not isinstance(audio_data, np.ndarray):
                audio_data = np.array(audio_data)

            metadata = {
                "original_text": text,
                "processed_text": processed_text,
                "voice": voice,
                "voice_file": voice_file_path if os.path.exists(voice_file_path) else "fallback",
                "speed": speed
            }

            return audio_data, sample_rate, metadata

        except Exception as e:
            logger.error(f"音频合成执行失败: {e}")
            raise

    async def _save_audio(
        self,
        audio_data: np.ndarray,
        sample_rate: int,
        format: str
    ) -> str:
        """保存音频文件"""
        try:
            # 创建临时文件
            suffix = f".{format}"
            temp_file = tempfile.NamedTemporaryFile(
                delete=False,
                suffix=suffix,
                dir=self.config.temp_dir
            )
            temp_file.close()

            # 在线程池中保存文件
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                sf.write,
                temp_file.name,
                audio_data,
                sample_rate
            )

            return temp_file.name

        except Exception as e:
            logger.error(f"音频文件保存失败: {e}")
            raise

    def _is_chinese_text(self, text: str) -> bool:
        """检查文本是否包含中文"""
        for char in text:
            if '\u4e00' <= char <= '\u9fff':
                return True
        return False

    async def get_available_voices(self) -> List[Voice]:
        """获取可用语音列表"""
        if self._voices_cache is not None:
            return self._voices_cache

        # 定义可用的语音
        voices = [
            # 中文女声 (基于实际的.pt文件)
            Voice(
                name="zf_001",
                display_name="中文女声-001",
                language="zh",
                gender="female",
                description="高质量中文女声-001"
            ),
            Voice(
                name="zf_002",
                display_name="中文女声-002",
                language="zh",
                gender="female",
                description="高质量中文女声-002"
            ),
            Voice(
                name="zf_003",
                display_name="中文女声-003",
                language="zh",
                gender="female",
                description="高质量中文女声-003"
            ),
            Voice(
                name="zf_004",
                display_name="中文女声-004",
                language="zh",
                gender="female",
                description="高质量中文女声-004"
            ),
            Voice(
                name="zf_005",
                display_name="中文女声-005",
                language="zh",
                gender="female",
                description="高质量中文女声-005"
            ),
            # 中文男声
            Voice(
                name="zm_009",
                display_name="中文男声-009",
                language="zh",
                gender="male",
                description="高质量中文男声-009"
            ),
            Voice(
                name="zm_010",
                display_name="中文男声-010",
                language="zh",
                gender="male",
                description="高质量中文男声-010"
            ),
            Voice(
                name="zm_011",
                display_name="中文男声-011",
                language="zh",
                gender="male",
                description="高质量中文男声-011"
            ),
            Voice(
                name="zm_012",
                display_name="中文男声-012",
                language="zh",
                gender="male",
                description="高质量中文男声-012"
            ),
            # 英文语音 (高质量)
            Voice(
                name="af_maple",
                display_name="英文女声-Maple",
                language="en",
                gender="female",
                description="高质量英文女声-Maple"
            ),
            Voice(
                name="af_sol",
                display_name="英文女声-Sol",
                language="en",
                gender="female",
                description="高质量英文女声-Sol"
            ),
            Voice(
                name="bf_vale",
                display_name="英文女声-Vale (英式)",
                language="en",
                gender="female",
                description="高质量英式女声-Vale"
            )
        ]

        self._voices_cache = voices
        return voices

    async def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_dir = Path(self.config.temp_dir)
            if temp_dir.exists():
                for file_path in temp_dir.glob("*.wav"):
                    try:
                        # 删除超过1小时的文件
                        if (asyncio.get_event_loop().time() - file_path.stat().st_mtime) > 3600:
                            file_path.unlink()
                    except Exception as e:
                        logger.warning(f"删除临时文件失败 {file_path}: {e}")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")


# 全局TTS引擎实例
tts_engine = TTSEngine()
