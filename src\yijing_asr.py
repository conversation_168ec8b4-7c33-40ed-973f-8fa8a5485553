"""
应急语音识别（ASR）服务模块
实现文件转写、实时转写等功能
"""
import requests
import websocket
import json
import time
import threading
import logging
import configparser
from typing import Optional, Dict, List, Callable, Any
import io
import struct
from fastapi import UploadFile
import asyncio
import aiohttp
import ssl
import traceback
import time

logger = logging.getLogger(__name__)

class YijingASRService:
    """应急语音识别服务类"""
    
    def __init__(self, config: configparser.ConfigParser):
        """初始化ASR服务"""
        self.config = config
        self.is_enabled = self._load_config()
        self.active_tasks = {}  # 存储活跃的任务
        
    def _load_config(self) -> bool:
        """加载配置"""
        try:
            if not self.config.has_section('YJY_SPEECH_RECOGNITION'):
                logger.warning("配置文件中未找到YJY_SPEECH_RECOGNITION段")
                return False
                
            self.host = self.config.get('YJY_SPEECH_RECOGNITION', 'HOST', fallback='*************')
            self.http_port = self.config.getint('YJY_SPEECH_RECOGNITION', 'HTTP_PORT', fallback=8250)
            self.https_port = self.config.getint('YJY_SPEECH_RECOGNITION', 'HTTPS_PORT', fallback=443)
            self.protocol = self.config.get('YJY_SPEECH_RECOGNITION', 'PROTOCOL', fallback='http')
            self.context_path = self.config.get('YJY_SPEECH_RECOGNITION', 'CONTEXT_PATH', fallback='/xmessagemas-cms')
            self.token = self.config.get('YJY_SPEECH_RECOGNITION', 'TOKEN', fallback='')
            self.enable_asr = self.config.getboolean('YJY_SPEECH_RECOGNITION', 'ENABLE_ASR', fallback=True)
            self.request_timeout = self.config.getint('YJY_SPEECH_RECOGNITION', 'REQUEST_TIMEOUT', fallback=60)
            self.max_audio_size_mb = self.config.getint('YJY_SPEECH_RECOGNITION', 'MAX_AUDIO_SIZE_MB', fallback=50)
            self.supported_formats = self.config.get('YJY_SPEECH_RECOGNITION', 'SUPPORTED_AUDIO_FORMATS', fallback='pcm').split(',')
            self.ws_timeout = self.config.getint('YJY_SPEECH_RECOGNITION', 'WS_TIMEOUT', fallback=300)
            self.audio_send_interval = self.config.getint('YJY_SPEECH_RECOGNITION', 'AUDIO_SEND_INTERVAL', fallback=400)
            self.audio_chunk_size = self.config.getint('YJY_SPEECH_RECOGNITION', 'AUDIO_CHUNK_SIZE', fallback=12800)
            
            # 构建基础URL
            port = self.https_port if self.protocol == 'https' else self.http_port
            self.base_url = f"{self.protocol}://{self.host}:{port}{self.context_path}"
            
            logger.info(f"ASR服务配置加载成功: {self.base_url}")
            return self.enable_asr
            
        except Exception as e:
            logger.error(f"ASR服务配置加载失败: {str(e)}")
            return False
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            'Content-Type': 'application/octet-stream'
        }
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        return headers
    
    async def file_transfer(self, audio_file: UploadFile) -> Dict[str, Any]:
        """
        文件转写功能
        上传音频文件进行转写
        """
        if not self.is_enabled:
            return {
                "code": "0x0190001",
                "msg": "ASR服务未启用",
                "data": ""
            }
        
        try:
            logger.info(f"开始文件转写: {audio_file.filename}")
            
            # 检查文件格式
            if not audio_file.filename.lower().endswith('.pcm'):
                logger.warning(f"不支持的文件格式: {audio_file.filename}")
                return {
                    "code": "0x0190002",
                    "msg": "不支持的音频格式，目前只支持PCM格式",
                    "data": ""
                }
            
            # 检查文件大小
            content = await audio_file.read()
            file_size_mb = len(content) / 1024 / 1024
            logger.info(f"文件大小: {file_size_mb:.2f}MB")
            
            if len(content) > self.max_audio_size_mb * 1024 * 1024:
                logger.warning(f"文件大小超过限制: {file_size_mb:.2f}MB > {self.max_audio_size_mb}MB")
                return {
                    "code": "0x0190003",
                    "msg": f"文件大小超过限制({self.max_audio_size_mb}MB)",
                    "data": ""
                }
            
            # 构建请求URL
            url = f"{self.base_url}/api/v1/asr/file/transfer"
            logger.info(f"请求URL: {url}")
            
            # 准备文件数据 - 使用aiohttp.FormData
            form_data = aiohttp.FormData()
            form_data.add_field(
                'uploadFile', 
                content, 
                filename=audio_file.filename, 
                content_type='application/octet-stream'
            )
            
            # 准备请求头
            headers = {}
            if self.token:
                headers['Token'] = self.token
                logger.info(f"使用Token认证: {self.token[:20]}...")
            else:
                logger.warning("未配置Token，使用匿名访问")
            
            # 根据文件大小动态调整超时时间
            # 按照文档说明：每一秒音频转写约需要一秒
            # PCM 16bit 16kHz 单声道，每秒约32KB，所以可以估算音频时长
            estimated_duration_seconds = len(content) / (16000 * 2)  # 16kHz * 2字节/样本
            # 超时时间 = 基础超时 + 估算音频时长 * 3（留出余量）+ 60秒网络延迟
            dynamic_timeout = max(120, int(estimated_duration_seconds * 3 + 60))
            logger.info(f"估算音频时长: {estimated_duration_seconds:.1f}秒, 设置超时时间: {dynamic_timeout}秒")
            
            # 发送请求
            start_time = time.time()
            logger.info(f"开始发送请求到ASR服务器...")
            
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(
                    total=dynamic_timeout,  # 使用动态超时时间
                    connect=30,  # 连接超时30秒
                    sock_read=240  # 读取超时240秒
                )
            ) as session:
                async with session.post(
                    url, 
                    data=form_data,
                    headers=headers,
                    ssl=False if self.protocol == 'http' else ssl.create_default_context()
                ) as response:
                    request_time = time.time() - start_time
                    logger.info(f"收到响应，状态码: {response.status}, 请求耗时: {request_time:.2f}秒")
                    
                    if response.status == 200:
                        try:
                            result = await response.json()
                            logger.info(f"文件转写成功: {audio_file.filename}, 总耗时: {request_time:.2f}秒")
                            logger.debug(f"转写结果: {result}")
                            return result
                        except Exception as json_error:
                            logger.error(f"解析响应JSON失败: {str(json_error)}")
                            response_text = await response.text()
                            logger.error(f"原始响应内容: {response_text[:500]}...")
                            return {
                                "code": "0x0190009",
                                "msg": "响应格式错误",
                                "data": ""
                            }
                    else:
                        error_text = await response.text()
                        logger.error(f"文件转写失败: HTTP {response.status}")
                        logger.error(f"错误响应头: {dict(response.headers)}")
                        logger.error(f"错误响应内容: {error_text}")
                        
                        # 根据不同的HTTP状态码提供更具体的错误信息
                        if response.status == 404:
                            return {
                                "code": "0x0190004",
                                "msg": f"API接口不存在 (HTTP 404)，请检查服务器配置。URL: {url}",
                                "data": ""
                            }
                        elif response.status == 401:
                            return {
                                "code": "0x0190005",
                                "msg": "认证失败 (HTTP 401)，请检查Token配置",
                                "data": ""
                            }
                        elif response.status == 403:
                            return {
                                "code": "0x0190006",
                                "msg": "权限不足 (HTTP 403)，请检查Token权限",
                                "data": ""
                            }
                        elif response.status == 500:
                            return {
                                "code": "0x0190007",
                                "msg": "服务器内部错误 (HTTP 500)，请联系服务提供方",
                                "data": ""
                            }
                        else:
                            return {
                                "code": "0x0190008",
                                "msg": f"服务器错误: HTTP {response.status} - {error_text[:100]}",
                                "data": ""
                            }
                        
        except asyncio.TimeoutError:
            logger.error(f"文件转写超时，超时时间: {dynamic_timeout}秒")
            return {
                "code": "0x0190010",
                "msg": f"请求超时(超过{dynamic_timeout}秒)，请检查网络连接或联系服务提供方",
                "data": ""
            }
        except aiohttp.ClientConnectorError as e:
            logger.error(f"连接ASR服务器失败: {str(e)}")
            return {
                "code": "0x0190011",
                "msg": f"无法连接到ASR服务器: {str(e)}",
                "data": ""
            }
        except Exception as e:
            logger.error(f"文件转写异常: {str(e)}")
            logger.error(f"异常详情: {traceback.format_exc()}")
            return {
                "code": "0x0190012",
                "msg": f"转写失败: {str(e)}",
                "data": ""
            }
    
    async def end_task(self, task_id: str) -> Dict[str, Any]:
        """
        结束转写任务
        """
        if not self.is_enabled:
            return {
                "code": "0x0190001",
                "msg": "ASR服务未启用",
                "data": ""
            }
        
        try:
            url = f"{self.base_url}/api/v1/asr/end"
            data = {"taskId": task_id}
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.request_timeout)) as session:
                headers = {'Content-Type': 'application/json'}
                if self.token:
                    headers['Token'] = self.token
                
                async with session.post(
                    url,
                    json=data,
                    headers=headers,
                    ssl=False if self.protocol == 'http' else ssl.create_default_context()
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        # 从活跃任务中移除
                        if task_id in self.active_tasks:
                            del self.active_tasks[task_id]
                        logger.info(f"任务结束成功: {task_id}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"结束任务失败: {response.status}, {error_text}")
                        return {
                            "code": "0x0190007",
                            "msg": f"服务器错误: {response.status}",
                            "data": ""
                        }
                        
        except Exception as e:
            logger.error(f"结束任务异常: {str(e)}")
            return {
                "code": "0x0190008",
                "msg": f"结束任务失败: {str(e)}",
                "data": ""
            }
    
    def get_websocket_url(self, endpoint: str, params: Dict[str, str] = None) -> str:
        """获取WebSocket连接URL"""
        protocol = 'wss' if self.protocol == 'https' else 'ws'
        port = self.https_port if self.protocol == 'https' else self.http_port
        base_ws_url = f"{protocol}://{self.host}:{port}{self.context_path}"
        
        url = f"{base_ws_url}/{endpoint}"
        if params:
            param_str = "&".join([f"{k}={v}" for k, v in params.items()])
            url = f"{url}?{param_str}"
        
        return url
    
    @staticmethod
    def build_protocol_message(speaker: str, pcm_data: bytes) -> bytes:
        """
        构建会议模式自定义协议消息
        格式：4字节json长度+json+4字节pcm长度+pcm
        """
        # 构建JSON数据
        json_obj = {"speaker": speaker}
        json_str = json.dumps(json_obj, ensure_ascii=False)
        json_data = json_str.encode('utf-8')
        
        # 构建消息
        # 4字节JSON长度（大端序）+ JSON数据 + 4字节PCM长度（大端序）+ PCM数据
        message = b''
        message += struct.pack('>I', len(json_data))  # JSON长度
        message += json_data  # JSON数据
        message += struct.pack('>I', len(pcm_data))  # PCM长度
        message += pcm_data  # PCM数据
        
        return message
    
    def create_realtime_session(self, 
                              mode: str = "single",  # single 或 meeting
                              creator: str = "admin",
                              speaker: str = "admin",
                              task_id: str = None,
                              message_callback: Callable = None) -> Dict[str, Any]:
        """
        创建实时转写会话
        
        Args:
            mode: 转写模式，"single"（单路）或"meeting"（会议模式）
            creator: 创建人
            speaker: 说话人（单路模式使用）
            task_id: 任务ID（可选，用于恢复连接）
            message_callback: 消息回调函数
        """
        if not self.is_enabled:
            return {
                "success": False,
                "message": "ASR服务未启用"
            }
        
        try:
            # 构建WebSocket URL
            if mode == "single":
                endpoint = "ws/audio/stream"
                params = {"creator": creator, "speaker": speaker}
                if task_id:
                    params["taskId"] = task_id
            else:  # meeting
                endpoint = "ws/audio/streams/protocol"
                params = {"creator": creator}
                if task_id:
                    params["taskId"] = task_id
            
            ws_url = self.get_websocket_url(endpoint, params)
            
            # 创建WebSocket连接会话信息
            session_info = {
                "url": ws_url,
                "mode": mode,
                "creator": creator,
                "speaker": speaker,
                "task_id": task_id,
                "message_callback": message_callback,
                "connected": False,
                "ws": None
            }
            
            return {
                "success": True,
                "session_info": session_info,
                "message": "会话创建成功"
            }
            
        except Exception as e:
            logger.error(f"创建实时转写会话失败: {str(e)}")
            return {
                "success": False,
                "message": f"创建会话失败: {str(e)}"
            }
    
    def get_active_tasks(self) -> List[str]:
        """获取活跃任务列表"""
        return list(self.active_tasks.keys())
    
    def get_task_info(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        return self.active_tasks.get(task_id)
    
    def is_service_available(self) -> bool:
        """检查服务是否可用"""
        if not self.is_enabled:
            return False
        
        try:
            # 简单的健康检查
            url = f"{self.base_url}/health"  # 假设有健康检查端点
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except Exception:
            # 如果没有健康检查端点，假设服务可用
            return True 